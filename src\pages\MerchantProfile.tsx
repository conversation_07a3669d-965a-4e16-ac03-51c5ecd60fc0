import React, { useState } from 'react';
import { 
  Store, 
  Edit, 
  Camera, 
  MapPin, 
  Calendar, 
  Star, 
  Award, 
  Users, 
  TrendingUp,
  Globe,
  Phone,
  Mail,
  Clock,
  Shield
} from 'lucide-react';


const MerchantProfile: React.FC = () => {
  const [isEditing, setIsEditing] = useState(false);
  const [profileData, setProfileData] = useState({
    businessName: 'Campus Tech Store',
    description: 'Your trusted partner for the latest technology and electronics on campus. We specialize in providing students with high-quality devices, accessories, and tech solutions at competitive prices. From laptops and smartphones to gaming gear and study accessories, we have everything you need to succeed in your academic journey.',
    businessType: 'Electronics Retailer',
    website: 'https://campustechstore.com',
    phone: '+****************',
    email: '<EMAIL>',
    address: '123 University Ave, Campus District, CA 94301',
    foundedYear: '2020',
    specialties: ['Electronics', 'Laptops', 'Smartphones', 'Gaming Gear', 'Accessories'],
    operatingHours: 'Mon-Fri: 9AM-8PM, Sat-Sun: 10AM-6PM'
  });

  const [profileImage] = useState('/placeholder-avatar.svg');
  const [coverImage] = useState('/placeholder-image.jpg');

  const stats = {
    totalProducts: 156,
    totalSales: 2847,
    averageRating: 4.8,
    totalReviews: 1247,
    responseRate: 98,
    joinedDate: 'January 2022',
    universities: 25,
    repeatCustomers: 67
  };

  const achievements = [
    { icon: Award, title: 'Top Merchant', description: 'Highest rated merchant this month' },
    { icon: TrendingUp, title: 'Fast Growing', description: '200% growth in the last quarter' },
    { icon: Shield, title: 'Verified Partner', description: 'Verified business partner since 2022' },
    { icon: Users, title: 'Student Favorite', description: 'Loved by 10,000+ students' }
  ];

  const handleSave = () => {
    setIsEditing(false);
    console.log('Profile updated:', profileData);
  };

  const handleImageUpload = (type: 'profile' | 'cover') => {
    // In a real app, this would handle file upload
    console.log(`Upload ${type} image`);
  };

  return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Cover & Profile Header */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden mb-8">
            {/* Cover Image */}
            <div className="h-48 bg-gradient-to-r from-accent-500 to-orange-500 relative">
              <img
                src={coverImage}
                alt="Cover"
                className="w-full h-full object-cover opacity-20"
              />
              {isEditing && (
                <button
                  onClick={() => handleImageUpload('cover')}
                  className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm text-gray-700 p-2 rounded-full hover:bg-white transition-colors"
                >
                  <Camera className="w-5 h-5" />
                </button>
              )}
            </div>
            
            {/* Profile Info */}
            <div className="relative px-6 pb-6">
              <div className="flex flex-col md:flex-row md:items-end md:space-x-6 -mt-16">
                <div className="relative">
                  <img
                    src={profileImage}
                    alt={profileData.businessName}
                    className="w-32 h-32 rounded-2xl object-cover border-4 border-white dark:border-gray-800 shadow-lg"
                  />
                  {isEditing && (
                    <button
                      onClick={() => handleImageUpload('profile')}
                      className="absolute -bottom-2 -right-2 bg-accent-500 text-white p-2 rounded-full hover:bg-accent-600 transition-colors"
                    >
                      <Camera className="w-4 h-4" />
                    </button>
                  )}
                  <div className="absolute -bottom-2 -left-2 w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center border-4 border-white dark:border-gray-800">
                    <Shield className="w-4 h-4 text-white" />
                  </div>
                </div>
                
                <div className="flex-1 mt-4 md:mt-0">
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                    <div>
                      {isEditing ? (
                        <input
                          type="text"
                          value={profileData.businessName}
                          onChange={(e) => setProfileData({ ...profileData, businessName: e.target.value })}
                          className="text-3xl font-bold text-gray-900 dark:text-white mb-2 bg-transparent border-b border-gray-300 dark:border-gray-600 focus:border-accent-500 outline-none"
                        />
                      ) : (
                        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                          {profileData.businessName}
                        </h1>
                      )}
                      <p className="text-gray-600 dark:text-gray-400 mb-2">{profileData.businessType}</p>
                      <div className="flex items-center space-x-4 mb-4">
                        <div className="flex items-center space-x-1">
                          <Star className="w-5 h-5 text-yellow-400 fill-current" />
                          <span className="font-semibold text-gray-900 dark:text-white">{stats.averageRating}</span>
                          <span className="text-gray-500 dark:text-gray-400">({stats.totalReviews} reviews)</span>
                        </div>
                        <div className="flex items-center space-x-1">
                          <MapPin className="w-4 h-4 text-gray-400" />
                          <span className="text-gray-600 dark:text-gray-400">Serving {stats.universities} universities</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-3">
                      {isEditing ? (
                        <>
                          <button
                            onClick={() => setIsEditing(false)}
                            className="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-xl font-semibold hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                          >
                            Cancel
                          </button>
                          <button
                            onClick={handleSave}
                            className="bg-accent-600 hover:bg-accent-700 text-white px-6 py-3 rounded-xl font-semibold transition-colors"
                          >
                            Save Changes
                          </button>
                        </>
                      ) : (
                        <button
                          onClick={() => setIsEditing(true)}
                          className="bg-accent-600 hover:bg-accent-700 text-white px-6 py-3 rounded-xl font-semibold transition-colors flex items-center space-x-2"
                        >
                          <Edit className="w-5 h-5" />
                          <span>Edit Profile</span>
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 text-center">
              <div className="text-3xl font-bold text-primary-600 dark:text-primary-400 mb-2">
                {stats.totalProducts}
              </div>
              <p className="text-gray-600 dark:text-gray-400">Total Products</p>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 text-center">
              <div className="text-3xl font-bold text-success-600 dark:text-success-400 mb-2">
                {stats.totalSales.toLocaleString()}
              </div>
              <p className="text-gray-600 dark:text-gray-400">Total Sales</p>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 text-center">
              <div className="text-3xl font-bold text-accent-600 dark:text-accent-400 mb-2">
                {stats.responseRate}%
              </div>
              <p className="text-gray-600 dark:text-gray-400">Response Rate</p>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 text-center">
              <div className="text-3xl font-bold text-purple-600 dark:text-purple-400 mb-2">
                {stats.repeatCustomers}%
              </div>
              <p className="text-gray-600 dark:text-gray-400">Repeat Customers</p>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Main Content */}
            <div className="lg:col-span-2 space-y-8">
              {/* About Section */}
              <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">About Our Business</h2>
                {isEditing ? (
                  <textarea
                    value={profileData.description}
                    onChange={(e) => setProfileData({ ...profileData, description: e.target.value })}
                    rows={6}
                    className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-accent-500 focus:border-transparent dark:bg-gray-700 dark:text-white resize-none"
                  />
                ) : (
                  <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                    {profileData.description}
                  </p>
                )}
              </div>

              {/* Specialties */}
              <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">Our Specialties</h3>
                <div className="flex flex-wrap gap-3">
                  {profileData.specialties.map((specialty, index) => (
                    <span
                      key={index}
                      className="px-4 py-2 bg-accent-100 dark:bg-accent-900/20 text-accent-700 dark:text-accent-400 rounded-full text-sm font-medium"
                    >
                      {specialty}
                    </span>
                  ))}
                </div>
              </div>

              {/* Achievements */}
              <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-6">Achievements</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {achievements.map((achievement, index) => {
                    const IconComponent = achievement.icon;
                    return (
                      <div key={index} className="flex items-center space-x-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-xl">
                        <div className="w-12 h-12 bg-accent-100 dark:bg-accent-900/20 rounded-xl flex items-center justify-center">
                          <IconComponent className="w-6 h-6 text-accent-600 dark:text-accent-400" />
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-900 dark:text-white">{achievement.title}</h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400">{achievement.description}</p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            </div>

            {/* Sidebar */}
            <div className="space-y-6">
              {/* Contact Information */}
              <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">Contact Information</h3>
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <Mail className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Email</p>
                      <p className="font-medium text-gray-900 dark:text-white">{profileData.email}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Phone className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Phone</p>
                      <p className="font-medium text-gray-900 dark:text-white">{profileData.phone}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Globe className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Website</p>
                      <a 
                        href={profileData.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="font-medium text-accent-600 dark:text-accent-400 hover:underline"
                      >
                        {profileData.website}
                      </a>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <MapPin className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Address</p>
                      <p className="font-medium text-gray-900 dark:text-white">{profileData.address}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Business Details */}
              <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
                <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-4">Business Details</h3>
                <div className="space-y-4">
                  <div className="flex items-center space-x-3">
                    <Calendar className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Founded</p>
                      <p className="font-medium text-gray-900 dark:text-white">{profileData.foundedYear}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Clock className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Operating Hours</p>
                      <p className="font-medium text-gray-900 dark:text-white">{profileData.operatingHours}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Store className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Business Type</p>
                      <p className="font-medium text-gray-900 dark:text-white">{profileData.businessType}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <Users className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-400">Partner Since</p>
                      <p className="font-medium text-gray-900 dark:text-white">{stats.joinedDate}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
  );
};

export default MerchantProfile;
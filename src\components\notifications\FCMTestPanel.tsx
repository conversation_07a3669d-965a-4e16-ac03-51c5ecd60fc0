import React, { useState } from 'react';
import { 
  Bell, 
  CheckCircle, 
  XCircle, 
  AlertCircle, 
  RefreshCw,
  Smartphone,
  Globe,
  Settings
} from 'lucide-react';
import { useFCM } from '../../hooks/useFCMTokenManager';
import { useAuth } from '../../hooks/useAuth';
import NotificationService from '../../services/notificationService';

interface FCMTestPanelProps {
  className?: string;
}

export const FCMTestPanel: React.FC<FCMTestPanelProps> = ({ className = '' }) => {
  const [testResult, setTestResult] = useState<string | null>(null);
  const [isTestingNotification, setIsTestingNotification] = useState(false);
  
  const { user } = useAuth();
  const {
    token,
    isSupported,
    isLoading,
    error,
    permission,
    isInitialized,
    retryCount,
    requestPermission,
    refreshToken,
    clearToken,
    retryPermission,
    checkSupport
  } = useFCM();

  // Test notification sending
  const testNotification = async () => {
    if (!user) {
      setTestResult('Error: User not authenticated');
      return;
    }

    setIsTestingNotification(true);
    setTestResult(null);

    try {
      const result = await NotificationService.sendNotification({
        userId: user.uid,
        type: 'platform_announcement',
        title: 'FCM Test Notification',
        message: 'This is a test notification to verify FCM is working correctly.',
        channels: ['in_app', 'push'],
        priority: 'normal',
        link: '/notifications',
        metadata: {
          test: true,
          timestamp: new Date().toISOString()
        }
      });

      if (result.success) {
        setTestResult(`✅ Test notification sent successfully via: ${result.channels.join(', ')}`);
      } else {
        setTestResult(`❌ Test notification failed: ${result.error}`);
      }
    } catch (error) {
      setTestResult(`❌ Test notification error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsTestingNotification(false);
    }
  };

  // Get status icon and color
  const getStatusIcon = () => {
    if (isLoading || !isInitialized) return <RefreshCw className="h-5 w-5 animate-spin text-blue-500" />;
    if (!isSupported) return <XCircle className="h-5 w-5 text-red-500" />;
    if (permission === 'denied') return <XCircle className="h-5 w-5 text-red-500" />;
    if (permission === 'granted' && token) return <CheckCircle className="h-5 w-5 text-green-500" />;
    if (permission === 'default') return <AlertCircle className="h-5 w-5 text-yellow-500" />;
    return <AlertCircle className="h-5 w-5 text-gray-500" />;
  };

  const getStatusText = () => {
    if (isLoading || !isInitialized) return 'Initializing FCM...';
    if (!isSupported) return 'FCM not supported on this device/browser';
    if (permission === 'denied') return 'Notification permission denied';
    if (permission === 'granted' && token) return 'FCM ready and connected';
    if (permission === 'default') return 'Notification permission not requested';
    return 'FCM status unknown';
  };

  const getPlatformInfo = () => {
    const userAgent = navigator.userAgent;
    const isAndroid = /Android/i.test(userAgent);
    const isIOS = /iPad|iPhone|iPod/.test(userAgent);
    const isWindows = /Windows/i.test(userAgent);
    const isMac = /Macintosh|MacIntel|MacPPC|Mac68K/i.test(userAgent);
    const isChrome = /Chrome/i.test(userAgent);
    const isSafari = /Safari/i.test(userAgent) && !/Chrome/i.test(userAgent);
    const isEdge = /Edg/i.test(userAgent);
    const isFirefox = /Firefox/i.test(userAgent);

    let platform = 'Unknown';
    let browser = 'Unknown';
    let fcmSupport = 'Unknown';

    if (isAndroid) platform = 'Android';
    else if (isIOS) platform = 'iOS';
    else if (isWindows) platform = 'Windows';
    else if (isMac) platform = 'macOS';

    if (isChrome) browser = 'Chrome';
    else if (isSafari) browser = 'Safari';
    else if (isEdge) browser = 'Edge';
    else if (isFirefox) browser = 'Firefox';

    // FCM support by platform/browser
    if (isAndroid && isChrome) fcmSupport = 'Full Support';
    else if ((isWindows || isMac) && (isChrome || isEdge)) fcmSupport = 'Full Support';
    else if (isIOS && isSafari) fcmSupport = 'Limited (PWA only, iOS 16.4+)';
    else if (isMac && isSafari) fcmSupport = 'Limited';
    else fcmSupport = 'Not Supported';

    return { platform, browser, fcmSupport };
  };

  const platformInfo = getPlatformInfo();

  return (
    <div className={`bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 ${className}`}>
      <div className="flex items-center space-x-3 mb-6">
        <Bell className="h-6 w-6 text-primary-600 dark:text-primary-400" />
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          FCM Debug Panel
        </h3>
      </div>

      {/* Status Overview */}
      <div className="space-y-4 mb-6">
        <div className="flex items-center space-x-3">
          {getStatusIcon()}
          <span className="text-sm font-medium text-gray-900 dark:text-white">
            {getStatusText()}
          </span>
        </div>

        {error && (
          <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <p className="text-sm text-red-800 dark:text-red-400">{error}</p>
          </div>
        )}
      </div>

      {/* Platform Information */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="flex items-center space-x-2">
          <Smartphone className="h-4 w-4 text-gray-400" />
          <div>
            <p className="text-xs text-gray-500 dark:text-gray-400">Platform</p>
            <p className="text-sm font-medium text-gray-900 dark:text-white">{platformInfo.platform}</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <Globe className="h-4 w-4 text-gray-400" />
          <div>
            <p className="text-xs text-gray-500 dark:text-gray-400">Browser</p>
            <p className="text-sm font-medium text-gray-900 dark:text-white">{platformInfo.browser}</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <Settings className="h-4 w-4 text-gray-400" />
          <div>
            <p className="text-xs text-gray-500 dark:text-gray-400">FCM Support</p>
            <p className={`text-sm font-medium ${
              platformInfo.fcmSupport === 'Full Support' ? 'text-green-600 dark:text-green-400' :
              platformInfo.fcmSupport.includes('Limited') ? 'text-yellow-600 dark:text-yellow-400' :
              'text-red-600 dark:text-red-400'
            }`}>
              {platformInfo.fcmSupport}
            </p>
          </div>
        </div>
      </div>

      {/* Technical Details */}
      <div className="space-y-3 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-500 dark:text-gray-400">Permission:</span>
            <span className={`ml-2 font-medium ${
              permission === 'granted' ? 'text-green-600 dark:text-green-400' :
              permission === 'denied' ? 'text-red-600 dark:text-red-400' :
              'text-yellow-600 dark:text-yellow-400'
            }`}>
              {permission}
            </span>
          </div>
          
          <div>
            <span className="text-gray-500 dark:text-gray-400">Retry Count:</span>
            <span className="ml-2 font-medium text-gray-900 dark:text-white">{retryCount}</span>
          </div>
          
          <div>
            <span className="text-gray-500 dark:text-gray-400">Token:</span>
            <span className={`ml-2 font-medium ${token ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
              {token ? 'Present' : 'Missing'}
            </span>
          </div>
          
          <div>
            <span className="text-gray-500 dark:text-gray-400">Initialized:</span>
            <span className={`ml-2 font-medium ${isInitialized ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
              {isInitialized ? 'Yes' : 'No'}
            </span>
          </div>
        </div>

        {token && (
          <div>
            <span className="text-gray-500 dark:text-gray-400">Token (truncated):</span>
            <p className="mt-1 text-xs font-mono bg-gray-100 dark:bg-gray-700 p-2 rounded break-all">
              {token.substring(0, 50)}...
            </p>
          </div>
        )}
      </div>

      {/* Actions */}
      <div className="flex flex-wrap gap-2 mb-4">
        {permission === 'default' && (
          <button
            onClick={requestPermission}
            disabled={isLoading || !isSupported}
            className="px-3 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
          >
            Request Permission
          </button>
        )}

        {permission === 'denied' && retryCount < 3 && (
          <button
            onClick={retryPermission}
            disabled={isLoading}
            className="px-3 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
          >
            Retry Permission
          </button>
        )}

        {token && (
          <>
            <button
              onClick={refreshToken}
              disabled={isLoading}
              className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
            >
              Refresh Token
            </button>

            <button
              onClick={testNotification}
              disabled={isTestingNotification || !user}
              className="px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
            >
              {isTestingNotification ? 'Testing...' : 'Test Notification'}
            </button>

            <button
              onClick={clearToken}
              disabled={isLoading}
              className="px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
            >
              Clear Token
            </button>
          </>
        )}
      </div>

      {/* Test Result */}
      {testResult && (
        <div className={`p-3 rounded-lg ${
          testResult.includes('✅') ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800' :
          'bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800'
        }`}>
          <p className={`text-sm ${
            testResult.includes('✅') ? 'text-green-800 dark:text-green-400' :
            'text-red-800 dark:text-red-400'
          }`}>
            {testResult}
          </p>
        </div>
      )}
    </div>
  );
};

// Extended functions that can be deployed separately to avoid timeouts
import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';

// Initialize Firebase Admin if not already done
if (!admin.apps.length) {
  admin.initializeApp();
}

// Import all the heavy modules here
export * from './admin/adminPin';
export * from './auth';
export * from './stripe';
export * from './wallet';
export * from './reeflex';
export * from './shipping';
export * from './messages';
export * from './listings';
export * from './uploads';
export * from './feedback';

// Shipping rates callable function
export const getShippingRatesCallable = functions.https.onCall(async (data, _context) => {
  try {
    const { fromAddress, toAddress, packageDetails } = data;

    // Validate required fields
    if (!fromAddress || !toAddress || !packageDetails) {
      throw new functions.https.HttpsError('invalid-argument', 'Missing required fields: fromAddress, toAddress, packageDetails');
    }

    // Import the shipping service dynamically
    const { getShippingRates } = await import('./services/shippingService');
    const { PACKAGE_PRESETS } = await import('./services/shippingService');

    // Prepare package info for shipping service
    let packageInfo: any;

    if (packageDetails.useCustom) {
      // Custom package dimensions
      if (!packageDetails.dimensions || !packageDetails.weight) {
        throw new functions.https.HttpsError('invalid-argument', 'Custom package requires dimensions and weight');
      }

      packageInfo = {
        weight: packageDetails.weight.value.toString(),
        weightUnit: packageDetails.weight.unit,
        length: packageDetails.dimensions.length,
        width: packageDetails.dimensions.width,
        height: packageDetails.dimensions.height,
        dimensionUnit: packageDetails.dimensions.unit
      };
    } else {
      // Preset package
      if (!packageDetails.preset || !(packageDetails.preset in PACKAGE_PRESETS)) {
        throw new functions.https.HttpsError('invalid-argument', 'Invalid package preset');
      }

      const preset = PACKAGE_PRESETS[packageDetails.preset as keyof typeof PACKAGE_PRESETS];
      packageInfo = {
        weight: preset.weight.value.toString(),
        weightUnit: preset.weight.unit,
        length: preset.dimensions.length,
        width: preset.dimensions.width,
        height: preset.dimensions.height,
        dimensionUnit: preset.dimensions.unit,
        presetUsed: packageDetails.preset
      };
    }

    // Get shipping rates from Shippo
    const rates = await getShippingRates(fromAddress, toAddress, packageInfo);

    // Return the rates
    return {
      success: true,
      rates: rates.map(rate => ({
        carrier: rate.carrier,
        service: rate.service,
        amount: parseFloat(rate.amount),
        currency: rate.currency,
        estimatedDays: rate.estimated_days,
        rateId: rate.rate_id
      }))
    };

  } catch (error: any) {
    console.error('Error getting shipping rates:', error);

    if (error instanceof functions.https.HttpsError) {
      throw error;
    }

    throw new functions.https.HttpsError('internal', 'Failed to get shipping rates: ' + (error.message || 'Unknown error'));
  }
});

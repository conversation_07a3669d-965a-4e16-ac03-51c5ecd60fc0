import { useState, useEffect, useCallback } from 'react';
import { isBlockedByClient } from '../utils/networkUtils';

interface FirebaseError {
  message: string;
  timestamp: Date;
  operation?: string;
}

export const useFirebaseErrorHandler = () => {
  const [currentError, setCurrentError] = useState<FirebaseError | null>(null);
  const [errorHistory, setErrorHistory] = useState<FirebaseError[]>([]);

  // Global error handler for unhandled promise rejections
  useEffect(() => {
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      const error = event.reason;
      
      if (error && typeof error === 'object' && 'message' in error) {
        const errorMessage = String(error.message);
        
        // Check if this is a Firebase blocked error
        if (isBlockedByClient(errorMessage) || 
            errorMessage.includes('firestore.googleapis.com') ||
            errorMessage.includes('firebase')) {
          
          const firebaseError: FirebaseError = {
            message: errorMessage,
            timestamp: new Date(),
            operation: 'unknown'
          };
          
          setCurrentError(firebaseError);
          setErrorHistory(prev => [firebaseError, ...prev.slice(0, 9)]); // Keep last 10 errors
          
          // Prevent the error from being logged to console as unhandled
          event.preventDefault();
        }
      }
    };

    // Global error handler for regular errors
    const handleError = (event: ErrorEvent) => {
      const errorMessage = event.message || event.error?.message || 'Unknown error';
      
      if (isBlockedByClient(errorMessage) || 
          errorMessage.includes('firestore.googleapis.com') ||
          errorMessage.includes('firebase')) {
        
        const firebaseError: FirebaseError = {
          message: errorMessage,
          timestamp: new Date(),
          operation: 'unknown'
        };
        
        setCurrentError(firebaseError);
        setErrorHistory(prev => [firebaseError, ...prev.slice(0, 9)]);
      }
    };

    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    window.addEventListener('error', handleError);

    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
      window.removeEventListener('error', handleError);
    };
  }, []);

  const reportError = useCallback((error: Error, operation?: string) => {
    const firebaseError: FirebaseError = {
      message: error.message,
      timestamp: new Date(),
      operation
    };
    
    setCurrentError(firebaseError);
    setErrorHistory(prev => [firebaseError, ...prev.slice(0, 9)]);
  }, []);

  const clearCurrentError = useCallback(() => {
    setCurrentError(null);
  }, []);

  const clearErrorHistory = useCallback(() => {
    setErrorHistory([]);
  }, []);

  const isFirebaseBlocked = useCallback(() => {
    return currentError && isBlockedByClient(currentError.message);
  }, [currentError]);

  return {
    currentError,
    errorHistory,
    reportError,
    clearCurrentError,
    clearErrorHistory,
    isFirebaseBlocked
  };
};

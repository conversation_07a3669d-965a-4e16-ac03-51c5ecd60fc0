// Minimal functions index - only essential webhook functionality
import * as functions from 'firebase-functions/v1';
import * as admin from 'firebase-admin';

// Initialize Firebase Admin
admin.initializeApp();

// Helper function to generate 6-digit secret code
function generateSecretCode(): string {
  return Math.floor(100000 + Math.random() * 900000).toString();
}

// Essential Stripe webhook - only handles payment completion
export const essentialWebhook = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 60,
  })
  .https.onRequest(async (req, res) => {
    try {
      console.log('🔗 Essential webhook received');

      if (req.method !== 'POST') {
        res.status(405).send('Method not allowed');
        return;
      }

      const event = req.body;
      console.log(`📨 Event type: ${event.type}`);

      // Handle checkout session completed
      if (event.type === 'checkout.session.completed') {
        const session = event.data.object;
        const metadata = session.metadata;

        if (metadata?.orderId) {
          const orderId = metadata.orderId;
          console.log(`📦 Processing order: ${orderId}`);

          // Get order
          const orderRef = admin.firestore().collection('orders').doc(orderId);
          const orderDoc = await orderRef.get();

          if (orderDoc.exists) {
            const orderData = orderDoc.data();
            
            // Generate secret code
            const secretCode = generateSecretCode();
            console.log(`🔐 Generated code: ${secretCode}`);

            // Update order
            await orderRef.update({
              status: 'payment_completed',
              secretCode: secretCode,
              paymentCompletedAt: admin.firestore.Timestamp.now(),
              updatedAt: admin.firestore.Timestamp.now()
            });

            // Update listing to sold
            if (orderData?.listingId) {
              await admin.firestore().collection('listings').doc(orderData.listingId).update({
                status: 'sold',
                soldAt: admin.firestore.Timestamp.now(),
                updatedAt: admin.firestore.Timestamp.now()
              });
              console.log(`✅ Listing ${orderData.listingId} marked as sold`);
            }

            // Send buyer notification
            if (orderData?.buyerId) {
              await admin.firestore().collection('notifications').add({
                userId: orderData.buyerId,
                type: 'payment_success',
                title: 'Payment Successful!',
                message: `Payment processed. Secret code: ${secretCode}`,
                orderId: orderId,
                secretCode: secretCode,
                read: false,
                createdAt: admin.firestore.Timestamp.now()
              });
            }

            console.log(`✅ Order ${orderId} processed successfully`);
          }
        }
      }

      res.status(200).json({ received: true });

    } catch (error) {
      console.error('❌ Webhook error:', error);
      res.status(500).send('Webhook failed');
    }
  });

// Test function
export const testEssential = functions
  .https.onRequest(async (_req, res) => {
    res.json({
      success: true,
      message: 'Essential webhook working',
      testCode: generateSecretCode(),
      timestamp: new Date().toISOString()
    });
  });

// Release funds with secret code (alias for compatibility)
export const releaseEscrowWithCode = functions
  .https.onCall(async (data, context) => {
    try {
      if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'Authentication required');
      }

      const { orderId, secretCode } = data;

      if (!orderId || !secretCode) {
        throw new functions.https.HttpsError('invalid-argument', 'Order ID and secret code required');
      }

      // Get order
      const orderRef = admin.firestore().collection('orders').doc(orderId);
      const orderDoc = await orderRef.get();

      if (!orderDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'Order not found');
      }

      const orderData = orderDoc.data();

      // Verify buyer
      if (orderData?.buyerId !== context.auth.uid) {
        throw new functions.https.HttpsError('permission-denied', 'Not authorized');
      }

      // Verify secret code
      if (orderData?.secretCode !== secretCode) {
        throw new functions.https.HttpsError('invalid-argument', 'Invalid secret code');
      }

      // Update order status
      await orderRef.update({
        status: 'completed',
        fundsReleased: true,
        fundsReleasedAt: admin.firestore.Timestamp.now(),
        completedAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now()
      });

      console.log(`✅ Funds released for order: ${orderId}`);

      return {
        success: true,
        message: 'Funds released successfully'
      };

    } catch (error) {
      console.error('Error releasing funds:', error);
      throw error;
    }
  });

// Release funds with secret code (new name)
export const releaseFundsWithCode = functions
  .https.onCall(async (data, context) => {
    try {
      if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'Authentication required');
      }

      const { orderId, secretCode } = data;
      
      if (!orderId || !secretCode) {
        throw new functions.https.HttpsError('invalid-argument', 'Order ID and secret code required');
      }

      // Get order
      const orderRef = admin.firestore().collection('orders').doc(orderId);
      const orderDoc = await orderRef.get();

      if (!orderDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'Order not found');
      }

      const orderData = orderDoc.data();

      // Verify buyer
      if (orderData?.buyerId !== context.auth.uid) {
        throw new functions.https.HttpsError('permission-denied', 'Not authorized');
      }

      // Verify secret code
      if (orderData?.secretCode !== secretCode) {
        throw new functions.https.HttpsError('invalid-argument', 'Invalid secret code');
      }

      // Update order status
      await orderRef.update({
        status: 'completed',
        fundsReleased: true,
        fundsReleasedAt: admin.firestore.Timestamp.now(),
        completedAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now()
      });

      console.log(`✅ Funds released for order: ${orderId}`);

      return {
        success: true,
        message: 'Funds released successfully'
      };

    } catch (error) {
      console.error('Error releasing funds:', error);
      throw error;
    }
  });

// Mark delivery completed (for sellers)
export const markDeliveryCompleted = functions
  .https.onCall(async (data, context) => {
    try {
      if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'Authentication required');
      }

      const { orderId } = data;
      
      if (!orderId) {
        throw new functions.https.HttpsError('invalid-argument', 'Order ID required');
      }

      // Get order
      const orderRef = admin.firestore().collection('orders').doc(orderId);
      const orderDoc = await orderRef.get();

      if (!orderDoc.exists) {
        throw new functions.https.HttpsError('not-found', 'Order not found');
      }

      const orderData = orderDoc.data();

      // Verify seller
      if (orderData?.sellerId !== context.auth.uid) {
        throw new functions.https.HttpsError('permission-denied', 'Not authorized');
      }

      // Update order status to delivered
      await orderRef.update({
        status: 'delivered',
        deliveredAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now()
      });

      // Notify buyer
      if (orderData?.buyerId) {
        await admin.firestore().collection('notifications').add({
          userId: orderData.buyerId,
          type: 'order_delivered',
          title: 'Order Delivered!',
          message: `Your order has been delivered. Enter the secret code to release funds.`,
          orderId: orderId,
          read: false,
          createdAt: admin.firestore.Timestamp.now()
        });
      }

      console.log(`✅ Order ${orderId} marked as delivered`);

      return {
        success: true,
        message: 'Order marked as delivered'
      };

    } catch (error) {
      console.error('Error marking delivery:', error);
      throw error;
    }
  });

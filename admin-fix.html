<!DOCTYPE html>
<html>
<head>
    <title>Hive Campus Admin Fix</title>
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.gstatic.com https://*.googleapis.com; style-src 'self' 'unsafe-inline'; connect-src 'self' https://*.googleapis.com https://*.firebaseapp.com https://*.google.com; frame-src 'self' https://*.firebaseapp.com https://*.google.com https://accounts.google.com; img-src 'self' data: https:;">
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        button { background: #4285f4; color: white; border: none; padding: 10px 20px; margin: 10px; border-radius: 5px; cursor: pointer; }
        button:hover { background: #3367d6; }
        .success { color: green; }
        .error { color: red; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 5px; overflow-x: auto; }
        .step { background: #e8f0fe; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #4285f4; }
        .form-group { margin: 15px 0; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input { padding: 8px; border: 1px solid #ddd; border-radius: 4px; width: 250px; }
        .hidden { display: none; }
    </style>
</head>
<body>
    <h1>🔧 Hive Campus Admin Fix Tool</h1>

    <div class="step">
        <h3>Step 1: Fix Firestore Data</h3>
        <div id="status">Ready to fix admin user...</div>

        <div class="form-group hidden" id="passwordForm">
            <label for="adminPassword">Admin Password:</label>
            <input type="password" id="adminPassword" name="adminPassword" placeholder="Enter admin password">
        </div>

        <button type="button" id="fixAdminBtn">Fix Admin User in Firestore</button>
        <button type="button" id="checkStatusBtn">Check Current Status</button>
    </div>

    <div class="step">
        <h3>Step 2: Set Custom Claims (Manual)</h3>
        <p>After fixing Firestore data, you need to set custom claims manually:</p>
        <ol>
            <li>Go to <a href="https://console.firebase.google.com/project/h1c1-798a8/authentication/users" target="_blank">Firebase Console - Users</a></li>
            <li>Click on user: <code><EMAIL></code></li>
            <li>Look for "Custom claims" section in the user details</li>
            <li>Add this JSON: <pre>{"admin": true, "role": "admin"}</pre></li>
            <li>Click Save</li>
        </ol>
        <p><strong>Note:</strong> If you can't find "Custom claims", try refreshing the page or use the CLI method below.</p>
    </div>

    <div class="step">
        <h3>Alternative: Use Firebase CLI</h3>
        <p>If custom claims UI is not available, run this in your terminal:</p>
        <pre>cd scripts
node set-admin-claims.js</pre>
    </div>

    <div id="results"></div>

    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/9.0.0/firebase-app.js';
        import { getFirestore, doc, setDoc, collection } from 'https://www.gstatic.com/firebasejs/9.0.0/firebase-firestore.js';
        import { getAuth, signInWithEmailAndPassword } from 'https://www.gstatic.com/firebasejs/9.0.0/firebase-auth.js';

        // Your Firebase config (Updated with correct API key)
        const firebaseConfig = {
            apiKey: "AIzaSyBnF-pVKlXNdwBQ-74SEfw7NHJLLmL5e6A",
            authDomain: "h1c1-798a8.firebaseapp.com",
            projectId: "h1c1-798a8",
            storageBucket: "h1c1-798a8.firebasestorage.app",
            messagingSenderId: "1096652648176",
            appId: "1:1096652648176:web:4caac283b87d55a4ba9c35"
        };

        const app = initializeApp(firebaseConfig);
        const db = getFirestore(app);
        const auth = getAuth(app);

        // Event listeners instead of onclick
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('fixAdminBtn').addEventListener('click', fixAdmin);
            document.getElementById('checkStatusBtn').addEventListener('click', checkStatus);
        });

        async function fixAdmin() {
            const status = document.getElementById('status');
            const results = document.getElementById('results');
            const passwordForm = document.getElementById('passwordForm');
            const passwordInput = document.getElementById('adminPassword');

            try {
                // Show password form if not already shown
                if (passwordForm.classList.contains('hidden')) {
                    passwordForm.classList.remove('hidden');
                    status.textContent = 'Please enter admin password above and click again.';
                    return;
                }

                const password = passwordInput.value;
                if (!password) {
                    status.textContent = 'Please enter the admin password.';
                    return;
                }

                status.textContent = 'Fixing admin user...';

                // Login as admin first
                const email = '<EMAIL>';

                const userCredential = await signInWithEmailAndPassword(auth, email, password);
                const user = userCredential.user;
                
                status.textContent = 'Logged in, updating Firestore...';
                
                // Update user document
                await setDoc(doc(db, 'users', user.uid), {
                    uid: user.uid,
                    name: user.displayName || 'Admin User',
                    email: user.email,
                    role: 'admin',
                    university: 'Hive Campus Admin',
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    emailVerified: true,
                    status: 'active',
                    adminLevel: 'super',
                    permissions: ['user_management', 'content_moderation', 'analytics', 'system_settings', 'super_admin']
                }, { merge: true });
                
                // Create missing collections
                const collections = ['reports', 'shippingLabels', 'walletReports', 'universityAnalytics', 'systemMetrics', 'adminLogs'];
                
                for (const collectionName of collections) {
                    await setDoc(doc(db, collectionName, 'default'), {
                        created: new Date(),
                        type: 'system'
                    });
                }
                
                status.textContent = 'Firestore data fixed successfully!';
                results.innerHTML = `
                    <div class="success">
                        <h3>✅ Firestore Data Fixed!</h3>
                        <p><strong>Admin user:</strong> ${email}</p>
                        <p><strong>UID:</strong> ${user.uid}</p>
                        <p><strong>Collections created:</strong> ${collections.join(', ')}</p>

                        <h4>🔥 IMPORTANT: Now Set Custom Claims</h4>
                        <ol>
                            <li>Go to <a href="https://console.firebase.google.com/project/h1c1-798a8/authentication/users" target="_blank">Firebase Console - Users</a></li>
                            <li>Click on: <strong>${email}</strong></li>
                            <li>Find "Custom claims" section</li>
                            <li>Add this JSON: <pre>{"admin": true, "role": "admin"}</pre></li>
                            <li>Click Save</li>
                            <li><strong>Clear browser cache and cookies completely</strong></li>
                            <li>Login again - you should be redirected to admin dashboard</li>
                        </ol>

                        <p><strong>Alternative:</strong> Run the verification script:</p>
                        <pre>cd scripts && node verify-admin-user.js</pre>

                        <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 10px; border-radius: 5px; margin-top: 10px;">
                            <strong>⚠️ Troubleshooting:</strong> If you're still redirected to student dashboard after login:
                            <ul>
                                <li>Clear all browser data (cache, cookies, local storage)</li>
                                <li>Try incognito/private browsing mode</li>
                                <li>Check that custom claims are set correctly in Firebase Console</li>
                                <li>Verify Firestore document has role: "admin"</li>
                            </ul>
                        </div>
                    </div>
                `;
                
            } catch (error) {
                status.textContent = 'Error: ' + error.message;
                results.innerHTML = `<p style="color: red;">Error: ${error.message}</p>`;
            }
        }

        async function checkStatus() {
            const results = document.getElementById('results');
            const user = auth.currentUser;
            
            if (user) {
                const idTokenResult = await user.getIdTokenResult();
                results.innerHTML = `
                    <h3>Current User Status:</h3>
                    <p>Email: ${user.email}</p>
                    <p>UID: ${user.uid}</p>
                    <p>Email Verified: ${user.emailVerified}</p>
                    <p>Custom Claims: ${JSON.stringify(idTokenResult.claims, null, 2)}</p>
                `;
            } else {
                results.innerHTML = '<p>No user logged in</p>';
            }
        }
    </script>
</body>
</html>

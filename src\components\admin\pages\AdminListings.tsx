import React, { useState, useEffect } from 'react';
import { Timestamp } from 'firebase/firestore';
import {
  Package,
  Search,
  Filter,
  Eye,
  Trash2,
  Flag,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Calendar,
  DollarSign,
  User,
  GraduationCap,
  Image,
  MoreVertical,
  Ban,

} from 'lucide-react';
import { AdminDataService, FilterOptions, PaginationOptions } from '../../../services/AdminDataService';
import { Listing } from '../../../firebase/types';
import { logAdminAction } from '../../../utils/adminAuth';
import { useAuth } from '../../../hooks/useAuth';

const AdminListings: React.FC = () => {
  const { userProfile } = useAuth();
  const [listings, setListings] = useState<Listing[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');

  // Modal states for replacing prompt()
  const [showReasonModal, setShowReasonModal] = useState(false);
  const [modalConfig, setModalConfig] = useState<{
    title: string;
    placeholder: string;
    action: string;
    listingId: string;
  } | null>(null);
  const [reasonInput, setReasonInput] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<string>('');
  const [selectedCategory, setSelectedCategory] = useState<string>('');
  const [selectedUniversity, setSelectedUniversity] = useState<string>('');
  const [showFilters, setShowFilters] = useState(false);
  const [hasMore, setHasMore] = useState(false);
  const [lastDoc, setLastDoc] = useState<any>(null);
  const [_selectedListing, _setSelectedListing] = useState<Listing | null>(null);
  const [error, setError] = useState<string | null>(null);

  const categories = [
    'Textbooks',
    'Electronics',
    'Furniture',
    'Clothing',
    'Sports & Recreation',
    'School Supplies',
    'Transportation',
    'Other'
  ];

  const statuses = [
    'active',
    'sold',
    'pending',
    'inactive',
    'flagged',
    'banned'
  ];

  useEffect(() => {
    fetchListings(true);
  }, [selectedStatus, selectedCategory, selectedUniversity]);

  const fetchListings = async (reset = false) => {
    try {
      setLoading(true);
      setError(null);

      const filters: FilterOptions = {};
      if (selectedStatus) filters.status = selectedStatus;
      if (selectedCategory) filters.category = selectedCategory;
      if (selectedUniversity) filters.university = selectedUniversity;

      const options: PaginationOptions = {
        pageSize: 20,
        lastDoc: reset ? undefined : lastDoc
      };

      const result = await AdminDataService.getListings(options, filters);

      if (reset) {
        setListings(result.listings);
      } else {
        setListings(prev => [...prev, ...result.listings]);
      }

      setHasMore(result.hasMore);
      setLastDoc(result.lastDoc);
    } catch (err) {
      console.error('Error fetching listings:', err);
      setError('Failed to load listings');
    } finally {
      setLoading(false);
    }
  };

  const handleListingAction = async (listingId: string, action: string) => {
    try {
      if (userProfile) {
        await logAdminAction(userProfile, `listing_${action}`, { listingId });
      }

      switch (action) {
        case 'view': {
          // Open listing in new tab
          window.open(`/listing/${listingId}`, '_blank');
          return; // Don't refresh for view action
        }
        case 'approve': {
          await AdminDataService.updateListing(listingId, {
            status: 'active',
            moderatedAt: Timestamp.now(),
            moderatedBy: userProfile?.uid
          });
          break;
        }
        case 'flag': {
          setModalConfig({
            title: 'Flag Listing',
            placeholder: 'Enter reason for flagging this listing...',
            action: 'flag',
            listingId
          });
          setShowReasonModal(true);
          return; // Don't continue processing
        }
        case 'unflag': {
          await AdminDataService.updateListing(listingId, {
            status: 'active',
            flagReason: undefined,
            flaggedAt: undefined,
            flaggedBy: undefined,
            moderatedAt: Timestamp.now(),
            moderatedBy: userProfile?.uid
          });
          break;
        }
        case 'block': {
          setModalConfig({
            title: 'Block Listing',
            placeholder: 'Enter reason for blocking this listing...',
            action: 'block',
            listingId
          });
          setShowReasonModal(true);
          return; // Don't continue processing
        }
        case 'unblock': {
          await AdminDataService.updateListing(listingId, {
            status: 'active',
            blockReason: null,
            blockedAt: null,
            blockedBy: null,
            moderatedAt: new Date(),
            moderatedBy: userProfile?.uid
          });
          break;
        }
        case 'ban': {
          setModalConfig({
            title: 'Ban Listing',
            placeholder: 'Enter reason for banning this listing...',
            action: 'ban',
            listingId
          });
          setShowReasonModal(true);
          return; // Don't continue processing
        }
        case 'unban': {
          await AdminDataService.updateListing(listingId, {
            status: 'active',
            banReason: null,
            bannedAt: null,
            bannedBy: null,
            moderatedAt: new Date(),
            moderatedBy: userProfile?.uid
          });
          break;
        }
        case 'feature': {
          await AdminDataService.updateListing(listingId, {
            featured: true,
            featuredAt: new Date(),
            featuredBy: userProfile?.uid
          });
          break;
        }
        case 'unfeature': {
          await AdminDataService.updateListing(listingId, {
            featured: false,
            featuredAt: null,
            featuredBy: null
          });
          break;
        }
        case 'delete': {
          if (confirm('Are you sure you want to delete this listing? This action cannot be undone.')) {
            await AdminDataService.deleteListing(listingId);
          } else {
            return; // User cancelled
          }
          break;
        }
      }

      // Refresh listings
      await fetchListings(true);
    } catch (err) {
      console.error(`Error performing ${action} on listing:`, err);
    }
  };

  const handleModalSubmit = async () => {
    if (!modalConfig || !reasonInput.trim()) return;

    try {
      const { action, listingId } = modalConfig;

      switch (action) {
        case 'flag':
          await AdminDataService.updateListing(listingId, {
            status: 'flagged',
            flagReason: reasonInput.trim(),
            flaggedAt: Timestamp.now(),
            flaggedBy: userProfile?.uid
          });
          break;
        case 'block':
          await AdminDataService.updateListing(listingId, {
            status: 'blocked',
            blockReason: reasonInput.trim(),
            blockedAt: Timestamp.now(),
            blockedBy: userProfile?.uid
          });
          break;
        case 'ban':
          await AdminDataService.updateListing(listingId, {
            status: 'banned',
            banReason: reasonInput.trim(),
            bannedAt: Timestamp.now(),
            bannedBy: userProfile?.uid
          });
          break;
      }

      // Close modal and refresh listings
      setShowReasonModal(false);
      setModalConfig(null);
      setReasonInput('');
      await fetchListings(true);
    } catch (err) {
      console.error(`Error performing ${modalConfig.action} on listing:`, err);
    }
  };

  const handleModalCancel = () => {
    setShowReasonModal(false);
    setModalConfig(null);
    setReasonInput('');
  };

  const filteredListings = listings.filter(listing =>
    listing.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    listing.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    listing.ownerName?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'N/A';

    try {
      let date: Date;

      if (timestamp.toDate && typeof timestamp.toDate === 'function') {
        date = timestamp.toDate();
      } else if (timestamp.seconds && typeof timestamp.seconds === 'number') {
        date = new Date(timestamp.seconds * 1000);
      } else if (timestamp._seconds && typeof timestamp._seconds === 'number') {
        date = new Date(timestamp._seconds * 1000);
      } else {
        date = new Date(timestamp);
      }

      if (isNaN(date.getTime())) {
        return 'N/A';
      }

      return date.toLocaleDateString();
    } catch (error) {
      console.warn('Error formatting date:', error);
      return 'N/A';
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(price);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
      case 'sold': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300';
      case 'pending': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300';
      case 'inactive': return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
      case 'flagged': return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300';
      case 'banned': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
    }
  };

  const getConditionColor = (condition: string) => {
    switch (condition) {
      case 'new': return 'text-green-600 dark:text-green-400';
      case 'like-new': return 'text-blue-600 dark:text-blue-400';
      case 'good': return 'text-yellow-600 dark:text-yellow-400';
      case 'fair': return 'text-orange-600 dark:text-orange-400';
      case 'poor': return 'text-red-600 dark:text-red-400';
      default: return 'text-gray-600 dark:text-gray-400';
    }
  };

  if (loading && listings.length === 0) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600 dark:text-gray-400">Loading listings...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex">
            <AlertTriangle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                Error Loading Listings
              </h3>
              <p className="mt-1 text-sm text-red-700 dark:text-red-300">{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Listings Management</h2>
          <p className="text-gray-600 dark:text-gray-400">
            Moderate and manage marketplace listings ({listings.length} listings)
          </p>
        </div>
        <div className="flex space-x-3">
          <button className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
            <Flag className="h-4 w-4 mr-2" />
            Flagged ({listings.filter(l => l.status === 'flagged').length})
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white dark:bg-gray-800 shadow-sm rounded-lg border border-gray-200 dark:border-gray-700 p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search listings by title, description, or owner..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>
          </div>
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
          >
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </button>
        </div>

        {/* Filter Options */}
        {showFilters && (
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Status
                </label>
                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                >
                  <option value="">All Statuses</option>
                  {statuses.map(status => (
                    <option key={status} value={status}>
                      {status.charAt(0).toUpperCase() + status.slice(1)}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Category
                </label>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                >
                  <option value="">All Categories</option>
                  {categories.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  University
                </label>
                <input
                  type="text"
                  placeholder="Filter by university..."
                  value={selectedUniversity}
                  onChange={(e) => setSelectedUniversity(e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                />
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Listings Grid */}
      <div className="bg-white dark:bg-gray-800 shadow-sm rounded-lg border border-gray-200 dark:border-gray-700">
        {filteredListings.length === 0 ? (
          <div className="px-4 py-12">
            <div className="text-center">
              <Package className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No listings found</h3>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                {searchTerm ? 'Try adjusting your search terms.' : 'No listings match the current filters.'}
              </p>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 p-6">
            {filteredListings.map((listing) => (
              <div
                key={listing.id}
                className="border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden hover:shadow-md transition-shadow"
              >
                {/* Listing Image */}
                <div className="relative h-48 bg-gray-100 dark:bg-gray-700">
                  {listing.imageURLs && listing.imageURLs.length > 0 ? (
                    <img
                      src={listing.imageURLs[0]}
                      alt={listing.title}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center">
                      <Image className="h-12 w-12 text-gray-400" />
                    </div>
                  )}

                  {/* Status Badge */}
                  <div className="absolute top-2 left-2">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(listing.status)}`}>
                      {listing.status}
                    </span>
                  </div>

                  {/* Type Badge */}
                  <div className="absolute top-2 right-2">
                    <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300">
                      {listing.type}
                    </span>
                  </div>
                </div>

                {/* Listing Content */}
                <div className="p-4">
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white truncate">
                      {listing.title}
                    </h3>
                    <div className="flex items-center space-x-1">
                      <button
                        onClick={() => _setSelectedListing(listing)}
                        className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                        title="View Details"
                      >
                        <Eye className="h-4 w-4" />
                      </button>
                      <div className="relative">
                        <button className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                          <MoreVertical className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  </div>

                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                    {listing.description}
                  </p>

                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center text-lg font-bold text-gray-900 dark:text-white">
                      <DollarSign className="h-4 w-4 mr-1" />
                      {formatPrice(listing.price)}
                    </div>
                    <span className={`text-sm font-medium capitalize ${getConditionColor(listing.condition)}`}>
                      {listing.condition}
                    </span>
                  </div>

                  <div className="space-y-2 text-sm text-gray-500 dark:text-gray-400">
                    <div className="flex items-center">
                      <User className="h-3 w-3 mr-2" />
                      {listing.ownerName || 'Unknown'}
                    </div>
                    <div className="flex items-center">
                      <GraduationCap className="h-3 w-3 mr-2" />
                      {listing.university}
                    </div>
                    <div className="flex items-center">
                      <Calendar className="h-3 w-3 mr-2" />
                      {formatDate(listing.createdAt)}
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="mt-4 space-y-2">
                    {/* Primary Actions Row */}
                    <div className="flex space-x-2">
                      <button
                        onClick={() => handleListingAction(listing.id!, 'view')}
                        className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-blue-300 text-sm font-medium rounded-md text-blue-700 bg-blue-50 hover:bg-blue-100"
                        title="View Listing"
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </button>

                      {listing.status === 'flagged' ? (
                        <button
                          onClick={() => handleListingAction(listing.id!, 'unflag')}
                          className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700"
                          title="Unflag Listing"
                        >
                          <CheckCircle className="h-4 w-4 mr-1" />
                          Unflag
                        </button>
                      ) : listing.status === 'active' ? (
                        <button
                          onClick={() => handleListingAction(listing.id!, 'flag')}
                          className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-orange-300 text-sm font-medium rounded-md text-orange-700 bg-orange-50 hover:bg-orange-100"
                          title="Flag for Review"
                        >
                          <Flag className="h-4 w-4 mr-1" />
                          Flag
                        </button>
                      ) : null}
                    </div>

                    {/* Secondary Actions Row */}
                    <div className="flex space-x-2">
                      {listing.status === 'blocked' ? (
                        <button
                          onClick={() => handleListingAction(listing.id!, 'unblock')}
                          className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-green-300 text-sm font-medium rounded-md text-green-700 bg-green-50 hover:bg-green-100"
                          title="Unblock Listing"
                        >
                          <CheckCircle className="h-4 w-4 mr-1" />
                          Unblock
                        </button>
                      ) : (
                        <button
                          onClick={() => handleListingAction(listing.id!, 'block')}
                          className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-yellow-300 text-sm font-medium rounded-md text-yellow-700 bg-yellow-50 hover:bg-yellow-100"
                          title="Block Listing"
                        >
                          <XCircle className="h-4 w-4 mr-1" />
                          Block
                        </button>
                      )}

                      {listing.status === 'banned' ? (
                        <button
                          onClick={() => handleListingAction(listing.id!, 'unban')}
                          className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-green-300 text-sm font-medium rounded-md text-green-700 bg-green-50 hover:bg-green-100"
                          title="Unban Listing"
                        >
                          <CheckCircle className="h-4 w-4 mr-1" />
                          Unban
                        </button>
                      ) : (
                        <button
                          onClick={() => handleListingAction(listing.id!, 'ban')}
                          className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-red-50 hover:bg-red-100"
                          title="Ban Listing"
                        >
                          <Ban className="h-4 w-4 mr-1" />
                          Ban
                        </button>
                      )}

                      <button
                        onClick={() => handleListingAction(listing.id!, 'delete')}
                        className="flex-1 inline-flex items-center justify-center px-3 py-2 border border-red-300 text-sm font-medium rounded-md text-red-700 bg-red-50 hover:bg-red-100"
                        title="Delete Listing"
                      >
                        <Trash2 className="h-4 w-4 mr-1" />
                        Delete
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Load More Button */}
        {hasMore && !loading && (
          <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700">
            <button
              onClick={() => fetchListings(false)}
              className="w-full text-center py-2 text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
            >
              Load More Listings
            </button>
          </div>
        )}
      </div>

      {/* Reason Input Modal */}
      {showReasonModal && modalConfig && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              {modalConfig.title}
            </h3>

            <div className="mb-4">
              <label htmlFor="listing-reason-input" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Reason
              </label>
              <textarea
                id="listing-reason-input"
                name="reason"
                value={reasonInput}
                onChange={(e) => setReasonInput(e.target.value)}
                placeholder={modalConfig.placeholder}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white resize-none"
                rows={3}
                required
              />
            </div>

            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={handleModalCancel}
                className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleModalSubmit}
                disabled={!reasonInput.trim()}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Submit
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminListings;

import React, { useState, useEffect } from 'react';
import {
  Wallet,
  DollarSign,
  Gift,
  Users,
  Settings,
  Save,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import { functions } from '../../../firebase/config';
import { httpsCallable } from 'firebase/functions';

interface WalletSettings {
  signupBonus: number;
  referralBonus: number;
  enableSignupBonus: boolean;
  enableReferralBonus: boolean;
}

const AdminWalletSettings: React.FC = () => {
  const [settings, setSettings] = useState<WalletSettings>({
    signupBonus: 0,
    referralBonus: 0,
    enableSignupBonus: false,
    enableReferralBonus: false
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Load current wallet settings
  const loadSettings = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // Use Firebase callable function
      const getWalletSettings = httpsCallable(functions, 'getWalletSettings');
      const result = await getWalletSettings({});
      const data = result.data as { success: boolean; settings?: any };

      if (data.success && data.settings) {
        setSettings(data.settings);
      }
    } catch (error) {
      console.error('Error loading wallet settings:', error);
      setError('Failed to load wallet settings');
    } finally {
      setIsLoading(false);
    }
  };

  // Save wallet settings
  const saveSettings = async () => {
    try {
      setIsSaving(true);
      setError(null);
      setSuccess(null);

      // Use Firebase callable function
      const configureWalletSettings = httpsCallable(functions, 'configureWalletSettingsCallable');
      const result = await configureWalletSettings(settings);
      const data = result.data as { success: boolean; message?: string };

      if (data.success) {
        setSuccess('Wallet settings updated successfully!');
        setTimeout(() => setSuccess(null), 3000);
      }
    } catch (error) {
      console.error('Error saving wallet settings:', error);
      setError(error instanceof Error ? error.message : 'Failed to save wallet settings');
    } finally {
      setIsSaving(false);
    }
  };

  useEffect(() => {
    loadSettings();
  }, []);

  const handleInputChange = (field: keyof WalletSettings, value: number | boolean) => {
    setSettings(prev => ({
      ...prev,
      [field]: value
    }));
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center space-x-3 mb-4">
          <div className="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
            <Wallet className="w-6 h-6 text-purple-600 dark:text-purple-400" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Wallet Settings
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Configure promotional credits and bonuses for the wallet system
            </p>
          </div>
        </div>

        {/* Status Messages */}
        {error && (
          <div className="mb-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg flex items-center space-x-2">
            <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400" />
            <span className="text-red-700 dark:text-red-300">{error}</span>
          </div>
        )}

        {success && (
          <div className="mb-4 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg flex items-center space-x-2">
            <CheckCircle className="w-5 h-5 text-green-600 dark:text-green-400" />
            <span className="text-green-700 dark:text-green-300">{success}</span>
          </div>
        )}
      </div>

      {/* Settings Form */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Signup Bonus Settings */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
              <Gift className="w-5 h-5 text-green-600 dark:text-green-400" />
            </div>
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Signup Bonus
            </h2>
          </div>

          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <input
                type="checkbox"
                id="enableSignupBonus"
                checked={settings.enableSignupBonus}
                onChange={(e) => handleInputChange('enableSignupBonus', e.target.checked)}
                className="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500 dark:focus:ring-green-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
              />
              <label htmlFor="enableSignupBonus" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Enable signup bonus
              </label>
            </div>

            <div>
              <label htmlFor="signup-bonus-amount" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Bonus Amount ($)
              </label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-3 w-4 h-4 text-gray-400" />
                <input
                  id="signup-bonus-amount"
                  name="signupBonusAmount"
                  type="number"
                  step="0.01"
                  min="0"
                  max="100"
                  value={settings.signupBonus}
                  onChange={(e) => handleInputChange('signupBonus', parseFloat(e.target.value) || 0)}
                  disabled={!settings.enableSignupBonus}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white disabled:opacity-50 disabled:cursor-not-allowed"
                  placeholder="0.00"
                />
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Amount credited to new users when they sign up
              </p>
            </div>
          </div>
        </div>

        {/* Referral Bonus Settings */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
              <Users className="w-5 h-5 text-blue-600 dark:text-blue-400" />
            </div>
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
              Referral Bonus
            </h2>
          </div>

          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <input
                type="checkbox"
                id="enableReferralBonus"
                checked={settings.enableReferralBonus}
                onChange={(e) => handleInputChange('enableReferralBonus', e.target.checked)}
                className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600"
              />
              <label htmlFor="enableReferralBonus" className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Enable referral bonuses
              </label>
            </div>

            <div>
              <label htmlFor="referral-bonus-amount" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Bonus Amount ($)
              </label>
              <div className="relative">
                <DollarSign className="absolute left-3 top-3 w-4 h-4 text-gray-400" />
                <input
                  id="referral-bonus-amount"
                  name="referralBonusAmount"
                  type="number"
                  step="0.01"
                  min="0"
                  max="100"
                  value={settings.referralBonus}
                  onChange={(e) => handleInputChange('referralBonus', parseFloat(e.target.value) || 0)}
                  disabled={!settings.enableReferralBonus}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white disabled:opacity-50 disabled:cursor-not-allowed"
                  placeholder="0.00"
                />
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                Amount credited to both referrer and new user
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Save Changes
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Apply the new wallet settings to the system
            </p>
          </div>
          <button
            onClick={saveSettings}
            disabled={isSaving}
            className="flex items-center space-x-2 px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isSaving ? (
              <>
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                <span>Saving...</span>
              </>
            ) : (
              <>
                <Save className="w-4 h-4" />
                <span>Save Settings</span>
              </>
            )}
          </button>
        </div>
      </div>

      {/* Information */}
      <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6">
        <div className="flex items-start space-x-3">
          <Settings className="w-5 h-5 text-blue-600 dark:text-blue-400 mt-0.5" />
          <div>
            <h3 className="text-sm font-medium text-blue-900 dark:text-blue-300 mb-2">
              Important Information
            </h3>
            <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
              <li>• Changes take effect immediately for new users and referrals</li>
              <li>• Existing wallet balances are not affected by these changes</li>
              <li>• Signup bonuses are only given to new users after enabling</li>
              <li>• Referral bonuses require both users to be eligible</li>
              <li>• All wallet credits are non-withdrawable and can only be used for purchases</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminWalletSettings;

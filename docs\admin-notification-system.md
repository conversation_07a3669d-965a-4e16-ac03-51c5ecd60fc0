# Admin Notification System

A comprehensive real-time notification system for Hive Campus administrators to monitor all platform activity and system events.

## Overview

The admin notification system provides real-time visibility into all critical user actions and system events through:

- **Real-time notifications** in the admin dashboard header (bell icon)
- **Dropdown view** showing latest 30 notifications with unread count
- **Full-page view** with filtering, search, and bulk actions
- **Automatic triggers** from Firestore events and Stripe webhooks
- **Secure access** restricted to admin users only

## Features

### 🔔 Real-time Notifications
- Live updates via Firestore onSnapshot listeners
- Red badge showing unread count
- Dropdown with latest notifications
- Relative timestamps (e.g., "3 mins ago")
- Click to navigate to relevant admin section

### 📋 Full Notification Management
- Dedicated notifications page (`/admin/notifications`)
- Search and filter capabilities
- Bulk mark as read operations
- Detailed notification metadata
- Direct links to relevant admin actions

### 🎯 Comprehensive Event Tracking
- **User Activities**: Signups, profile updates
- **Marketplace**: Listing creation, sales, updates
- **Orders**: New orders, status changes
- **Payments**: Completions, failures, refunds
- **Wallet**: Balance additions, usage
- **Shipping**: Label creation, tracking updates
- **Escrow**: Fund releases, secret code verification
- **Support**: Feedback submissions, issue reports
- **System**: Errors, maintenance events

## Architecture

### Frontend Components

```
src/components/admin/
├── AdminNotifications.tsx          # Header dropdown component
├── pages/AdminNotificationsPage.tsx # Full page view
└── AdminNotificationTest.tsx       # Development testing tool

src/hooks/
└── useAdminNotifications.ts        # Real-time data hook

src/services/
└── adminNotificationService.ts     # Service layer

src/types/
└── admin-notifications.ts          # TypeScript definitions
```

### Backend Functions

```
functions/src/
├── admin-notifications.ts          # Firestore triggers
└── index.ts                        # Webhook integrations
```

### Database Schema

**Collection**: `admin_notifications`

```typescript
{
  id: string;
  type: AdminNotificationType;
  title: string;
  message: string;
  icon: string;                     // Emoji or icon key
  userId?: string;                  // User who triggered action
  username?: string;
  orderId?: string;
  listingId?: string;
  amount?: number;
  metadata?: Record<string, any>;   // Additional context
  actionUrl?: string;               // Link to admin page
  createdAt: Timestamp;
  read: boolean;
}
```

## Notification Types

| Type | Trigger | Icon | Priority |
|------|---------|------|----------|
| `user_signup` | New user registration | 👋 | Low |
| `listing_created` | New marketplace listing | 📦 | Low |
| `listing_sold` | Listing marked as sold | 💰 | Medium |
| `order_created` | New order placed | 🛒 | Medium |
| `payment_completed` | Successful payment | ✅ | Medium |
| `payment_failed` | Failed payment | ❌ | High |
| `feedback_submitted` | User feedback | 💬 | Low |
| `user_issue` | Issue reported | 🚨 | High |
| `wallet_used` | Wallet balance used | 💳 | Low |
| `wallet_added` | Wallet credit added | 💰 | Low |
| `shipping_label_created` | Shipping label | 📮 | Medium |
| `secret_code_verified` | Delivery confirmed | 🔓 | Medium |
| `escrow_released` | Funds released | 🏦 | Medium |
| `dispute_created` | Support dispute | ⚠️ | High |
| `system_error` | System error | ⚡ | High |

## Implementation

### 1. Firestore Security Rules

```javascript
// Admin notifications collection
match /admin_notifications/{notificationId} {
  // Only admins can read or write admin notifications
  allow read, write: if isAdmin();
}
```

### 2. Firebase Functions

**Automatic Triggers**:
- `onUserCreate` - New user signups
- `onListingCreate` - New listings
- `onListingUpdate` - Listing changes
- `onOrderCreate` - New orders
- `onFeedbackCreate` - Feedback submissions
- `onIssueCreate` - Issue reports
- `onWalletTransactionCreate` - Wallet activities
- `onShippingLabelCreate` - Shipping labels

**Webhook Integration**:
- Stripe `checkout.session.completed`
- Stripe `payment_intent.payment_failed`

### 3. Frontend Integration

**Header Component**:
```tsx
import AdminNotifications from './AdminNotifications';

// In AdminHeader.tsx
<AdminNotifications />
```

**Hook Usage**:
```tsx
const {
  notifications,
  unreadCount,
  markAsRead,
  markAllAsRead
} = useAdminNotifications({ limit: 30 });
```

## Development & Testing

### Test Component
Access the notification test interface at `/admin/notification-test` (development only):

- Create sample notifications
- Test different notification types
- Verify real-time updates
- Test marking as read/unread

### Seeding Data
```typescript
import { seedAdminNotifications } from '../utils/seedAdminNotifications';

// Create sample notifications for testing
await seedAdminNotifications();
```

## Deployment

### 1. Deploy Firebase Functions
```bash
cd functions
npm run build
firebase deploy --only functions
```

### 2. Update Firestore Rules
```bash
firebase deploy --only firestore:rules
```

### 3. Frontend Build
```bash
npm run build
firebase deploy --only hosting
```

## Configuration

### Environment Variables
No additional environment variables required - uses existing Firebase configuration.

### Customization
- **Icons**: Update `NOTIFICATION_CONFIG` in `admin-notifications.ts`
- **Colors**: Modify color mapping in components
- **Priorities**: Adjust priority levels for different notification types
- **Filters**: Add custom filter options in the full page view

## Security

- **Admin Only**: All notification access restricted to admin users
- **Firestore Rules**: Enforced at database level
- **Authentication**: Verified through Firebase Auth custom claims
- **Data Isolation**: Admin notifications separate from user notifications

## Performance

- **Real-time Updates**: Efficient Firestore listeners with automatic cleanup
- **Pagination**: Limited queries (default 30 notifications)
- **Caching**: Browser-level caching for notification counts
- **Optimistic Updates**: Immediate UI updates for better UX

## Monitoring

- **Function Logs**: Monitor Firebase Functions logs for trigger execution
- **Error Handling**: Graceful degradation if notification creation fails
- **Metrics**: Track notification creation rates and admin engagement

## Future Enhancements

- **Sound Notifications**: Optional audio alerts for high-priority notifications
- **Email Digest**: Daily/weekly email summaries for admins
- **Notification Categories**: Group notifications by functional area
- **Custom Filters**: Save and reuse filter combinations
- **Notification Templates**: Predefined templates for common scenarios
- **Analytics Dashboard**: Notification trends and admin response times

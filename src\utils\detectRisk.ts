/**
 * Risk Detection Utility for Hive Campus
 *
 * Detects and blocks off-platform payment methods, contact information,
 * and URLs to ensure all transactions remain within the platform.
 *
 * This helps prevent fraud and maintains platform safety policies
 * similar to Depop, Mercari, Facebook Marketplace, and eBay.
 */

import { collection, addDoc, serverTimestamp } from 'firebase/firestore';
import { firestore } from '../firebase/config';
import { auth } from '../firebase/config';

// Payment method keywords (case-insensitive)
const PAYMENT_KEYWORDS = [
  'venmo',
  'paypal',
  'cashapp',
  'cash app',
  'zelle',
  'google pay',
  'gpay',
  'apple pay',
  'chime',
  'phonepe',
  'paytm',
  'western union',
  'moneygram',
  'bitcoin',
  'btc',
  'crypto',
  'ethereum',
  'eth',
  'coinbase',
  'binance',
  'robinhood',
  'square cash',
  'facebook pay',
  'meta pay',
  'skrill',
  'neteller',
  'wise',
  'transferwise',
  'remitly',
  'worldremit'
];

// Common obfuscation patterns for payment methods
const OBFUSCATED_PATTERNS = [
  // Venmo variations
  /v[3e]nm[0o]/gi,
  /v[e3][n][m][o0]/gi,
  
  // PayPal variations
  /p[@a]yp[@a]l/gi,
  /p[a@][y][p][a@][l]/gi,
  /p[4@]yp[4@]l/gi,
  
  // CashApp variations
  /c[@a][s$][h][a@]pp/gi,
  /c[a@][s$]h[\s]*[a@]pp/gi,
  /c[4@][s$][h][\s]*[4@]pp/gi,
  
  // Google Pay variations
  /g[o0@][o0@]gle[\s]*p[a@]y/gi,
  /g[p@]y/gi,
  
  // Apple Pay variations
  /[a@]pple[\s]*p[a@]y/gi,
  /[a@]pp1e[\s]*p[a@]y/gi,
  
  // Chime variations
  /ch[i1!]me/gi,
  /ch[i1!][m][e3]/gi,
  
  // Zelle variations
  /z[e3]ll[e3]/gi,
  /z[3e][l1][l1][3e]/gi
];

// Phone number patterns (US formats)
const PHONE_PATTERNS = [
  // Standard formats: (*************, ************, ************
  /\(?\d{3}\)?[-.\s]?\d{3}[-.\s]?\d{4}/g,
  
  // International format: *************
  /\+1\s?\d{10}/g,
  
  // 10+ consecutive digits
  /\b\d{10,}\b/g,
  
  // Obfuscated phone numbers
  /\d{3}[-.\s]*\d{3}[-.\s]*\d{4}/g,
  
  // Phone with text
  /(?:phone|call|text|mobile|cell)[\s:]*\d{3}[-.\s]*\d{3}[-.\s]*\d{4}/gi
];

// Email patterns
const EMAIL_PATTERNS = [
  // Standard email format
  /[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/g,
  
  // Obfuscated emails
  /[a-zA-Z0-9._%+-]+[@\s]*[a-zA-Z0-9.-]+[\s]*\.[\s]*[a-zA-Z]{2,}/g,
  
  // Email with spaces or symbols
  /[a-zA-Z0-9._%+-]+\s*[@\s]*\s*[a-zA-Z0-9.-]+\s*[.\s]*\s*(?:com|edu|net|org|gov)/gi
];

// URL patterns
const URL_PATTERNS = [
  // Standard URLs
  /https?:\/\/[^\s]+/gi,
  
  // www. domains
  /www\.[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}/gi,
  
  // Common domains without protocol
  /[a-zA-Z0-9.-]+\.(?:com|net|org|edu|gov|io|co|me|tv|info|biz)/gi,
  
  // Obfuscated URLs
  /[a-zA-Z0-9.-]+[\s]*\.[\s]*(?:com|net|org|edu|gov)/gi
];

// Social media and messaging app patterns
const SOCIAL_PATTERNS = [
  /(?:instagram|insta|ig)[\s:]*[@]?[a-zA-Z0-9._]+/gi,
  /(?:snapchat|snap)[\s:]*[@]?[a-zA-Z0-9._]+/gi,
  /(?:telegram|tg)[\s:]*[@]?[a-zA-Z0-9._]+/gi,
  /(?:whatsapp|whats app)[\s:]*\+?\d+/gi,
  /(?:discord)[\s:]*[a-zA-Z0-9._#]+/gi,
  /(?:kik|wickr)[\s:]*[a-zA-Z0-9._]+/gi
];

/**
 * Main function to detect risky content in user input
 *
 * @param input - The text to analyze (message or listing content)
 * @returns boolean - true if risky content is detected
 */
export function isRiskyMessage(input: string): boolean {
  if (!input || typeof input !== 'string') {
    return false;
  }

  // Normalize input: convert to lowercase and remove extra spaces
  const normalizedInput = input.toLowerCase().trim();

  // Whitelist legitimate shipping/delivery terms to avoid false positives
  const legitimateShippingTerms = [
    'buyer pays',
    'seller pays',
    'shipping fee',
    'delivery method',
    'in-person',
    'mail delivery',
    'package size',
    'hive shipping',
    'shippo',
    'shipping cost',
    'free shipping',
    'shipping address',
    'tracking number'
  ];

  // Check if the input contains only legitimate shipping terms
  const containsOnlyLegitimateTerms = legitimateShippingTerms.some(term =>
    normalizedInput.includes(term)
  );

  // If it contains legitimate shipping terms, check if it's ONLY those terms
  if (containsOnlyLegitimateTerms) {
    // Allow if it's clearly about shipping/delivery and not payment methods
    const isShippingContext = legitimateShippingTerms.some(term =>
      normalizedInput.includes(term)
    );

    if (isShippingContext) {
      // Still check for specific risky patterns, but be more lenient
      for (const keyword of PAYMENT_KEYWORDS) {
        // Skip "pay" if it's in shipping context
        if (keyword === 'pay' && (normalizedInput.includes('buyer pays') || normalizedInput.includes('seller pays') || normalizedInput.includes('shipping'))) {
          continue;
        }
        if (normalizedInput.includes(keyword.toLowerCase())) {
          return true;
        }
      }
      return false; // Allow shipping-related content
    }
  }

  // Check for payment method keywords
  for (const keyword of PAYMENT_KEYWORDS) {
    if (normalizedInput.includes(keyword.toLowerCase())) {
      return true;
    }
  }
  
  // Check for obfuscated payment patterns
  for (const pattern of OBFUSCATED_PATTERNS) {
    if (pattern.test(input)) {
      return true;
    }
  }
  
  // Check for phone numbers
  for (const pattern of PHONE_PATTERNS) {
    if (pattern.test(input)) {
      return true;
    }
  }
  
  // Check for email addresses
  for (const pattern of EMAIL_PATTERNS) {
    if (pattern.test(input)) {
      return true;
    }
  }
  
  // Check for URLs
  for (const pattern of URL_PATTERNS) {
    if (pattern.test(input)) {
      return true;
    }
  }
  
  // Check for social media handles
  for (const pattern of SOCIAL_PATTERNS) {
    if (pattern.test(input)) {
      return true;
    }
  }
  
  return false;
}

/**
 * Get specific risk type for logging purposes
 *
 * @param input - The text to analyze
 * @returns string - The type of risk detected
 */
export function getRiskType(input: string): string | null {
  if (!input || typeof input !== 'string') {
    return null;
  }

  const normalizedInput = input.toLowerCase().trim();

  // Whitelist legitimate shipping/delivery terms
  const legitimateShippingTerms = [
    'buyer pays',
    'seller pays',
    'shipping fee',
    'delivery method',
    'in-person',
    'mail delivery',
    'package size',
    'hive shipping',
    'shippo',
    'shipping cost',
    'free shipping',
    'shipping address',
    'tracking number'
  ];

  // Check if it's shipping context
  const isShippingContext = legitimateShippingTerms.some(term =>
    normalizedInput.includes(term)
  );

  // Check payment methods first (highest priority)
  for (const keyword of PAYMENT_KEYWORDS) {
    // Skip "pay" if it's in shipping context
    if (keyword === 'pay' && isShippingContext && (normalizedInput.includes('buyer pays') || normalizedInput.includes('seller pays') || normalizedInput.includes('shipping'))) {
      continue;
    }
    if (normalizedInput.includes(keyword.toLowerCase())) {
      return 'payment_method';
    }
  }
  
  for (const pattern of OBFUSCATED_PATTERNS) {
    if (pattern.test(input)) {
      return 'obfuscated_payment';
    }
  }
  
  // Check contact information
  for (const pattern of PHONE_PATTERNS) {
    if (pattern.test(input)) {
      return 'phone_number';
    }
  }
  
  for (const pattern of EMAIL_PATTERNS) {
    if (pattern.test(input)) {
      return 'email_address';
    }
  }
  
  // Check URLs and social media
  for (const pattern of URL_PATTERNS) {
    if (pattern.test(input)) {
      return 'url';
    }
  }
  
  for (const pattern of SOCIAL_PATTERNS) {
    if (pattern.test(input)) {
      return 'social_media';
    }
  }
  
  return null;
}

/**
 * Get user-friendly error message based on risk type
 *
 * @param riskType - The type of risk detected
 * @returns string - User-friendly error message
 */
export function getRiskErrorMessage(riskType: string | null): string {
  switch (riskType) {
    case 'payment_method':
    case 'obfuscated_payment':
      return '⚠️ Off-platform payment methods are not allowed. Please use Hive Campus Checkout for safe transactions.';

    case 'phone_number':
      return '⚠️ Phone numbers are not allowed. Please communicate through Hive Campus messaging.';

    case 'email_address':
      return '⚠️ Email addresses are not allowed. Please use Hive Campus messaging to stay protected.';

    case 'url':
      return '⚠️ External links are not allowed. Keep all communication within Hive Campus.';

    case 'social_media':
      return '⚠️ Social media handles are not allowed. Please use Hive Campus messaging.';

    default:
      return '⚠️ This content violates our platform policies. Please remove personal contact information or payment details.';
  }
}

/**
 * Log a violation to Firestore for monitoring and analysis
 *
 * @param type - The type of violation ('chat' or 'listing')
 * @param content - The content that triggered the violation
 * @param riskType - The specific type of risk detected
 * @param additionalContext - Optional additional context (e.g., listing ID, chat ID)
 */
export async function logViolation(
  type: 'chat' | 'listing',
  content: string,
  riskType: string | null,
  additionalContext?: {
    listingId?: string;
    chatId?: string;
    listingTitle?: string;
    recipientId?: string;
  }
): Promise<void> {
  try {
    const currentUser = auth.currentUser;

    if (!currentUser) {
      console.warn('Cannot log violation: user not authenticated');
      return;
    }

    const violationData = {
      uid: currentUser.uid,
      type,
      content: content.substring(0, 500), // Limit content length for storage
      riskType: riskType || 'unknown',
      timestamp: serverTimestamp(),
      userAgent: navigator.userAgent,
      url: window.location.href,
      ...additionalContext
    };

    await addDoc(collection(firestore, 'violations'), violationData);

    console.log('Violation logged:', {
      type,
      riskType,
      userId: currentUser.uid,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Failed to log violation:', error);
    // Don't throw error to avoid disrupting user experience
  }
}

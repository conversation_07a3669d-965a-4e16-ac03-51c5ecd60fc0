import React from 'react';
import { formatDistanceToNow } from 'date-fns';
import { 
  Bell, 
  Package, 
  CreditCard, 
  MessageCircle, 
  AlertTriangle, 
  CheckCircle, 
  Clock,
  ExternalLink,
  MoreHorizontal
} from 'lucide-react';
import { UserNotification, NotificationType } from '../../types/notifications';

interface NotificationsDropdownProps {
  notifications: UserNotification[];
  onNotificationClick: (notificationId: string, link?: string) => void;
  onClose: () => void;
  className?: string;
}

// Icon mapping for different notification types
const getNotificationIcon = (type: NotificationType) => {
  switch (type) {
    case 'listing_sold':
    case 'order_confirmed':
    case 'order_delivered':
      return Package;
    case 'wallet_credited':
    case 'wallet_debited':
    case 'payment_success':
    case 'payment_failed':
      return CreditCard;
    case 'new_chat_message':
      return MessageCircle;
    case 'user_warning':
    case 'admin_warning':
      return AlertTriangle;
    case 'delivery_confirmation':
      return CheckCircle;
    case '48_hour_shipping_reminder':
      return Clock;
    case 'platform_announcement':
    case 'admin_broadcast':
      return Bell;
    default:
      return Bell;
  }
};

// Color mapping for different notification types
const getNotificationColor = (type: NotificationType, priority?: string) => {
  if (priority === 'urgent') return 'text-red-500';
  if (priority === 'high') return 'text-orange-500';
  
  switch (type) {
    case 'listing_sold':
    case 'order_confirmed':
    case 'wallet_credited':
    case 'payment_success':
    case 'delivery_confirmation':
      return 'text-green-500';
    case 'payment_failed':
    case 'user_warning':
    case 'admin_warning':
      return 'text-red-500';
    case 'new_chat_message':
      return 'text-blue-500';
    case '48_hour_shipping_reminder':
      return 'text-yellow-500';
    case 'platform_announcement':
    case 'admin_broadcast':
      return 'text-purple-500';
    default:
      return 'text-gray-500';
  }
};

export const NotificationsDropdown: React.FC<NotificationsDropdownProps> = ({
  notifications,
  onNotificationClick,
  onClose,
  className = ''
}) => {
  const handleNotificationClick = (notification: UserNotification) => {
    onNotificationClick(notification.id, notification.link);
  };

  const handleViewAll = () => {
    window.location.href = '/notifications';
    onClose();
  };

  return (
    <div className={`w-80 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 ${className}`}>
      {/* Header */}
      <div className="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Notifications
          </h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 transition-colors"
            aria-label="Close notifications"
          >
            <MoreHorizontal className="h-5 w-5" />
          </button>
        </div>
      </div>

      {/* Notifications List */}
      <div className="max-h-96 overflow-y-auto">
        {notifications.length === 0 ? (
          <div className="px-4 py-8 text-center">
            <Bell className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-3" />
            <p className="text-gray-500 dark:text-gray-400 text-sm">
              No notifications yet
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {notifications.map((notification) => {
              const Icon = getNotificationIcon(notification.type);
              const iconColor = getNotificationColor(notification.type, notification.priority);
              const timeAgo = formatDistanceToNow(notification.createdAt.toDate(), { addSuffix: true });

              return (
                <div
                  key={notification.id}
                  onClick={() => handleNotificationClick(notification)}
                  className={`
                    px-4 py-3 cursor-pointer transition-colors hover:bg-gray-50 dark:hover:bg-gray-700
                    ${!notification.read ? 'bg-blue-50 dark:bg-blue-900/20' : ''}
                  `}
                >
                  <div className="flex items-start space-x-3">
                    {/* Icon */}
                    <div className={`flex-shrink-0 ${iconColor}`}>
                      <Icon className="h-5 w-5" />
                    </div>

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <p className={`text-sm font-medium ${!notification.read ? 'text-gray-900 dark:text-white' : 'text-gray-700 dark:text-gray-300'}`}>
                            {notification.title}
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-400 mt-1 line-clamp-2">
                            {notification.message}
                          </p>
                          
                          {/* Additional info for specific types */}
                          {notification.amount && (
                            <p className="text-xs text-green-600 dark:text-green-400 mt-1 font-medium">
                              ${notification.amount.toFixed(2)}
                            </p>
                          )}
                          
                          {notification.secretCode && (
                            <p className="text-xs text-blue-600 dark:text-blue-400 mt-1 font-mono">
                              Code: {notification.secretCode}
                            </p>
                          )}
                        </div>

                        {/* Link indicator */}
                        {notification.link && (
                          <ExternalLink className="h-3 w-3 text-gray-400 flex-shrink-0 ml-2" />
                        )}
                      </div>

                      {/* Timestamp and priority */}
                      <div className="flex items-center justify-between mt-2">
                        <p className="text-xs text-gray-400 dark:text-gray-500">
                          {timeAgo}
                        </p>
                        
                        {notification.priority && notification.priority !== 'normal' && (
                          <span className={`
                            text-xs px-2 py-1 rounded-full font-medium
                            ${notification.priority === 'urgent' ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400' : ''}
                            ${notification.priority === 'high' ? 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-400' : ''}
                            ${notification.priority === 'low' ? 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400' : ''}
                          `}>
                            {notification.priority}
                          </span>
                        )}
                      </div>

                      {/* Unread indicator */}
                      {!notification.read && (
                        <div className="absolute left-2 top-1/2 transform -translate-y-1/2 w-2 h-2 bg-blue-500 rounded-full"></div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </div>

      {/* Footer */}
      {notifications.length > 0 && (
        <div className="px-4 py-3 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={handleViewAll}
            className="w-full text-center text-sm text-primary-600 hover:text-primary-700 dark:text-primary-400 dark:hover:text-primary-300 font-medium transition-colors"
          >
            View all notifications
          </button>
        </div>
      )}
    </div>
  );
};

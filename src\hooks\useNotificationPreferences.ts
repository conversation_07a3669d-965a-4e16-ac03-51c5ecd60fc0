import { useState, useEffect, useCallback } from 'react';
import { doc, getDoc, setDoc, onSnapshot } from 'firebase/firestore';
import { firestore } from '../firebase/config';
import { useAuth } from './useAuth';
import { 
  NotificationPreferences, 
  DEFAULT_NOTIFICATION_PREFERENCES,
  NotificationType,
  NotificationChannel
} from '../types/notifications';

interface UseNotificationPreferencesState {
  preferences: NotificationPreferences;
  isLoading: boolean;
  error: string | null;
}

interface UseNotificationPreferencesActions {
  updatePreferences: (updates: Partial<NotificationPreferences>) => Promise<void>;
  togglePushNotifications: () => Promise<void>;
  toggleEmailNotifications: () => Promise<void>;
  togglePauseAll: () => Promise<void>;
  muteCategory: (type: NotificationType) => Promise<void>;
  unmuteCategory: (type: NotificationType) => Promise<void>;
  toggleChannelForType: (channel: NotificationChannel, type: NotificationType) => Promise<void>;
  resetToDefaults: () => Promise<void>;
}

export const useNotificationPreferences = (): UseNotificationPreferencesState & UseNotificationPreferencesActions => {
  const { user } = useAuth();
  const [state, setState] = useState<UseNotificationPreferencesState>({
    preferences: DEFAULT_NOTIFICATION_PREFERENCES,
    isLoading: true,
    error: null
  });

  // Real-time listener for preferences
  useEffect(() => {
    if (!user) {
      setState({
        preferences: DEFAULT_NOTIFICATION_PREFERENCES,
        isLoading: false,
        error: null
      });
      return;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    const preferencesRef = doc(firestore, `users/${user.uid}`);
    
    const unsubscribe = onSnapshot(
      preferencesRef,
      (snapshot) => {
        try {
          if (snapshot.exists()) {
            const userData = snapshot.data();
            const preferences = userData.preferences?.notifications || DEFAULT_NOTIFICATION_PREFERENCES;
            
            // Merge with defaults to ensure all fields are present
            const mergedPreferences: NotificationPreferences = {
              ...DEFAULT_NOTIFICATION_PREFERENCES,
              ...preferences,
              channels: {
                ...DEFAULT_NOTIFICATION_PREFERENCES.channels,
                ...preferences.channels
              }
            };

            setState({
              preferences: mergedPreferences,
              isLoading: false,
              error: null
            });
          } else {
            // User document doesn't exist, create with defaults
            initializePreferences();
          }
        } catch (error) {
          console.error('Error processing notification preferences:', error);
          setState(prev => ({
            ...prev,
            isLoading: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          }));
        }
      },
      (error) => {
        console.error('Error listening to notification preferences:', error);
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: error.message
        }));
      }
    );

    return () => unsubscribe();
  }, [user]);

  // Initialize preferences for new users
  const initializePreferences = useCallback(async () => {
    if (!user) return;

    try {
      await setDoc(
        doc(firestore, `users/${user.uid}`),
        {
          preferences: {
            notifications: DEFAULT_NOTIFICATION_PREFERENCES
          }
        },
        { merge: true }
      );
    } catch (error) {
      console.error('Error initializing notification preferences:', error);
    }
  }, [user]);

  // Update preferences
  const updatePreferences = useCallback(async (updates: Partial<NotificationPreferences>): Promise<void> => {
    if (!user) return;

    try {
      const updatedPreferences = {
        ...state.preferences,
        ...updates
      };

      await setDoc(
        doc(firestore, `users/${user.uid}`),
        {
          preferences: {
            notifications: updatedPreferences
          }
        },
        { merge: true }
      );
    } catch (error) {
      console.error('Error updating notification preferences:', error);
      throw error;
    }
  }, [user, state.preferences]);

  // Toggle push notifications
  const togglePushNotifications = useCallback(async (): Promise<void> => {
    await updatePreferences({
      enable_push: !state.preferences.enable_push
    });
  }, [state.preferences.enable_push, updatePreferences]);

  // Toggle email notifications
  const toggleEmailNotifications = useCallback(async (): Promise<void> => {
    await updatePreferences({
      enable_email: !state.preferences.enable_email
    });
  }, [state.preferences.enable_email, updatePreferences]);

  // Toggle pause all notifications
  const togglePauseAll = useCallback(async (): Promise<void> => {
    await updatePreferences({
      pause_all: !state.preferences.pause_all
    });
  }, [state.preferences.pause_all, updatePreferences]);

  // Mute a specific notification category
  const muteCategory = useCallback(async (type: NotificationType): Promise<void> => {
    const currentMuted = state.preferences.muted_categories;
    if (!currentMuted.includes(type)) {
      await updatePreferences({
        muted_categories: [...currentMuted, type]
      });
    }
  }, [state.preferences.muted_categories, updatePreferences]);

  // Unmute a specific notification category
  const unmuteCategory = useCallback(async (type: NotificationType): Promise<void> => {
    const currentMuted = state.preferences.muted_categories;
    await updatePreferences({
      muted_categories: currentMuted.filter(t => t !== type)
    });
  }, [state.preferences.muted_categories, updatePreferences]);

  // Toggle channel for specific notification type
  const toggleChannelForType = useCallback(async (
    channel: NotificationChannel, 
    type: NotificationType
  ): Promise<void> => {
    const currentChannelPrefs = state.preferences.channels[channel];
    const isCurrentlyEnabled = currentChannelPrefs.types.includes(type);

    const updatedTypes = isCurrentlyEnabled
      ? currentChannelPrefs.types.filter(t => t !== type)
      : [...currentChannelPrefs.types, type];

    await updatePreferences({
      channels: {
        ...state.preferences.channels,
        [channel]: {
          ...currentChannelPrefs,
          types: updatedTypes
        }
      }
    });
  }, [state.preferences.channels, updatePreferences]);

  // Reset to default preferences
  const resetToDefaults = useCallback(async (): Promise<void> => {
    await updatePreferences(DEFAULT_NOTIFICATION_PREFERENCES);
  }, [updatePreferences]);

  return {
    ...state,
    updatePreferences,
    togglePushNotifications,
    toggleEmailNotifications,
    togglePauseAll,
    muteCategory,
    unmuteCategory,
    toggleChannelForType,
    resetToDefaults
  };
};

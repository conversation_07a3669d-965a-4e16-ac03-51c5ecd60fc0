import * as functions from 'firebase-functions/v1';
import * as admin from 'firebase-admin';

// Simple admin notification creation function
export const createAdminNotification = functions.https.onCall(async (data, context) => {
  // Check if user is admin (you can implement your admin check here)
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  try {
    const notification = {
      type: data.type || 'system',
      title: data.title || 'Test Notification',
      message: data.message || 'This is a test notification',
      icon: data.icon || '📢',
      userId: data.userId || null,
      username: data.username || null,
      read: false,
      createdAt: admin.firestore.FieldValue.serverTimestamp()
    };

    const docRef = await admin.firestore().collection('admin_notifications').add(notification);
    
    return { success: true, id: docRef.id };
  } catch (error) {
    console.error('Error creating admin notification:', error);
    throw new functions.https.HttpsError('internal', 'Failed to create notification');
  }
});

// Simple function to test the system
export const testAdminNotifications = functions.https.onCall(async (data, context) => {
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  try {
    // Create a few test notifications
    const notifications = [
      {
        type: 'user_signup',
        title: 'New User Signup',
        message: 'Test User has joined the platform',
        icon: '👋',
        username: 'Test User',
        read: false,
        createdAt: admin.firestore.FieldValue.serverTimestamp()
      },
      {
        type: 'payment_completed',
        title: 'Payment Completed',
        message: 'Test payment of $50 completed successfully',
        icon: '✅',
        username: 'Test Buyer',
        amount: 50,
        read: false,
        createdAt: admin.firestore.FieldValue.serverTimestamp()
      },
      {
        type: 'listing_created',
        title: 'New Listing Created',
        message: 'Test Seller created a new listing: Test Item',
        icon: '📦',
        username: 'Test Seller',
        amount: 25,
        read: false,
        createdAt: admin.firestore.FieldValue.serverTimestamp()
      }
    ];

    const batch = admin.firestore().batch();
    const collection = admin.firestore().collection('admin_notifications');

    notifications.forEach((notification) => {
      const docRef = collection.doc();
      batch.set(docRef, notification);
    });

    await batch.commit();

    return { success: true, created: notifications.length };
  } catch (error) {
    console.error('Error creating test notifications:', error);
    throw new functions.https.HttpsError('internal', 'Failed to create test notifications');
  }
});

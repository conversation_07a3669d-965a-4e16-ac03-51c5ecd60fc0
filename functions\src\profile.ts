// Profile management functions
import * as functions from 'firebase-functions/v1';
import * as admin from 'firebase-admin';
import cors from 'cors';

const corsHandler = cors({ origin: true });

// Function to update user profile with proper validation
export const updateProfile = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 60
  })
  .https.onRequest(async (req, res) => {
    corsHandler(req, res, async () => {
      try {
        if (req.method !== 'POST') {
          res.status(405).json({ error: 'Method not allowed' });
          return;
        }

        // Get the ID token from the Authorization header
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
          res.status(401).json({ error: 'Unauthorized' });
          return;
        }

        const idToken = authHeader.split('Bearer ')[1];
        const decodedToken = await admin.auth().verifyIdToken(idToken);
        const uid = decodedToken.uid;
        const { name, bio, graduationYear, major, university } = req.body;

        // Validate required fields
        if (!name || !name.trim()) {
          res.status(400).json({ error: 'Name is required' });
          return;
        }

        // Prepare update data
        const updateData: any = {
          name: name.trim(),
          updatedAt: admin.firestore.Timestamp.now()
        };

        if (bio !== undefined) updateData.bio = bio.trim();
        if (major !== undefined) updateData.major = major.trim();
        if (university !== undefined) updateData.university = university.trim();

        if (graduationYear !== undefined) {
          const year = parseInt(graduationYear);
          if (year >= 2020 && year <= 2035) {
            updateData.graduationYear = year;
          }
        }

        // Update user document
        await admin.firestore().collection('users').doc(uid).update(updateData);

        // Update Auth displayName
        await admin.auth().updateUser(uid, {
          displayName: name.trim()
        });

        res.json({
          success: true,
          message: 'Profile updated successfully',
          data: updateData
        });
      } catch (error) {
        console.error('Error updating profile:', error);
        res.status(500).json({ error: 'Failed to update profile' });
      }
    });
  });

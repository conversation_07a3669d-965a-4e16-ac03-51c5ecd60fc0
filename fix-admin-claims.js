// Direct Node.js script to fix admin user
// Run with: node fix-admin-claims.js

const admin = require('firebase-admin');

// Initialize Firebase Admin SDK
// This will use the default credentials from your Firebase CLI login
admin.initializeApp({
  projectId: 'h1c1-798a8'
});

async function fixAdminUser() {
  try {
    const adminEmail = '<EMAIL>';
    const adminUID = 'CrFp8zLvGof9FkzDLz7wodmFmEy2'; // From the users.json export
    
    console.log('🔧 Starting admin user fix...');
    console.log('📧 Admin Email:', adminEmail);
    console.log('🆔 Admin UID:', adminUID);
    
    // Step 1: Set custom claims (the secure way)
    console.log('\n📝 Setting custom claims...');
    await admin.auth().setCustomUserClaims(adminUID, {
      admin: true,
      role: 'admin'
    });
    console.log('✅ Custom claims set successfully!');
    
    // Step 2: Update Firestore document
    console.log('\n📄 Updating Firestore document...');
    await admin.firestore().collection('users').doc(adminUID).set({
      uid: adminUID,
      name: 'Admin User',
      email: adminEmail,
      role: 'admin',
      university: 'Hive Campus Admin',
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now(),
      emailVerified: true,
      status: 'active',
      adminLevel: 'super',
      permissions: ['user_management', 'content_moderation', 'analytics', 'system_settings', 'super_admin']
    }, { merge: true });
    console.log('✅ Firestore document updated!');
    
    // Step 3: Create missing collections to prevent permission errors
    console.log('\n📁 Creating missing collections...');
    const collections = [
      'reports', 
      'shippingLabels', 
      'walletReports', 
      'universityAnalytics', 
      'systemMetrics', 
      'adminLogs'
    ];
    
    for (const collectionName of collections) {
      await admin.firestore().collection(collectionName).doc('default').set({
        created: admin.firestore.Timestamp.now(),
        type: 'system',
        description: `Default document for ${collectionName} collection`
      });
    }
    console.log('✅ Missing collections created!');
    
    // Step 4: Verify the fix
    console.log('\n🔍 Verifying the fix...');
    const userRecord = await admin.auth().getUser(adminUID);
    console.log('📋 Custom Claims:', JSON.stringify(userRecord.customClaims, null, 2));
    
    const userDoc = await admin.firestore().collection('users').doc(adminUID).get();
    const userData = userDoc.data();
    console.log('📋 Firestore Role:', userData?.role);
    console.log('📋 Admin Level:', userData?.adminLevel);
    
    // Step 5: Success message
    console.log('\n🎉 ADMIN USER FIXED SUCCESSFULLY!');
    console.log('\n📋 Next Steps:');
    console.log('1. 🧹 Clear your browser cache completely');
    console.log('2. 🔐 Login with:', adminEmail);
    console.log('3. 🚀 Navigate to admin dashboard');
    console.log('4. ✅ All permission errors should be resolved');
    console.log('5. 🔒 Set up 8-digit PIN when ready');
    
    console.log('\n🔧 Technical Details:');
    console.log('- Custom claims are now set in Firebase Auth');
    console.log('- Firestore document has admin role');
    console.log('- Missing collections created');
    console.log('- Security rules will now recognize admin permissions');
    
    return { success: true };
    
  } catch (error) {
    console.error('\n❌ Error fixing admin user:', error);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Make sure you are logged into Firebase CLI: firebase login');
    console.log('2. Make sure you have admin permissions on the Firebase project');
    console.log('3. Check that the project ID is correct: h1c1-798a8');
    
    return { success: false, error: error.message };
  }
}

// Run the fix
console.log('🚀 Hive Campus Admin User Fix Tool');
console.log('=====================================');

fixAdminUser()
  .then((result) => {
    if (result.success) {
      console.log('\n✅ Script completed successfully!');
      process.exit(0);
    } else {
      console.log('\n❌ Script failed!');
      process.exit(1);
    }
  })
  .catch((error) => {
    console.error('\n💥 Unexpected error:', error);
    process.exit(1);
  });

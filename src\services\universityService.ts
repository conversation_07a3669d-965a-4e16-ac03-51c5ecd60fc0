import {
  collection,
  doc,
  getDocs,
  setDoc,
  updateDoc,
  query,
  where,
  orderBy,
  Timestamp
} from 'firebase/firestore';
import { firestore } from '../firebase/config';
import { User, Listing } from '../firebase/types';

export interface University {
  id: string;
  name: string;
  domain: string;
  shortName?: string;
  location?: {
    city: string;
    state: string;
    country: string;
  };
  isActive: boolean;
  userCount: number;
  listingCount: number;
  createdAt: Timestamp;
  updatedAt?: Timestamp;
  // Admin settings
  allowedEmailDomains: string[];
  requiresVerification: boolean;
  settings?: {
    allowPublicListings: boolean;
    allowCrossUniversityChat: boolean;
    moderationLevel: 'low' | 'medium' | 'high';
  };
}

export interface UniversityStats {
  totalUsers: number;
  activeUsers: number;
  totalListings: number;
  activeListings: number;
  recentActivity: {
    newUsersThisWeek: number;
    newListingsThisWeek: number;
    messagesThisWeek: number;
  };
}

/**
 * University Service for managing university data and operations
 */
export class UniversityService {
  
  /**
   * Get all universities with real-time stats
   */
  static async getUniversities(): Promise<University[]> {
    try {
      // Get all users and listings to calculate stats
      const [usersSnapshot, listingsSnapshot] = await Promise.all([
        getDocs(collection(firestore, 'users')),
        getDocs(collection(firestore, 'listings'))
      ]);

      const users = usersSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as User[];
      const listings = listingsSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as Listing[];

      // Start with comprehensive list of default universities
      const defaultUniversities = this.getDefaultUniversities();
      const universityMap = new Map<string, University>();

      // Add all default universities to the map
      defaultUniversities.forEach(uni => {
        universityMap.set(uni.name, uni);
      });

      // If no real data, return the default list
      if (users.length === 0 && listings.length === 0) {
        return Array.from(universityMap.values());
      }

      // Create stats map for real data
      const statsMap = new Map<string, {
        userCount: number;
        listingCount: number;
        domains: Set<string>;
      }>();

      // Process users
      users.forEach(user => {
        const uni = user.university;
        if (!uni || typeof uni !== 'string') return; // Skip if no university

        if (!statsMap.has(uni)) {
          statsMap.set(uni, { userCount: 0, listingCount: 0, domains: new Set() });
        }
        statsMap.get(uni)!.userCount++;

        // Extract domain from email
        if (user.email) {
          const domain = user.email.split('@')[1];
          if (domain) {
            statsMap.get(uni)!.domains.add(domain);
          }
        }
      });

      // Process listings
      listings.forEach(listing => {
        const uni = listing.university;
        if (!uni || typeof uni !== 'string') return; // Skip if no university

        if (!statsMap.has(uni)) {
          statsMap.set(uni, { userCount: 0, listingCount: 0, domains: new Set() });
        }
        statsMap.get(uni)!.listingCount++;
      });

      // Update default universities with real stats
      statsMap.forEach((stats, universityName) => {
        const existingUni = universityMap.get(universityName);
        if (existingUni) {
          // Update existing university with real stats
          existingUni.userCount = stats.userCount;
          existingUni.listingCount = stats.listingCount;
          if (stats.domains.size > 0) {
            existingUni.allowedEmailDomains = Array.from(stats.domains);
            existingUni.domain = Array.from(stats.domains)[0];
          }
        } else {
          // Add new university from real data
          const primaryDomain = Array.from(stats.domains)[0] || `${universityName.toLowerCase().replace(/\s+/g, '')}.edu`;

          universityMap.set(universityName, {
            id: universityName.toLowerCase().replace(/\s+/g, '-'),
            name: universityName,
            domain: primaryDomain,
            shortName: this.generateShortName(universityName),
            isActive: true,
            userCount: stats.userCount,
            listingCount: stats.listingCount,
            allowedEmailDomains: Array.from(stats.domains),
            requiresVerification: true,
            createdAt: Timestamp.now(),
            settings: {
              allowPublicListings: true,
              allowCrossUniversityChat: false,
              moderationLevel: 'medium' as const
            }
          });
        }
      });

      // Convert to array and sort by name for better UX
      const universities = Array.from(universityMap.values());
      return universities.sort((a, b) => a.name.localeCompare(b.name));
    } catch (error) {
      console.error('Error fetching universities:', error);
      throw error;
    }
  }

  /**
   * Get detailed stats for a specific university
   */
  static async getUniversityStats(universityName: string): Promise<UniversityStats> {
    try {
      const oneWeekAgo = new Date();
      oneWeekAgo.setDate(oneWeekAgo.getDate() - 7);

      const [
        allUsersSnapshot,
        recentUsersSnapshot,
        allListingsSnapshot,
        recentListingsSnapshot
      ] = await Promise.all([
        getDocs(query(collection(firestore, 'users'), where('university', '==', universityName))),
        getDocs(query(
          collection(firestore, 'users'),
          where('university', '==', universityName),
          where('createdAt', '>=', Timestamp.fromDate(oneWeekAgo))
        )),
        getDocs(query(collection(firestore, 'listings'), where('university', '==', universityName))),
        getDocs(query(
          collection(firestore, 'listings'),
          where('university', '==', universityName),
          where('createdAt', '>=', Timestamp.fromDate(oneWeekAgo))
        ))
      ]);

      const allUsers = allUsersSnapshot.docs.map(doc => doc.data()) as User[];
      const allListings = allListingsSnapshot.docs.map(doc => doc.data()) as Listing[];

      // Calculate active users (logged in within last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      const activeUsers = allUsers.filter(user => {
        const lastActivity = user.updatedAt?.toDate() || user.createdAt.toDate();
        return lastActivity > thirtyDaysAgo;
      }).length;

      const activeListings = allListings.filter(listing => listing.status === 'active').length;

      return {
        totalUsers: allUsers.length,
        activeUsers,
        totalListings: allListings.length,
        activeListings,
        recentActivity: {
          newUsersThisWeek: recentUsersSnapshot.size,
          newListingsThisWeek: recentListingsSnapshot.size,
          messagesThisWeek: 0 // TODO: Implement when chat data is available
        }
      };
    } catch (error) {
      console.error('Error fetching university stats:', error);
      throw error;
    }
  }

  /**
   * Get users by university name or domain
   */
  static async getUsersByUniversity(universityNameOrDomain: string): Promise<any[]> {
    try {
      // First try to find users by university name
      let usersQuery = query(
        collection(firestore, 'users'),
        where('university', '==', universityNameOrDomain),
        orderBy('createdAt', 'desc')
      );

      let usersSnapshot = await getDocs(usersQuery);

      // If no results and it looks like a domain, try to find the university name
      if (usersSnapshot.empty && universityNameOrDomain.includes('.')) {
        // Try to find users by extracting university name from domain
        const universityName = universityNameOrDomain.split('.')[0];
        const capitalizedName = universityName.charAt(0).toUpperCase() + universityName.slice(1);

        usersQuery = query(
          collection(firestore, 'users'),
          where('university', '==', capitalizedName),
          orderBy('createdAt', 'desc')
        );

        usersSnapshot = await getDocs(usersQuery);
      }

      return usersSnapshot.docs.map(doc => ({
        id: doc.id,
        uid: doc.id,
        ...doc.data()
      }));
    } catch (error) {
      console.error('Error fetching users by university:', error);
      // Try alternative query without orderBy if index doesn't exist
      try {
        const usersQuery = query(
          collection(firestore, 'users'),
          where('university', '==', universityNameOrDomain)
        );

        const usersSnapshot = await getDocs(usersQuery);
        return usersSnapshot.docs.map(doc => ({
          id: doc.id,
          uid: doc.id,
          ...doc.data()
        }));
      } catch (fallbackError) {
        console.error('Fallback query also failed:', fallbackError);
        return [];
      }
    }
  }

  /**
   * Add a new university
   */
  static async addUniversity(universityData: Omit<University, 'id' | 'userCount' | 'listingCount' | 'createdAt'>): Promise<void> {
    try {
      const universityId = universityData.name.toLowerCase().replace(/\s+/g, '-');
      const universityRef = doc(firestore, 'universities', universityId);

      await setDoc(universityRef, {
        ...universityData,
        id: universityId,
        userCount: 0,
        listingCount: 0,
        createdAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error adding university:', error);
      throw error;
    }
  }

  /**
   * Update university settings
   */
  static async updateUniversity(universityId: string, updates: Partial<University>): Promise<void> {
    try {
      const universityRef = doc(firestore, 'universities', universityId);
      await updateDoc(universityRef, {
        ...updates,
        updatedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error updating university:', error);
      throw error;
    }
  }

  /**
   * Deactivate a university (soft delete)
   */
  static async deactivateUniversity(universityId: string): Promise<void> {
    try {
      await this.updateUniversity(universityId, { isActive: false });
    } catch (error) {
      console.error('Error deactivating university:', error);
      throw error;
    }
  }

  /**
   * Get users for a specific university
   */
  static async getUniversityUsers(universityName: string, _limit: number = 50): Promise<User[]> {
    try {
      const usersQuery = query(
        collection(firestore, 'users'),
        where('university', '==', universityName),
        orderBy('createdAt', 'desc')
      );
      
      const snapshot = await getDocs(usersQuery);
      return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as User[];
    } catch (error) {
      console.error('Error fetching university users:', error);
      throw error;
    }
  }

  /**
   * Get listings for a specific university
   */
  static async getUniversityListings(universityName: string, _limit: number = 50): Promise<Listing[]> {
    try {
      const listingsQuery = query(
        collection(firestore, 'listings'),
        where('university', '==', universityName),
        orderBy('createdAt', 'desc')
      );
      
      const snapshot = await getDocs(listingsQuery);
      return snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as Listing[];
    } catch (error) {
      console.error('Error fetching university listings:', error);
      throw error;
    }
  }

  /**
   * Validate university email domain
   */
  static validateEmailDomain(email: string, university: University): boolean {
    const emailDomain = email.split('@')[1];
    return university.allowedEmailDomains.includes(emailDomain);
  }

  /**
   * Generate short name for university
   */
  private static generateShortName(fullName: string): string {
    const words = fullName.split(' ');
    if (words.length === 1) {
      return words[0].substring(0, 4).toUpperCase();
    }
    
    // Take first letter of each significant word
    const significantWords = words.filter(word => 
      !['of', 'the', 'and', 'at', 'in'].includes(word.toLowerCase())
    );
    
    if (significantWords.length >= 2) {
      return significantWords.slice(0, 3).map(word => word[0]).join('').toUpperCase();
    }
    
    return fullName.substring(0, 4).toUpperCase();
  }

  /**
   * Get comprehensive list of default universities
   */
  private static getDefaultUniversities(): University[] {
    const defaultUniversities = [
      // Major State Universities
      { name: 'University of California, Berkeley', domains: ['berkeley.edu'] },
      { name: 'University of California, Los Angeles', domains: ['ucla.edu'] },
      { name: 'University of California, San Diego', domains: ['ucsd.edu'] },
      { name: 'University of California, Davis', domains: ['ucdavis.edu'] },
      { name: 'University of California, Irvine', domains: ['uci.edu'] },
      { name: 'University of California, Santa Barbara', domains: ['ucsb.edu'] },
      { name: 'University of Texas at Austin', domains: ['utexas.edu'] },
      { name: 'University of Michigan', domains: ['umich.edu'] },
      { name: 'University of Florida', domains: ['ufl.edu'] },
      { name: 'Ohio State University', domains: ['osu.edu'] },
      { name: 'Pennsylvania State University', domains: ['psu.edu'] },
      { name: 'University of Washington', domains: ['uw.edu'] },
      { name: 'University of Wisconsin-Madison', domains: ['wisc.edu'] },
      { name: 'University of Illinois at Urbana-Champaign', domains: ['illinois.edu'] },
      { name: 'University of Georgia', domains: ['uga.edu'] },
      { name: 'University of North Carolina at Chapel Hill', domains: ['unc.edu'] },
      { name: 'University of Virginia', domains: ['virginia.edu'] },
      { name: 'University of Maryland', domains: ['umd.edu'] },
      { name: 'Arizona State University', domains: ['asu.edu'] },
      { name: 'Mississippi State University', domains: ['msstate.edu'] },

      // Ivy League Universities
      { name: 'Harvard University', domains: ['harvard.edu'] },
      { name: 'Yale University', domains: ['yale.edu'] },
      { name: 'Princeton University', domains: ['princeton.edu'] },
      { name: 'Columbia University', domains: ['columbia.edu'] },
      { name: 'University of Pennsylvania', domains: ['upenn.edu'] },
      { name: 'Dartmouth College', domains: ['dartmouth.edu'] },
      { name: 'Brown University', domains: ['brown.edu'] },
      { name: 'Cornell University', domains: ['cornell.edu'] },

      // Top Private Universities
      { name: 'Stanford University', domains: ['stanford.edu'] },
      { name: 'Massachusetts Institute of Technology', domains: ['mit.edu'] },
      { name: 'California Institute of Technology', domains: ['caltech.edu'] },
      { name: 'University of Chicago', domains: ['uchicago.edu'] },
      { name: 'Northwestern University', domains: ['northwestern.edu'] },
      { name: 'Duke University', domains: ['duke.edu'] },
      { name: 'Vanderbilt University', domains: ['vanderbilt.edu'] },
      { name: 'Rice University', domains: ['rice.edu'] },
      { name: 'Emory University', domains: ['emory.edu'] },
      { name: 'University of Southern California', domains: ['usc.edu'] },
      { name: 'Carnegie Mellon University', domains: ['cmu.edu'] },
      { name: 'Georgetown University', domains: ['georgetown.edu'] },

      // Major Tech-Focused Universities
      { name: 'Georgia Institute of Technology', domains: ['gatech.edu'] },
      { name: 'Virginia Tech', domains: ['vt.edu'] },
      { name: 'Purdue University', domains: ['purdue.edu'] },
      { name: 'University of California, San Francisco', domains: ['ucsf.edu'] },
      { name: 'Rensselaer Polytechnic Institute', domains: ['rpi.edu'] },

      // Other Notable Universities
      { name: 'New York University', domains: ['nyu.edu'] },
      { name: 'Boston University', domains: ['bu.edu'] },
      { name: 'University of Miami', domains: ['miami.edu'] },
      { name: 'University of Notre Dame', domains: ['nd.edu'] },
      { name: 'Wake Forest University', domains: ['wfu.edu'] },
      { name: 'Tulane University', domains: ['tulane.edu'] },
      { name: 'University of Rochester', domains: ['rochester.edu'] },
      { name: 'Case Western Reserve University', domains: ['case.edu'] },
      { name: 'Lehigh University', domains: ['lehigh.edu'] },
      { name: 'Syracuse University', domains: ['syracuse.edu'] },

      // State Universities by Region
      { name: 'Florida State University', domains: ['fsu.edu'] },
      { name: 'University of Alabama', domains: ['ua.edu'] },
      { name: 'Auburn University', domains: ['auburn.edu'] },
      { name: 'University of Tennessee', domains: ['utk.edu'] },
      { name: 'University of Kentucky', domains: ['uky.edu'] },
      { name: 'Louisiana State University', domains: ['lsu.edu'] },
      { name: 'University of Arkansas', domains: ['uark.edu'] },
      { name: 'University of Oklahoma', domains: ['ou.edu'] },
      { name: 'Oklahoma State University', domains: ['okstate.edu'] },
      { name: 'Texas A&M University', domains: ['tamu.edu'] },
      { name: 'University of Colorado Boulder', domains: ['colorado.edu'] },
      { name: 'University of Utah', domains: ['utah.edu'] },
      { name: 'University of Oregon', domains: ['uoregon.edu'] },
      { name: 'Oregon State University', domains: ['oregonstate.edu'] },
      { name: 'Washington State University', domains: ['wsu.edu'] },
      { name: 'University of Nevada, Las Vegas', domains: ['unlv.edu'] },
      { name: 'San Diego State University', domains: ['sdsu.edu'] },
      { name: 'California State University, Long Beach', domains: ['csulb.edu'] },
      { name: 'University of Connecticut', domains: ['uconn.edu'] },
      { name: 'University of Delaware', domains: ['udel.edu'] },
      { name: 'University of Vermont', domains: ['uvm.edu'] },
      { name: 'University of New Hampshire', domains: ['unh.edu'] },
      { name: 'University of Maine', domains: ['maine.edu'] },
      { name: 'University of Rhode Island', domains: ['uri.edu'] },
      { name: 'University of Massachusetts Amherst', domains: ['umass.edu'] },
      { name: 'Rutgers University', domains: ['rutgers.edu'] },
      { name: 'Temple University', domains: ['temple.edu'] },
      { name: 'University of Pittsburgh', domains: ['pitt.edu'] },
      { name: 'West Virginia University', domains: ['wvu.edu'] },
      { name: 'Virginia Commonwealth University', domains: ['vcu.edu'] },
      { name: 'University of South Carolina', domains: ['sc.edu'] },
      { name: 'Clemson University', domains: ['clemson.edu'] },
      { name: 'University of Mississippi', domains: ['olemiss.edu'] },
      { name: 'University of Louisiana at Lafayette', domains: ['louisiana.edu'] },
      { name: 'University of Houston', domains: ['uh.edu'] },
      { name: 'Texas Tech University', domains: ['ttu.edu'] },
      { name: 'University of North Texas', domains: ['unt.edu'] },
      { name: 'University of Kansas', domains: ['ku.edu'] },
      { name: 'Kansas State University', domains: ['ksu.edu'] },
      { name: 'University of Nebraska-Lincoln', domains: ['unl.edu'] },
      { name: 'Iowa State University', domains: ['iastate.edu'] },
      { name: 'University of Iowa', domains: ['uiowa.edu'] },
      { name: 'University of Minnesota', domains: ['umn.edu'] },
      { name: 'University of Missouri', domains: ['missouri.edu'] },
      { name: 'Indiana University', domains: ['indiana.edu'] },
      { name: 'Ball State University', domains: ['bsu.edu'] },
      { name: 'Michigan State University', domains: ['msu.edu'] },
      { name: 'Wayne State University', domains: ['wayne.edu'] },
      { name: 'University of Cincinnati', domains: ['uc.edu'] },
      { name: 'Miami University', domains: ['miamioh.edu'] },
      { name: 'Kent State University', domains: ['kent.edu'] },
      { name: 'Bowling Green State University', domains: ['bgsu.edu'] }
    ];

    return defaultUniversities.map((uni) => ({
      id: uni.name.toLowerCase().replace(/[^a-z0-9]/g, '-').replace(/-+/g, '-'),
      name: uni.name,
      domain: uni.domains[0],
      shortName: this.generateShortName(uni.name),
      isActive: true,
      userCount: 0,
      listingCount: 0,
      allowedEmailDomains: uni.domains,
      requiresVerification: true,
      createdAt: Timestamp.now(),
      settings: {
        allowPublicListings: true,
        allowCrossUniversityChat: false,
        moderationLevel: 'medium' as const
      }
    }));
  }

  /**
   * Search universities by name or domain
   */
  static async searchUniversities(searchTerm: string): Promise<University[]> {
    try {
      const universities = await this.getUniversities();
      const term = searchTerm.toLowerCase();

      return universities.filter(uni =>
        uni.name.toLowerCase().includes(term) ||
        uni.domain.toLowerCase().includes(term) ||
        uni.shortName?.toLowerCase().includes(term)
      );
    } catch (error) {
      console.error('Error searching universities:', error);
      throw error;
    }
  }

  /**
   * Get count of available universities (for debugging)
   */
  static async getUniversityCount(): Promise<number> {
    try {
      const universities = await this.getUniversities();
      return universities.length;
    } catch (error) {
      console.error('Error getting university count:', error);
      return 0;
    }
  }
}

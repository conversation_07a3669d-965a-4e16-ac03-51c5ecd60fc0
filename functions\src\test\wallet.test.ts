import { validateWalletUsage, calculateFinalAmount } from '../utils/wallet';

describe('Wallet Utility Functions', () => {
  describe('validateWalletUsage', () => {
    test('should validate successful wallet usage', () => {
      const result = validateWalletUsage(5, 10, 20);
      expect(result.isValid).toBe(true);
      expect(result.error).toBeUndefined();
    });

    test('should reject negative wallet amount', () => {
      const result = validateWalletUsage(-1, 10, 20);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('cannot be negative');
    });

    test('should reject wallet amount exceeding balance', () => {
      const result = validateWalletUsage(15, 10, 20);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('Insufficient wallet balance');
    });

    test('should reject wallet amount exceeding total cost', () => {
      const result = validateWalletUsage(25, 30, 20);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('cannot exceed total cost');
    });
  });

  describe('calculateFinalAmount', () => {
    test('should calculate final amount correctly', () => {
      const result = calculateFinalAmount(10, 5, 3);
      expect(result.totalBeforeWallet).toBe(15);
      expect(result.finalAmount).toBe(12);
    });

    test('should handle zero final amount', () => {
      const result = calculateFinalAmount(10, 5, 15);
      expect(result.totalBeforeWallet).toBe(15);
      expect(result.finalAmount).toBe(0);
    });

    test('should handle wallet amount exceeding total', () => {
      const result = calculateFinalAmount(10, 5, 20);
      expect(result.totalBeforeWallet).toBe(15);
      expect(result.finalAmount).toBe(0); // Math.max(0, 15-20) = 0
    });

    test('should handle no wallet usage', () => {
      const result = calculateFinalAmount(10, 5, 0);
      expect(result.totalBeforeWallet).toBe(15);
      expect(result.finalAmount).toBe(15);
    });
  });
});

// Test scenarios for the main checkout flow
describe('Checkout Flow Test Scenarios', () => {
  test('Scenario 1: $1 wallet used on $1 item', () => {
    const itemPrice = 1;
    const shippingCost = 0;
    const walletUsed = 1;
    const walletBalance = 5;

    const validation = validateWalletUsage(walletUsed, walletBalance, itemPrice + shippingCost);
    const calculation = calculateFinalAmount(itemPrice, shippingCost, walletUsed);

    expect(validation.isValid).toBe(true);
    expect(calculation.finalAmount).toBe(0); // Should skip Stripe
  });

  test('Scenario 2: $1 wallet used on $5 item', () => {
    const itemPrice = 5;
    const shippingCost = 0;
    const walletUsed = 1;
    const walletBalance = 5;

    const validation = validateWalletUsage(walletUsed, walletBalance, itemPrice + shippingCost);
    const calculation = calculateFinalAmount(itemPrice, shippingCost, walletUsed);

    expect(validation.isValid).toBe(true);
    expect(calculation.finalAmount).toBe(4); // Should charge $4 to Stripe
  });

  test('Scenario 3: $0 wallet used', () => {
    const itemPrice = 5;
    const shippingCost = 2;
    const walletUsed = 0;
    const walletBalance = 10;

    const validation = validateWalletUsage(walletUsed, walletBalance, itemPrice + shippingCost);
    const calculation = calculateFinalAmount(itemPrice, shippingCost, walletUsed);

    expect(validation.isValid).toBe(true);
    expect(calculation.finalAmount).toBe(7); // Should charge full amount
  });

  test('Scenario 4: walletUsed > walletBalance', () => {
    const itemPrice = 10;
    const shippingCost = 2;
    const walletUsed = 15;
    const walletBalance = 10;

    const validation = validateWalletUsage(walletUsed, walletBalance, itemPrice + shippingCost);

    expect(validation.isValid).toBe(false);
    expect(validation.error).toContain('Insufficient wallet balance');
  });

  test('Scenario 5: Complex order with shipping', () => {
    const itemPrice = 25;
    const shippingCost = 5;
    const walletUsed = 10;
    const walletBalance = 15;

    const validation = validateWalletUsage(walletUsed, walletBalance, itemPrice + shippingCost);
    const calculation = calculateFinalAmount(itemPrice, shippingCost, walletUsed);

    expect(validation.isValid).toBe(true);
    expect(calculation.totalBeforeWallet).toBe(30);
    expect(calculation.finalAmount).toBe(20); // $30 - $10 = $20 to Stripe
  });
});

"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var _a;
Object.defineProperty(exports, "__esModule", { value: true });
exports.trackShipment = exports.generateShippingLabel = exports.getShippingRates = void 0;
const functions = __importStar(require("firebase-functions"));
const admin = __importStar(require("firebase-admin"));
// Shippo API configuration
const SHIPPO_API_KEY = ((_a = functions.config().shippo) === null || _a === void 0 ? void 0 : _a.api_key) || process.env.SHIPPO_API_KEY;
const SHIPPO_BASE_URL = 'https://api.goshippo.com';
// Get shipping rates for an order
exports.getShippingRates = functions.https.onCall(async (data, context) => {
    var _a, _b, _c;
    try {
        if (!(context === null || context === void 0 ? void 0 : context.auth)) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const { listingId, shippingAddress } = data;
        if (!listingId || !shippingAddress) {
            throw new functions.https.HttpsError('invalid-argument', 'Listing ID and shipping address are required');
        }
        // Get listing details
        const listingDoc = await admin.firestore().collection('listings').doc(listingId).get();
        if (!listingDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'Listing not found');
        }
        const listing = listingDoc.data();
        // Check if listing supports shipping
        if ((listing === null || listing === void 0 ? void 0 : listing.deliveryMethod) !== 'mail') {
            throw new functions.https.HttpsError('invalid-argument', 'This listing does not support shipping');
        }
        // Get seller address from listing
        const sellerAddress = (_a = listing === null || listing === void 0 ? void 0 : listing.shippingOptions) === null || _a === void 0 ? void 0 : _a.sellerAddress;
        if (!sellerAddress) {
            throw new functions.https.HttpsError('failed-precondition', 'Seller address not configured');
        }
        // Get package details
        const packageDetails = (_b = listing === null || listing === void 0 ? void 0 : listing.shippingOptions) === null || _b === void 0 ? void 0 : _b.packageDetails;
        if (!packageDetails) {
            throw new functions.https.HttpsError('failed-precondition', 'Package details not configured');
        }
        // Create shipment to get rates
        const shipment = await createShippoShipment(sellerAddress, shippingAddress, packageDetails);
        // Filter and format rates
        const formattedRates = ((_c = shipment.rates) === null || _c === void 0 ? void 0 : _c.map((rate) => ({
            id: rate.object_id,
            amount: parseFloat(rate.amount),
            currency: rate.currency,
            provider: rate.provider,
            service: rate.servicelevel.name,
            estimatedDays: rate.estimated_days,
            displayName: `${rate.provider} ${rate.servicelevel.name} (${rate.estimated_days} days)`
        }))) || [];
        return {
            success: true,
            rates: formattedRates.sort((a, b) => a.amount - b.amount) // Sort by price
        };
    }
    catch (error) {
        console.error('Error getting shipping rates:', error);
        throw error;
    }
});
// Generate shipping label after payment
exports.generateShippingLabel = functions.https.onCall(async (data, context) => {
    var _a, _b, _c, _d;
    try {
        if (!(context === null || context === void 0 ? void 0 : context.auth)) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const { orderId, rateId } = data;
        if (!orderId || !rateId) {
            throw new functions.https.HttpsError('invalid-argument', 'Order ID and rate ID are required');
        }
        // Get order details
        const orderDoc = await admin.firestore().collection('orders').doc(orderId);
        const orderSnapshot = await orderDoc.get();
        if (!orderSnapshot.exists) {
            throw new functions.https.HttpsError('not-found', 'Order not found');
        }
        const orderData = orderSnapshot.data();
        // Verify user is the seller
        if ((orderData === null || orderData === void 0 ? void 0 : orderData.sellerId) !== ((_a = context === null || context === void 0 ? void 0 : context.auth) === null || _a === void 0 ? void 0 : _a.uid)) {
            throw new functions.https.HttpsError('permission-denied', 'Only the seller can generate shipping labels');
        }
        // Create shipping label transaction
        const transaction = await createShippoTransaction(rateId);
        if (transaction.status !== 'SUCCESS') {
            throw new functions.https.HttpsError('internal', 'Failed to create shipping label');
        }
        // Update order with shipping information
        await orderDoc.update({
            status: 'shipped',
            shippingLabelUrl: transaction.label_url,
            shippingTrackingUrl: transaction.tracking_url_provider,
            shippingTrackingNumber: transaction.tracking_number,
            shippedAt: admin.firestore.Timestamp.now(),
            deliveryConfirmedAt: null, // Will be set when delivered
            returnEligibleUntil: null, // Will be set when delivered
            updatedAt: admin.firestore.Timestamp.now()
        });
        // Store shipping label details
        await admin.firestore().collection('shippingLabels').doc(orderId).set({
            orderId,
            labelUrl: transaction.label_url,
            trackingNumber: transaction.tracking_number,
            trackingUrl: transaction.tracking_url_provider,
            carrier: (_b = transaction.rate) === null || _b === void 0 ? void 0 : _b.provider,
            service: (_d = (_c = transaction.rate) === null || _c === void 0 ? void 0 : _c.servicelevel) === null || _d === void 0 ? void 0 : _d.name,
            createdAt: admin.firestore.Timestamp.now()
        });
        return {
            success: true,
            labelUrl: transaction.label_url,
            trackingNumber: transaction.tracking_number,
            trackingUrl: transaction.tracking_url_provider
        };
    }
    catch (error) {
        console.error('Error generating shipping label:', error);
        throw error;
    }
});
// Track shipment status
exports.trackShipment = functions.https.onCall(async (data, context) => {
    var _a;
    try {
        if (!(context === null || context === void 0 ? void 0 : context.auth)) {
            throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
        }
        const { orderId } = data;
        if (!orderId) {
            throw new functions.https.HttpsError('invalid-argument', 'Order ID is required');
        }
        // Get order details
        const orderDoc = await admin.firestore().collection('orders').doc(orderId).get();
        if (!orderDoc.exists) {
            throw new functions.https.HttpsError('not-found', 'Order not found');
        }
        const orderData = orderDoc.data();
        const trackingNumber = orderData === null || orderData === void 0 ? void 0 : orderData.shippingTrackingNumber;
        if (!trackingNumber) {
            throw new functions.https.HttpsError('failed-precondition', 'No tracking number available');
        }
        // Get tracking info from Shippo
        const trackingInfo = await getShippoTracking(trackingNumber);
        // Check if delivered
        if (((_a = trackingInfo.tracking_status) === null || _a === void 0 ? void 0 : _a.status) === 'DELIVERED') {
            // Update order status and set return window
            const deliveryDate = new Date();
            const returnDeadline = new Date(deliveryDate.getTime() + 3 * 24 * 60 * 60 * 1000); // 3 days
            await admin.firestore().collection('orders').doc(orderId).update({
                status: 'delivered',
                deliveryConfirmedAt: admin.firestore.Timestamp.fromDate(deliveryDate),
                returnEligibleUntil: admin.firestore.Timestamp.fromDate(returnDeadline),
                updatedAt: admin.firestore.Timestamp.now()
            });
        }
        return {
            success: true,
            tracking: trackingInfo
        };
    }
    catch (error) {
        console.error('Error tracking shipment:', error);
        throw error;
    }
});
// Helper functions for Shippo API calls
async function createShippoShipment(fromAddress, toAddress, packageDetails) {
    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;
    const shipmentData = {
        address_from: fromAddress,
        address_to: toAddress,
        parcels: [{
                length: ((_b = (_a = packageDetails.dimensions) === null || _a === void 0 ? void 0 : _a.length) === null || _b === void 0 ? void 0 : _b.toString()) || '10',
                width: ((_d = (_c = packageDetails.dimensions) === null || _c === void 0 ? void 0 : _c.width) === null || _d === void 0 ? void 0 : _d.toString()) || '8',
                height: ((_f = (_e = packageDetails.dimensions) === null || _e === void 0 ? void 0 : _e.height) === null || _f === void 0 ? void 0 : _f.toString()) || '4',
                distance_unit: ((_g = packageDetails.dimensions) === null || _g === void 0 ? void 0 : _g.unit) || 'in',
                weight: ((_j = (_h = packageDetails.weight) === null || _h === void 0 ? void 0 : _h.value) === null || _j === void 0 ? void 0 : _j.toString()) || '1',
                mass_unit: ((_k = packageDetails.weight) === null || _k === void 0 ? void 0 : _k.unit) || 'lb'
            }],
        async: false
    };
    const response = await fetch(`${SHIPPO_BASE_URL}/shipments/`, {
        method: 'POST',
        headers: {
            'Authorization': `ShippoToken ${SHIPPO_API_KEY}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(shipmentData)
    });
    if (!response.ok) {
        throw new Error(`Shippo API error: ${response.status}`);
    }
    return await response.json();
}
async function createShippoTransaction(rateId) {
    const transactionData = {
        rate: rateId,
        label_file_type: 'PDF',
        async: false
    };
    const response = await fetch(`${SHIPPO_BASE_URL}/transactions/`, {
        method: 'POST',
        headers: {
            'Authorization': `ShippoToken ${SHIPPO_API_KEY}`,
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(transactionData)
    });
    if (!response.ok) {
        throw new Error(`Shippo transaction error: ${response.status}`);
    }
    return await response.json();
}
async function getShippoTracking(trackingNumber) {
    const response = await fetch(`${SHIPPO_BASE_URL}/tracks/${trackingNumber}`, {
        method: 'GET',
        headers: {
            'Authorization': `ShippoToken ${SHIPPO_API_KEY}`
        }
    });
    if (!response.ok) {
        throw new Error(`Shippo tracking error: ${response.status}`);
    }
    return await response.json();
}

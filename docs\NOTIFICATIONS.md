# Hive Campus Notification System

## Overview

The Hive Campus notification system provides comprehensive cross-platform push notifications using Firebase Cloud Messaging (FCM). It supports real-time in-app notifications, push notifications, and email notifications across Android, iOS, Windows, and macOS platforms.

## Features

### ✅ Implemented Features

- **Cross-Platform Push Notifications**: Support for Android (PWA), iOS 16.4+ (PWA), Windows (Chrome/Edge), and macOS (Chrome/Safari)
- **Real-time In-App Notifications**: Live updates using Firestore listeners
- **Comprehensive Notification Types**: 15+ notification types including orders, payments, messages, warnings, and announcements
- **User Preferences**: Granular control over notification channels and categories
- **FCM Integration**: Full Firebase Cloud Messaging setup with VAPID key
- **Service Worker**: Background message handling with custom notification display
- **Notification Management**: Mark as read, delete, bulk actions, filtering, and search
- **Debug Panel**: Comprehensive FCM testing and debugging tools
- **Toast Notifications**: In-app toast messages for immediate feedback
- **Retry Logic**: Automatic retry for failed permission requests

### 🔧 Technical Implementation

#### Frontend Components

1. **NotificationBell**: Header notification icon with badge count and dropdown
2. **NotificationsDropdown**: Quick access to recent notifications
3. **NotificationSettings**: Comprehensive settings panel for user preferences
4. **NotificationsPage**: Full notification management interface
5. **FCMTestPanel**: Debug and testing tools for FCM functionality

#### Hooks

1. **useFCM**: Main FCM token management and messaging hook
2. **useUserNotifications**: Real-time notification data management
3. **useNotificationPreferences**: User preference management

#### Backend Functions

1. **sendNotification**: Send notifications to individual users
2. **sendBatchNotifications**: Send notifications to multiple users
3. **NotificationService**: Frontend service for calling notification functions

#### Service Worker

- **firebase-messaging-sw.js**: Handles background push notifications
- Custom notification display with actions and click handling
- Platform-specific optimizations

## Configuration

### Environment Variables

```env
VITE_FIREBASE_VAPID_KEY=BE7_tTGXiVr_OTgmGH4oWI_KxfLfOidgN4P9eCg_AkTm3ld3WAqxDtNlCBT_BgUOTJ8tX7JyVnd47k3zluNA_y0
```

### Firebase Configuration

The system uses the existing Firebase configuration with messaging enabled:

```typescript
import { getMessaging, isSupported } from 'firebase/messaging';

// Initialize Firebase Cloud Messaging
let messaging: ReturnType<typeof getMessaging> | null = null;
try {
  if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
    isSupported().then((supported) => {
      if (supported) {
        messaging = getMessaging(app);
      }
    });
  }
} catch (error) {
  console.warn('Firebase Messaging not supported:', error);
}
```

## Notification Types

The system supports the following notification types:

- `listing_sold` - When a user's item is purchased
- `order_confirmed` - When an order is confirmed
- `order_delivered` - When an order is delivered
- `wallet_credited` - When money is added to wallet
- `wallet_debited` - When money is deducted from wallet
- `new_chat_message` - New chat messages
- `48_hour_shipping_reminder` - Shipping reminders
- `platform_announcement` - Platform announcements
- `auction_update` - Auction updates
- `payment_failed` - Payment failures
- `user_warning` - Account warnings
- `delivery_confirmation` - Delivery confirmations
- `admin_warning` - Admin warnings
- `admin_broadcast` - Admin announcements
- `payment_success` - Successful payments

## Platform Support

### Android (Chrome PWA)
- ✅ Full FCM support
- ✅ Background notifications
- ✅ Notification actions
- ✅ Custom icons and sounds

### iOS 16.4+ (Safari PWA)
- ✅ Limited FCM support (PWA only)
- ✅ Requires "Add to Home Screen"
- ⚠️ Background notifications limited
- ✅ In-app notifications work fully

### Windows (Chrome/Edge)
- ✅ Full FCM support
- ✅ Native notification integration
- ✅ Background notifications
- ✅ All features supported

### macOS (Chrome/Safari)
- ✅ Chrome: Full support
- ⚠️ Safari: Limited support
- ✅ Background notifications (Chrome)
- ✅ In-app notifications work fully

## Usage

### Basic Setup

```typescript
import { useFCM } from './hooks/useFCMTokenManager';
import { useUserNotifications } from './hooks/useUserNotifications';

function MyComponent() {
  const { requestPermission, token, isSupported } = useFCM();
  const { notifications, unreadCount } = useUserNotifications();

  // Request permission on component mount
  useEffect(() => {
    if (isSupported && !token) {
      requestPermission();
    }
  }, [isSupported, token, requestPermission]);

  return (
    <div>
      <p>Unread notifications: {unreadCount}</p>
      {/* Render notifications */}
    </div>
  );
}
```

### Sending Notifications

```typescript
import NotificationService from './services/notificationService';

// Send a single notification
await NotificationService.sendNotification({
  userId: 'user123',
  type: 'order_confirmed',
  title: 'Order Confirmed',
  message: 'Your order has been confirmed!',
  channels: ['in_app', 'push', 'email'],
  priority: 'high',
  link: '/orders/123'
});

// Send batch notifications
await NotificationService.sendBatchNotifications(
  ['user1', 'user2', 'user3'],
  {
    type: 'platform_announcement',
    title: 'New Feature Available',
    message: 'Check out our new messaging system!',
    channels: ['in_app', 'push']
  }
);
```

### User Preferences

```typescript
import { useNotificationPreferences } from './hooks/useNotificationPreferences';

function NotificationSettings() {
  const {
    preferences,
    togglePushNotifications,
    toggleEmailNotifications,
    muteCategory,
    unmuteCategory
  } = useNotificationPreferences();

  return (
    <div>
      <button onClick={togglePushNotifications}>
        {preferences.enable_push ? 'Disable' : 'Enable'} Push Notifications
      </button>
      <button onClick={() => muteCategory('new_chat_message')}>
        Mute Chat Messages
      </button>
    </div>
  );
}
```

## Firestore Structure

### User Notifications
```
users/{userId}/notifications/{notificationId}
{
  type: string,
  title: string,
  message: string,
  icon: string,
  createdAt: Timestamp,
  read: boolean,
  link: string,
  priority: 'low' | 'normal' | 'high' | 'urgent',
  actionRequired: boolean,
  metadata: object,
  // Type-specific fields
  orderId?: string,
  listingId?: string,
  chatId?: string,
  amount?: number,
  secretCode?: string
}
```

### User Preferences
```
users/{userId}
{
  preferences: {
    notifications: {
      enable_push: boolean,
      enable_email: boolean,
      pause_all: boolean,
      muted_categories: string[],
      channels: {
        in_app: { enabled: boolean, types: string[] },
        push: { enabled: boolean, types: string[] },
        email: { enabled: boolean, types: string[] }
      }
    }
  },
  fcmToken: string,
  fcmTokenUpdatedAt: Timestamp
}
```

## Security Rules

```javascript
// Allow users to manage their own notifications
match /users/{userId}/notifications/{notificationId} {
  allow read, update, delete: if request.auth.uid == userId;
  allow create: if isAdmin();
}

// Allow users to update their FCM token
match /users/{userId} {
  allow update: if request.auth.uid == userId && 
    request.resource.data.diff(resource.data).affectedKeys()
    .hasOnly(['fcmToken', 'fcmTokenUpdatedAt', 'fcmTokenPlatform', 'fcmTokenUserAgent']);
}
```

## Testing

### Debug Panel

Access the debug panel at `/notifications` (Debug tab) in development mode to:

- Check FCM support and initialization status
- View platform and browser compatibility
- Test notification permissions
- Send test notifications
- View FCM token details
- Retry failed permission requests

### Manual Testing

1. **Permission Request**: Visit the app and check for automatic permission request
2. **Token Generation**: Verify FCM token is generated and stored in Firestore
3. **Foreground Messages**: Send a test notification while app is open
4. **Background Messages**: Send a test notification while app is closed/minimized
5. **Notification Actions**: Click on notifications to test navigation
6. **Cross-Platform**: Test on different devices and browsers

## Troubleshooting

### Common Issues

1. **FCM Not Supported**: Check browser compatibility and HTTPS requirement
2. **Permission Denied**: Guide users to enable notifications in browser settings
3. **Token Not Generated**: Verify VAPID key configuration and network connectivity
4. **Background Notifications Not Working**: Ensure service worker is registered and active
5. **iOS Limitations**: Remind users to add PWA to home screen for full functionality

### Debug Steps

1. Check browser console for FCM-related errors
2. Verify service worker registration in DevTools
3. Test notification permission status
4. Validate FCM token in Firestore
5. Use the debug panel for comprehensive diagnostics

## Future Enhancements

- Email notification templates and sending
- Notification scheduling and batching
- Advanced analytics and metrics
- A/B testing for notification content
- Rich media notifications
- Notification categories and channels
- Smart notification timing
- Machine learning for notification optimization

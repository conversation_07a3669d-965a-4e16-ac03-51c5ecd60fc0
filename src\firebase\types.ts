import { Timestamp } from 'firebase/firestore';

// User related types
export interface User {
  uid: string;
  name: string;
  email: string;
  role: UserRole;
  university: string;
  profilePictureURL?: string;
  bio?: string;
  graduationYear?: number;
  major?: string;
  createdAt: Timestamp;
  updatedAt?: Timestamp;
  // Admin management fields
  status?: 'active' | 'blocked' | 'suspended';
  blockedAt?: Timestamp;
  blockedBy?: string;
  blockReason?: string;
  emailVerified?: boolean;
  lastLoginAt?: Timestamp;
  warningCount?: number;
  lastWarningAt?: Timestamp;
}

// Address related types
export interface UserAddress {
  id: string;
  fullName: string;
  phone: string;
  line1: string;
  line2?: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  isDefault: boolean;
  createdAt: Timestamp;
  updatedAt?: Timestamp;
}

// Seller address for shipping origin
export interface SellerAddress {
  id?: string;
  name: string;
  street1: string;
  street2?: string;
  city: string;
  state: string;
  zip: string;
  country: string;
  phone?: string;
  isDefault?: boolean;
  createdAt?: Timestamp;
  updatedAt?: Timestamp;
}

export type UserRole = 'student' | 'merchant' | 'admin';

// Listing related types
export interface Listing {
  id?: string;
  title: string;
  description: string;
  price: number;
  category: string;
  condition: ListingCondition;
  type: ListingType;
  ownerId: string;
  ownerName?: string;
  university: string;
  imageURLs: string[];
  status: ListingStatus;
  visibility: 'university' | 'public';
  createdAt: Timestamp;
  updatedAt?: Timestamp;

  // Delivery method fields
  deliveryMethod: 'in_person' | 'mail';
  shippingOptions?: {
    model: 'shippo' | 'manual';
    paidBy: 'buyer' | 'seller';
    packageSize?: 'small' | 'medium' | 'large';
    estimatedCost?: number;
    allowBuyerChoice?: boolean;
    // Enhanced package details for Shippo
    packageDetails?: {
      dimensions?: {
        length: number;
        width: number;
        height: number;
        unit: 'in' | 'cm';
      };
      weight?: {
        value: number;
        unit: 'oz' | 'lb';
      };
      presetUsed?: string;
    };
    sellerAddress?: {
      name: string;
      street1: string;
      street2?: string;
      city: string;
      state: string;
      zip: string;
      country: string;
      phone?: string;
    };
  };

  // Rent-specific fields
  rentalPeriod?: 'weekly' | 'monthly';
  weeklyPrice?: number;
  monthlyPrice?: number;
  startDate?: string;
  endDate?: string;

  // Auction-specific fields
  startingBid?: number;
  currentBid?: number;
  auctionStartDate?: string;
  auctionStartTime?: string;
  auctionEndDate?: string;
  auctionEndTime?: string;
  auctionDuration?: number;
  bidders?: string[];
  highestBidder?: string;

  // Admin moderation fields
  featured?: boolean;
  featuredAt?: Timestamp;
  featuredBy?: string;
  flagReason?: string;
  flaggedAt?: Timestamp;
  flaggedBy?: string;
  blockReason?: string;
  blockedAt?: Timestamp;
  blockedBy?: string;
  banReason?: string;
  bannedAt?: Timestamp;
  bannedBy?: string;
  moderatedAt?: Timestamp;
  moderatedBy?: string;
}

export type ListingCondition = 'new' | 'like_new' | 'very_good' | 'good' | 'fair' | 'poor';
export type ListingType = 'sell' | 'rent' | 'auction';
export type ListingStatus = 'active' | 'sold' | 'pending' | 'deleted' | 'flagged' | 'blocked' | 'banned';

// Message related types
export interface Chat {
  id: string;
  participants: string[];
  lastMessage?: {
    text?: string;
    type: 'text' | 'image';
    senderId: string;
    createdAt: Timestamp;
  };
  unreadCount?: number;
  listingContext?: {
    listingId: string;
    listingTitle: string;
    listingImageURL?: string;
  };
  createdAt: Timestamp;
  updatedAt?: Timestamp;
}

export interface Message {
  id?: string;
  senderId: string;
  receiverId: string;
  text?: string;
  type: 'text' | 'image';
  createdAt: Timestamp;
  read: boolean;
  imageData?: {
    url: string;
    fileName: string;
    fileSize: number;
    fileType: string;
    storagePath: string;
  };
  listingContext?: {
    listingId: string;
    title: string;
    imageURL?: string;
  };
}

// Feedback related types
export interface Feedback {
  id?: string;
  userId: string;
  userName?: string;
  rating: number;
  message: string;
  createdAt: Timestamp;
}

export interface IssueReport {
  id?: string;
  userId: string;
  userName?: string;
  category: string;
  title: string;
  description: string;
  screenshotURL?: string;
  status: 'open' | 'in_progress' | 'resolved' | 'closed';
  createdAt: Timestamp;
  updatedAt?: Timestamp;
}

// Order related types
export type OrderStatus =
  | 'pending_payment'
  | 'payment_processing'
  | 'payment_succeeded'
  | 'payment_completed'  // Legacy status, should use payment_succeeded
  | 'in_progress'
  | 'awaiting_shipment'
  | 'label_created'
  | 'in_transit'
  | 'shipped_pending_code'
  | 'shipped'
  | 'delivered'
  | 'completed'
  | 'cancelled'
  | 'refunded';

export interface Order {
  id: string;
  buyerId: string;
  sellerId: string;
  listingId: string;
  amount: number;
  commission?: number;
  commissionRate?: number;
  commissionAmount?: number;
  sellerAmount?: number;
  paid?: boolean;
  status: OrderStatus;
  paymentIntentId?: string;
  stripeSessionId?: string;
  stripePaymentIntentId?: string;
  stripeTransferId?: string;
  category?: string;
  title: string;
  description?: string;
  shippingAddress?: ShippingAddress;
  trackingInfo?: TrackingInfo;
  secretCode?: string;
  walletAmountUsed?: number;
  cashbackAmount?: number;
  createdAt: Timestamp;
  updatedAt?: Timestamp;
  completedAt?: Timestamp;
  releasedAt?: Timestamp;
  deliveredAt?: Timestamp;
  autoReleaseDate?: Timestamp;
  escrowReleaseDate?: Timestamp;

  // Enhanced delivery and shipping fields
  deliveryMethod: 'in_person' | 'mail';
  shippingModel?: 'shippo' | 'manual';
  shippingPaidBy?: 'buyer' | 'seller';
  shippingFee?: number;
  shipping?: {
    packageSize?: 'small' | 'medium' | 'large';
    labelUrl?: string;
    trackingNumber?: string;
    trackingUrl?: string;
    carrier?: string;
    service?: string;
    rateId?: string;
    labelCreatedAt?: Timestamp;
    shippedAt?: Timestamp;
    estimatedDeliveryDate?: Timestamp;
    trackingInfo?: any;
    lastUpdated?: Timestamp;
  };

  paymentMethod?: string;
  isTextbook?: boolean;
  // Enhanced escrow fields
  sellerOnboarded?: boolean;
  pendingPayout?: boolean;
  stripeAccountId?: string | null;
  onboardingReminderSent?: boolean;
  onboardingReminderCount?: number;
  // Order type and action details
  orderType?: 'buy' | 'rent' | 'bid';
  rentalPeriod?: 'weekly' | 'monthly';
  bidAmount?: number;
}

export interface ShippingAddress {
  name: string;
  line1: string;
  line2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
  phone?: string;
}

export interface TrackingInfo {
  carrier: string;
  trackingNumber: string;
  labelUrl?: string;
  shippedDate?: Timestamp;
  estimatedDeliveryDate?: Timestamp;
}

// Wallet related types
export interface Wallet {
  userId: string;
  balance: number;
  referralCode: string;
  usedReferral: boolean;
  history: WalletTransaction[];
  grantedBy: 'admin' | 'referral' | 'signup' | 'cashback';
  lastUpdated: Timestamp;
  createdAt: Timestamp;
}

export interface WalletTransaction {
  id: string;
  type: 'credit' | 'debit';
  amount: number;
  description: string;
  source: 'admin_grant' | 'referral_bonus' | 'signup_bonus' | 'cashback' | 'purchase_deduction';
  orderId?: string;
  grantedBy?: string; // admin uid if granted by admin
  referralUserId?: string; // uid of user who used referral code
  expiresAt?: Timestamp; // optional expiration date for credits
  isExpired?: boolean; // flag for expired credits
  createdAt: Timestamp;
}

export interface WalletCredit {
  id: string;
  amount: number;
  source: 'admin_grant' | 'referral_bonus' | 'signup_bonus' | 'cashback';
  description: string;
  grantedBy?: string;
  expiresAt?: Timestamp;
  isExpired: boolean;
  createdAt: Timestamp;
}

export interface WalletAnalytics {
  userId: string;
  totalCreditsEarned: number;
  totalCreditsUsed: number;
  referralCount: number;
  lastActivity: Timestamp;
  suspiciousActivity: boolean;
  activityScore: number; // 0-100 based on usage patterns
}

export interface ReferralCode {
  code: string;
  userId: string;
  usedBy: string[];
  totalRewards: number;
  isActive: boolean;
  createdAt: Timestamp;
  updatedAt?: Timestamp;
}

// Shipping label related types
export interface ShippingLabel {
  orderId: string;
  carrier: string;
  trackingNumber: string;
  labelUrl: string;
  createdAt: Timestamp;
}

// Secret code related types
export interface SecretCode {
  orderId: string;
  code: string;
  expiresAt: Timestamp;
  isUsed: boolean;
  usedAt?: Timestamp;
}

// Connect account related types
export interface ConnectAccount {
  userId: string;
  stripeAccountId: string;
  isOnboarded: boolean;
  accountType: 'student' | 'merchant';
  createdAt: Timestamp;
  updatedAt?: Timestamp;
}
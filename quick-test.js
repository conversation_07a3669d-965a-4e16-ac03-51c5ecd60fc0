console.log('🧪 Quick test starting...');

async function quickTest() {
  try {
    console.log('📡 Testing webhook endpoint...');
    
    const response = await fetch('https://us-central1-h1c1-798a8.cloudfunctions.net/stripeWebhook', {
      method: 'GET'
    });
    
    console.log(`Status: ${response.status} ${response.statusText}`);
    
    if (response.status === 405) {
      console.log('✅ Webhook is working correctly!');
      console.log('🎉 Ready for payment testing!');
    } else {
      console.log('⚠️ Unexpected response');
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
}

quickTest();

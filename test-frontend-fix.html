<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend Fix Test - Stripe Checkout</title>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore-compat.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Frontend Fix Test - Stripe Checkout</h1>
        <p>This page demonstrates the correct way to call the stripeApi function.</p>

        <!-- Authentication -->
        <div class="test-section">
            <h3>🔐 Authentication</h3>
            <button onclick="signInAnonymously()">Sign In Anonymously</button>
            <button onclick="signOut()">Sign Out</button>
            <div id="auth-status"></div>
        </div>

        <!-- Create Test Listing -->
        <div class="test-section">
            <h3>📚 Test Listing</h3>
            <button onclick="createTestListing()" id="create-listing-btn" disabled>Create Test Listing</button>
            <div id="listing-status"></div>
        </div>

        <!-- Test Stripe API Call -->
        <div class="test-section">
            <h3>💳 Test Stripe API Call</h3>
            <p>This shows the CORRECT way to call the stripeApi function:</p>
            <button onclick="testStripeApiCall()" id="test-stripe-btn" disabled>Test Stripe API Call</button>
            <div id="stripe-status"></div>
        </div>

        <!-- Frontend Code Example -->
        <div class="test-section">
            <h3>📝 Frontend Code Fix</h3>
            <p><strong>Replace your frontend code with this:</strong></p>
            <pre id="code-example"></pre>
        </div>

        <!-- Logs -->
        <div class="test-section">
            <h3>📋 Logs</h3>
            <button onclick="clearLogs()">Clear Logs</button>
            <div id="logs" class="log"></div>
        </div>
    </div>

    <script>
        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBnF-pVKlXNdwBQ-74SEfw7NHJLLmL5e6A",
            authDomain: "h1c1-798a8.firebaseapp.com",
            projectId: "h1c1-798a8",
            storageBucket: "h1c1-798a8.firebasestorage.app",
            messagingSenderId: "1096652648176",
            appId: "1:1096652648176:web:4caac283b87d55a4ba9c35",
            measurementId: "G-6WQNVK6V4K"
        };

        // Initialize Firebase
        let app, auth, db;
        try {
            app = firebase.initializeApp(firebaseConfig);
            auth = firebase.auth();
            db = firebase.firestore();
            console.log('Firebase initialized successfully');
        } catch (error) {
            console.error('Firebase initialization error:', error);
        }

        // Global variables
        let currentUser = null;
        let testListingId = null;

        // Logging function
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('logs');
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(logEntry);
        }

        function clearLogs() {
            document.getElementById('logs').textContent = '';
        }

        // Authentication functions
        async function signInAnonymously() {
            try {
                log('Signing in anonymously...');
                const result = await auth.signInAnonymously();
                currentUser = result.user;
                log(`✅ Signed in as: ${currentUser.uid}`);
                document.getElementById('auth-status').innerHTML = `<span style="color: green;">✅ Signed in: ${currentUser.uid}</span>`;
                document.getElementById('create-listing-btn').disabled = false;
            } catch (error) {
                log(`❌ Sign in failed: ${error.message}`, 'error');
                document.getElementById('auth-status').innerHTML = '<span style="color: red;">❌ Sign in failed</span>';
            }
        }

        async function signOut() {
            try {
                await auth.signOut();
                currentUser = null;
                log('✅ Signed out');
                document.getElementById('auth-status').innerHTML = '<span style="color: gray;">Signed out</span>';
                document.getElementById('create-listing-btn').disabled = true;
                document.getElementById('test-stripe-btn').disabled = true;
            } catch (error) {
                log(`❌ Sign out failed: ${error.message}`, 'error');
            }
        }

        // Create test listing
        async function createTestListing() {
            if (!currentUser) {
                log('❌ Please sign in first', 'error');
                return;
            }

            try {
                log('Creating test listing...');
                
                const listingData = {
                    title: 'Test Textbook - Frontend Fix',
                    description: 'Testing the frontend fix for Stripe checkout',
                    price: 19.99,
                    category: 'textbooks',
                    condition: 'excellent',
                    userId: currentUser.uid,
                    userName: 'Test User',
                    userEmail: currentUser.email || '<EMAIL>',
                    images: ['https://via.placeholder.com/300x400?text=Test+Book'],
                    status: 'active',
                    createdAt: firebase.firestore.FieldValue.serverTimestamp(),
                    updatedAt: firebase.firestore.FieldValue.serverTimestamp()
                };

                const docRef = await db.collection('listings').add(listingData);
                testListingId = docRef.id;
                
                log(`✅ Test listing created: ${testListingId}`);
                document.getElementById('listing-status').innerHTML = `<span style="color: green;">✅ Listing created: ${testListingId}</span>`;
                document.getElementById('test-stripe-btn').disabled = false;
                
            } catch (error) {
                log(`❌ Failed to create listing: ${error.message}`, 'error');
                document.getElementById('listing-status').innerHTML = '<span style="color: red;">❌ Failed to create listing</span>';
            }
        }

        // Test Stripe API call (CORRECT implementation)
        async function testStripeApiCall() {
            if (!currentUser || !testListingId) {
                log('❌ Please sign in and create a listing first', 'error');
                return;
            }

            try {
                log('🔥 Testing Stripe API call with CORRECT implementation...');
                
                // Get the current user's ID token
                const idToken = await currentUser.getIdToken();
                log('✅ Got user ID token');
                
                // Make the API call with proper authentication
                const response = await fetch('https://us-central1-h1c1-798a8.cloudfunctions.net/stripeApi/create-checkout-session', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${idToken}`
                    },
                    body: JSON.stringify({
                        listingId: testListingId,
                        quantity: 1
                    })
                });

                log(`📊 Response status: ${response.status}`);
                
                if (response.ok) {
                    const data = await response.json();
                    log('✅ SUCCESS! Stripe API call worked!');
                    log(`Session ID: ${data.sessionId}`);
                    log(`Session URL: ${data.sessionUrl}`);
                    
                    document.getElementById('stripe-status').innerHTML = `
                        <div style="color: green;">
                            <strong>✅ SUCCESS!</strong><br>
                            Session ID: ${data.sessionId}<br>
                            <a href="${data.sessionUrl}" target="_blank">Open Stripe Checkout</a>
                        </div>
                    `;
                    
                    // Optionally redirect to Stripe Checkout
                    if (confirm('Open Stripe Checkout in new tab?')) {
                        window.open(data.sessionUrl, '_blank');
                    }
                    
                } else {
                    const errorText = await response.text();
                    log(`❌ API call failed: ${response.status} - ${errorText}`, 'error');
                    document.getElementById('stripe-status').innerHTML = `<span style="color: red;">❌ Failed: ${response.status}</span>`;
                }
                
            } catch (error) {
                log(`❌ Error testing Stripe API: ${error.message}`, 'error');
                document.getElementById('stripe-status').innerHTML = `<span style="color: red;">❌ Error: ${error.message}</span>`;
            }
        }

        // Show the correct frontend code
        function showFrontendCode() {
            const codeExample = `
// CORRECT way to call the Stripe API function:

async function createCheckoutSession(listingId, quantity = 1) {
    try {
        // Get current user's ID token
        const user = firebase.auth().currentUser;
        if (!user) {
            throw new Error('User not authenticated');
        }
        
        const idToken = await user.getIdToken();
        
        // Make API call with proper authentication
        const response = await fetch('https://us-central1-h1c1-798a8.cloudfunctions.net/stripeApi/create-checkout-session', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': \`Bearer \${idToken}\`
            },
            body: JSON.stringify({
                listingId: listingId,
                quantity: quantity
            })
        });

        if (!response.ok) {
            throw new Error(\`HTTP error! status: \${response.status}\`);
        }

        const data = await response.json();
        
        // Redirect to Stripe Checkout
        window.location.href = data.sessionUrl;
        
    } catch (error) {
        console.error('Checkout error:', error);
        // Show user-friendly error message
        alert('Payment processing error: ' + error.message);
    }
}

// Call this function when user clicks "Buy Now"
// createCheckoutSession('your-listing-id', 1);
            `;
            
            document.getElementById('code-example').textContent = codeExample;
        }

        // Initialize page
        window.onload = function() {
            log('🚀 Frontend fix test page loaded');
            showFrontendCode();
        };
    </script>
</body>
</html>

import { useState, useEffect, useCallback } from 'react';
import { 
  collection, 
  doc, 
  getDocs, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  orderBy, 
  limit,
  Timestamp 
} from 'firebase/firestore';
import { firestore } from '../firebase/config';
import { useAuth } from './useAuth';
import { UserAddress } from '../firebase/types';

interface UseAddressManagementReturn {
  addresses: UserAddress[];
  isLoading: boolean;
  error: string | null;
  addAddress: (address: Omit<UserAddress, 'id' | 'createdAt' | 'updatedAt'>) => Promise<string>;
  updateAddress: (addressId: string, updates: Partial<UserAddress>) => Promise<void>;
  deleteAddress: (addressId: string) => Promise<void>;
  setDefaultAddress: (addressId: string) => Promise<void>;
  getDefaultAddress: () => UserAddress | null;
  refreshAddresses: () => Promise<void>;
}

export const useAddressManagement = (): UseAddressManagementReturn => {
  const [addresses, setAddresses] = useState<UserAddress[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { currentUser } = useAuth();

  // Fetch user addresses
  const fetchAddresses = useCallback(async () => {
    if (!currentUser) {
      setAddresses([]);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const addressesRef = collection(firestore, 'users', currentUser.uid, 'addresses');
      const q = query(addressesRef, orderBy('createdAt', 'desc'), limit(2));
      const snapshot = await getDocs(q);

      const fetchedAddresses: UserAddress[] = [];
      snapshot.forEach((doc) => {
        fetchedAddresses.push({
          id: doc.id,
          ...doc.data()
        } as UserAddress);
      });

      setAddresses(fetchedAddresses);
    } catch (err: any) {
      console.error('Error fetching addresses:', err);

      // Handle permission errors gracefully
      if (err.code === 'permission-denied') {
        console.log('No addresses found or permission denied - this is normal for new users');
        setAddresses([]);
        setError(null); // Don't show error for permission denied on empty collection
      } else {
        setError('Failed to load addresses');
      }
    } finally {
      setIsLoading(false);
    }
  }, [currentUser]);

  // Add new address
  const addAddress = async (addressData: Omit<UserAddress, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> => {
    if (!currentUser) throw new Error('User not authenticated');

    // Check if user already has 2 addresses
    if (addresses.length >= 2) {
      throw new Error('You can only save up to 2 addresses. Please delete an existing address first.');
    }

    setError(null);

    try {
      const addressesRef = collection(firestore, 'users', currentUser.uid, 'addresses');
      
      // If this is the first address or marked as default, make it default
      const isFirstAddress = addresses.length === 0;
      const shouldBeDefault = isFirstAddress || addressData.isDefault;

      // If setting as default, update other addresses to not be default
      if (shouldBeDefault && addresses.length > 0) {
        for (const addr of addresses) {
          if (addr.isDefault) {
            await updateDoc(doc(firestore, 'users', currentUser.uid, 'addresses', addr.id), {
              isDefault: false,
              updatedAt: Timestamp.now()
            });
          }
        }
      }

      const newAddress = {
        ...addressData,
        isDefault: shouldBeDefault,
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      };

      const docRef = await addDoc(addressesRef, newAddress);
      
      // Refresh addresses
      await fetchAddresses();
      
      return docRef.id;
    } catch (err) {
      console.error('Error adding address:', err);
      setError('Failed to add address');
      throw err;
    }
  };

  // Update address
  const updateAddress = async (addressId: string, updates: Partial<UserAddress>): Promise<void> => {
    if (!currentUser) throw new Error('User not authenticated');

    setError(null);

    try {
      const addressRef = doc(firestore, 'users', currentUser.uid, 'addresses', addressId);
      
      // If setting as default, update other addresses to not be default
      if (updates.isDefault) {
        for (const addr of addresses) {
          if (addr.id !== addressId && addr.isDefault) {
            await updateDoc(doc(firestore, 'users', currentUser.uid, 'addresses', addr.id), {
              isDefault: false,
              updatedAt: Timestamp.now()
            });
          }
        }
      }

      await updateDoc(addressRef, {
        ...updates,
        updatedAt: Timestamp.now()
      });

      // Refresh addresses
      await fetchAddresses();
    } catch (err) {
      console.error('Error updating address:', err);
      setError('Failed to update address');
      throw err;
    }
  };

  // Delete address
  const deleteAddress = async (addressId: string): Promise<void> => {
    if (!currentUser) throw new Error('User not authenticated');

    setError(null);

    try {
      const addressRef = doc(firestore, 'users', currentUser.uid, 'addresses', addressId);
      await deleteDoc(addressRef);

      // If deleted address was default and there are other addresses, make the first one default
      const deletedAddress = addresses.find(addr => addr.id === addressId);
      if (deletedAddress?.isDefault && addresses.length > 1) {
        const remainingAddresses = addresses.filter(addr => addr.id !== addressId);
        if (remainingAddresses.length > 0) {
          await updateAddress(remainingAddresses[0].id, { isDefault: true });
        }
      }

      // Refresh addresses
      await fetchAddresses();
    } catch (err) {
      console.error('Error deleting address:', err);
      setError('Failed to delete address');
      throw err;
    }
  };

  // Set default address
  const setDefaultAddress = async (addressId: string): Promise<void> => {
    await updateAddress(addressId, { isDefault: true });
  };

  // Get default address
  const getDefaultAddress = (): UserAddress | null => {
    return addresses.find(addr => addr.isDefault) || addresses[0] || null;
  };

  // Refresh addresses
  const refreshAddresses = async (): Promise<void> => {
    await fetchAddresses();
  };

  // Fetch addresses on mount and when user changes
  useEffect(() => {
    if (currentUser) {
      fetchAddresses();
    } else {
      setAddresses([]);
    }
  }, [currentUser, fetchAddresses]);

  return {
    addresses,
    isLoading,
    error,
    addAddress,
    updateAddress,
    deleteAddress,
    setDefaultAddress,
    getDefaultAddress,
    refreshAddresses
  };
};

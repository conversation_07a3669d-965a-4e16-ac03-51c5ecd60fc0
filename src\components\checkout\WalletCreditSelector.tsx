import React, { useState, useEffect } from 'react';
import {
  Wallet,
  DollarSign,
  Info,
  AlertCircle,
  CheckCircle,
  Minus,
  Plus
} from 'lucide-react';

interface WalletCreditSelectorProps {
  walletBalance: number;
  itemPrice: number;
  shippingFee?: number;
  onCreditChange: (appliedCredit: number) => void;
  isLoading?: boolean;
  disabled?: boolean;
}

const WalletCreditSelector: React.FC<WalletCreditSelectorProps> = ({
  walletBalance,
  itemPrice,
  shippingFee = 0,
  onCreditChange,
  isLoading = false,
  disabled = false
}) => {
  const [appliedCredit, setAppliedCredit] = useState(0);
  const [useFullBalance, setUseFullBalance] = useState(false);
  const [inputValue, setInputValue] = useState('0.00');
  const [inputError, setInputError] = useState('');

  const totalPrice = itemPrice + shippingFee;
  const maxApplicableCredit = Math.min(walletBalance, totalPrice);
  const finalTotal = Math.max(0, totalPrice - appliedCredit);

  // Update parent component when applied credit changes
  useEffect(() => {
    onCreditChange(appliedCredit);
  }, [appliedCredit, onCreditChange]);

  // Handle full balance toggle
  const handleFullBalanceToggle = (checked: boolean) => {
    setUseFullBalance(checked);
    if (checked) {
      const creditToApply = maxApplicableCredit;
      setAppliedCredit(creditToApply);
      setInputValue(creditToApply.toFixed(2));
      setInputError('');
    } else {
      setAppliedCredit(0);
      setInputValue('0.00');
      setInputError('');
    }
  };

  // Handle manual input change
  const handleInputChange = (value: string) => {
    setInputValue(value);
    setUseFullBalance(false);
    
    // Parse and validate input
    const numericValue = parseFloat(value) || 0;
    
    // Validation
    if (numericValue < 0) {
      setInputError('Credit amount cannot be negative');
      setAppliedCredit(0);
      return;
    }
    
    if (numericValue > walletBalance) {
      setInputError(`Cannot exceed wallet balance of $${walletBalance.toFixed(2)}`);
      setAppliedCredit(0);
      return;
    }
    
    if (numericValue > totalPrice) {
      setInputError(`Cannot exceed item total of $${totalPrice.toFixed(2)}`);
      setAppliedCredit(0);
      return;
    }
    
    // Valid input
    setInputError('');
    setAppliedCredit(numericValue);
  };

  // Handle quick amount buttons
  const handleQuickAmount = (percentage: number) => {
    const creditToApply = Math.min(maxApplicableCredit * percentage, maxApplicableCredit);
    setAppliedCredit(creditToApply);
    setInputValue(creditToApply.toFixed(2));
    setUseFullBalance(percentage === 1);
    setInputError('');
  };

  // Handle increment/decrement buttons
  const handleIncrement = (amount: number) => {
    const newCredit = Math.min(appliedCredit + amount, maxApplicableCredit);
    setAppliedCredit(newCredit);
    setInputValue(newCredit.toFixed(2));
    setUseFullBalance(newCredit === maxApplicableCredit);
    setInputError('');
  };

  const formatCurrency = (amount: number) => `$${amount.toFixed(2)}`;

  if (isLoading) {
    return (
      <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
        <div className="flex items-center space-x-2 mb-2">
          <Wallet className="w-4 h-4 text-gray-400 animate-pulse" />
          <span className="text-gray-400">Loading wallet...</span>
        </div>
      </div>
    );
  }

  if (walletBalance === 0) {
    return (
      <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-gray-50 dark:bg-gray-800/50">
        <div className="flex items-center space-x-2 mb-2">
          <Wallet className="w-4 h-4 text-gray-400" />
          <span className="text-gray-600 dark:text-gray-400">Hive Campus Wallet</span>
        </div>
        <p className="text-sm text-gray-500 dark:text-gray-400">
          No wallet credit available. Share your referral code or check with admin for promotional bonuses!
        </p>
      </div>
    );
  }

  return (
    <div className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 bg-white dark:bg-gray-800">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-2">
          <Wallet className="w-5 h-5 text-blue-600 dark:text-blue-400" />
          <span className="font-medium text-gray-900 dark:text-white">Hive Campus Wallet</span>
        </div>
        <div className="text-right">
          <p className="text-sm text-gray-600 dark:text-gray-400">Available Balance</p>
          <p className="font-semibold text-gray-900 dark:text-white">{formatCurrency(walletBalance)}</p>
        </div>
      </div>

      {/* Full Balance Toggle */}
      <div className="mb-4">
        <label className="flex items-center space-x-3 cursor-pointer">
          <input
            type="checkbox"
            checked={useFullBalance}
            onChange={(e) => handleFullBalanceToggle(e.target.checked)}
            disabled={disabled}
            className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600 disabled:opacity-50"
          />
          <span className="text-sm text-gray-700 dark:text-gray-300">
            Apply full wallet balance ({formatCurrency(maxApplicableCredit)})
          </span>
        </label>
      </div>

      {/* Custom Amount Input */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Or choose custom amount:
        </label>
        <div className="flex items-center space-x-2">
          <button
            type="button"
            onClick={() => handleIncrement(-1)}
            disabled={disabled || appliedCredit <= 0}
            className="p-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Minus className="w-4 h-4" />
          </button>
          
          <div className="flex-1 relative">
            <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <input
              type="number"
              step="0.01"
              min="0"
              max={maxApplicableCredit}
              value={inputValue}
              onChange={(e) => handleInputChange(e.target.value)}
              disabled={disabled}
              className={`w-full pl-10 pr-4 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white disabled:opacity-50 ${
                inputError 
                  ? 'border-red-300 dark:border-red-600' 
                  : 'border-gray-300 dark:border-gray-600'
              }`}
              placeholder="0.00"
            />
          </div>
          
          <button
            type="button"
            onClick={() => handleIncrement(1)}
            disabled={disabled || appliedCredit >= maxApplicableCredit}
            className="p-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <Plus className="w-4 h-4" />
          </button>
        </div>
        
        {inputError && (
          <div className="flex items-center space-x-1 mt-1">
            <AlertCircle className="w-4 h-4 text-red-500" />
            <span className="text-sm text-red-600 dark:text-red-400">{inputError}</span>
          </div>
        )}
      </div>

      {/* Quick Amount Buttons */}
      <div className="mb-4">
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">Quick amounts:</p>
        <div className="grid grid-cols-4 gap-2">
          {[0.25, 0.5, 0.75, 1].map((percentage) => {
            const amount = Math.min(maxApplicableCredit * percentage, maxApplicableCredit);
            return (
              <button
                key={percentage}
                type="button"
                onClick={() => handleQuickAmount(percentage)}
                disabled={disabled}
                className="px-3 py-2 text-xs border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {percentage === 1 ? 'Max' : `${percentage * 100}%`}
                <br />
                <span className="text-gray-500">{formatCurrency(amount)}</span>
              </button>
            );
          })}
        </div>
      </div>

      {/* Summary */}
      {appliedCredit > 0 && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-3">
          <div className="flex items-center space-x-2 mb-2">
            <CheckCircle className="w-4 h-4 text-green-600 dark:text-green-400" />
            <span className="text-sm font-medium text-green-800 dark:text-green-300">
              Credit Applied
            </span>
          </div>
          <div className="space-y-1 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600 dark:text-gray-400">Item Total:</span>
              <span className="text-gray-900 dark:text-white">{formatCurrency(totalPrice)}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-green-600 dark:text-green-400">Wallet Credit:</span>
              <span className="text-green-600 dark:text-green-400">-{formatCurrency(appliedCredit)}</span>
            </div>
            <div className="flex justify-between font-semibold border-t border-green-200 dark:border-green-700 pt-1">
              <span className="text-gray-900 dark:text-white">Amount to Pay:</span>
              <span className="text-gray-900 dark:text-white">{formatCurrency(finalTotal)}</span>
            </div>
          </div>
        </div>
      )}

      {/* Info */}
      <div className="mt-3 flex items-start space-x-2">
        <Info className="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0" />
        <p className="text-xs text-gray-600 dark:text-gray-400">
          Wallet credit will only be deducted after successful payment. 
          {finalTotal === 0 && ' A minimum charge may apply for processing.'}
        </p>
      </div>
    </div>
  );
};

export default WalletCreditSelector;

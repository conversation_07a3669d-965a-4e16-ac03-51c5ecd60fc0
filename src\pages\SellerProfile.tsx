import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { 
  Star, 
  Shield, 
  MapPin, 
  Calendar, 
  MessageCircle, 
  Flag, 
  Award, 
  Clock,
  Eye,
  Heart,
  Grid,
  List
} from 'lucide-react';

const SellerProfile: React.FC = () => {
  const [activeTab, setActiveTab] = useState('listings');
  const [layoutMode, setLayoutMode] = useState<'grid' | 'list'>('grid');

  // Mock seller data
  const seller = {
    id: 'alex-johnson',
    name: '<PERSON>',
    username: '@alexj_stanford',
    avatar: '/placeholder-avatar.svg',
    coverImage: '/placeholder-image.jpg',
    rating: 4.9,
    reviewCount: 127,
    verified: true,
    joinedDate: 'September 2023',
    university: 'Stanford University',
    location: 'Palo Alto, CA',
    bio: 'CS student passionate about tech and sustainable living. Always looking for good deals and happy to help fellow students find what they need!',
    stats: {
      totalSales: 45,
      totalListings: 8,
      responseRate: 98,
      responseTime: '< 1 hour',
      repeatCustomers: 23,
      averageRating: 4.9
    },
    badges: [
      { name: 'Top Seller', icon: Award, color: 'text-yellow-500' },
      { name: 'Fast Responder', icon: Clock, color: 'text-green-500' },
      { name: 'Verified Student', icon: Shield, color: 'text-blue-500' }
    ]
  };

  const listings = [
    {
      id: 1,
      title: 'iPhone 14 Pro - Space Black 256GB',
      price: 899,
      originalPrice: 1099,
      image: 'https://images.pexels.com/photos/699122/pexels-photo-699122.jpeg?auto=compress&cs=tinysrgb&w=400',
      status: 'active',
      views: 234,
      likes: 18,
      condition: 'Like New',
      postedDate: '2 days ago'
    },
    {
      id: 2,
      title: 'MacBook Air M2 - Space Gray',
      price: 1200,
      originalPrice: 1499,
      image: 'https://images.pexels.com/photos/205421/pexels-photo-205421.jpeg?auto=compress&cs=tinysrgb&w=400',
      status: 'sold',
      views: 456,
      likes: 32,
      condition: 'Excellent',
      postedDate: '1 week ago'
    },
    {
      id: 3,
      title: 'AirPods Pro 2nd Generation',
      price: 180,
      originalPrice: 249,
      image: 'https://images.pexels.com/photos/3756679/pexels-photo-3756679.jpeg?auto=compress&cs=tinysrgb&w=400',
      status: 'active',
      views: 123,
      likes: 9,
      condition: 'Very Good',
      postedDate: '3 days ago'
    },
    {
      id: 4,
      title: 'iPad Air 5th Gen - Blue',
      price: 450,
      originalPrice: 599,
      image: 'https://images.pexels.com/photos/1275229/pexels-photo-1275229.jpeg?auto=compress&cs=tinysrgb&w=400',
      status: 'active',
      views: 89,
      likes: 12,
      condition: 'Good',
      postedDate: '5 days ago'
    }
  ];

  const reviews = [
    {
      id: 1,
      reviewer: 'Sarah Chen',
      avatar: 'https://images.pexels.com/photos/1181519/pexels-photo-1181519.jpeg?auto=compress&cs=tinysrgb&w=100',
      rating: 5,
      comment: 'Amazing seller! The iPhone was exactly as described and Alex was super responsive. Highly recommend!',
      date: '2 days ago',
      item: 'iPhone 14 Pro'
    },
    {
      id: 2,
      reviewer: 'Mike Rodriguez',
      avatar: 'https://images.pexels.com/photos/1674752/pexels-photo-1674752.jpeg?auto=compress&cs=tinysrgb&w=100',
      rating: 5,
      comment: 'Great communication and fast delivery. The MacBook was in perfect condition!',
      date: '1 week ago',
      item: 'MacBook Air M2'
    },
    {
      id: 3,
      reviewer: 'Emma Wilson',
      avatar: 'https://images.pexels.com/photos/1239288/pexels-photo-1239288.jpeg?auto=compress&cs=tinysrgb&w=100',
      rating: 4,
      comment: 'Good seller, item as described. Quick and easy transaction.',
      date: '2 weeks ago',
      item: 'AirPods Pro'
    }
  ];

  const tabs = [
    { id: 'listings', label: 'Listings', count: listings.length },
    { id: 'reviews', label: 'Reviews', count: reviews.length },
    { id: 'about', label: 'About', count: null }
  ];

  const renderListings = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
          {seller.name}'s Listings ({listings.length})
        </h3>
        <div className="flex items-center space-x-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-xl p-1">
          <button
            onClick={() => setLayoutMode('grid')}
            className={`p-2 rounded-lg transition-all ${
              layoutMode === 'grid' 
                ? 'bg-primary-100 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400' 
                : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'
            }`}
          >
            <Grid className="w-4 h-4" />
          </button>
          <button
            onClick={() => setLayoutMode('list')}
            className={`p-2 rounded-lg transition-all ${
              layoutMode === 'list' 
                ? 'bg-primary-100 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400' 
                : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'
            }`}
          >
            <List className="w-4 h-4" />
          </button>
        </div>
      </div>

      {layoutMode === 'grid' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {listings.map((listing) => (
            <Link key={listing.id} to={`/listing/${listing.id}`}>
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden group">
                <div className="relative">
                  <img
                    src={listing.image}
                    alt={listing.title}
                    className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300"
                  />
                  <div className="absolute top-3 left-3">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      listing.status === 'active' 
                        ? 'bg-success-100 text-success-700' 
                        : 'bg-gray-100 text-gray-700'
                    }`}>
                      {listing.status}
                    </span>
                  </div>
                  <div className="absolute top-3 right-3">
                    <span className="bg-red-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                      {Math.round(((listing.originalPrice - listing.price) / listing.originalPrice) * 100)}% OFF
                    </span>
                  </div>
                </div>
                <div className="p-4">
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-2 line-clamp-2">
                    {listing.title}
                  </h4>
                  <div className="flex items-center space-x-2 mb-3">
                    <span className="text-xl font-bold text-primary-600 dark:text-primary-400">
                      ${listing.price}
                    </span>
                    <span className="text-sm text-gray-500 dark:text-gray-400 line-through">
                      ${listing.originalPrice}
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                    <span>{listing.condition}</span>
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center space-x-1">
                        <Eye className="w-3 h-3" />
                        <span>{listing.views}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Heart className="w-3 h-3" />
                        <span>{listing.likes}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      ) : (
        <div className="space-y-4">
          {listings.map((listing) => (
            <Link key={listing.id} to={`/listing/${listing.id}`}>
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 p-4 flex items-center space-x-4 group">
                <img
                  src={listing.image}
                  alt={listing.title}
                  className="w-20 h-20 rounded-lg object-cover group-hover:scale-110 transition-transform duration-300"
                />
                <div className="flex-1">
                  <h4 className="font-semibold text-gray-900 dark:text-white mb-1">
                    {listing.title}
                  </h4>
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-lg font-bold text-primary-600 dark:text-primary-400">
                      ${listing.price}
                    </span>
                    <span className="text-sm text-gray-500 dark:text-gray-400 line-through">
                      ${listing.originalPrice}
                    </span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      listing.status === 'active' 
                        ? 'bg-success-100 text-success-700' 
                        : 'bg-gray-100 text-gray-700'
                    }`}>
                      {listing.status}
                    </span>
                  </div>
                  <div className="flex items-center justify-between text-sm text-gray-500 dark:text-gray-400">
                    <span>{listing.condition} • {listing.postedDate}</span>
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center space-x-1">
                        <Eye className="w-3 h-3" />
                        <span>{listing.views}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <Heart className="w-3 h-3" />
                        <span>{listing.likes}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      )}
    </div>
  );

  const renderReviews = () => (
    <div className="space-y-6">
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
        Customer Reviews ({reviews.length})
      </h3>
      <div className="space-y-6">
        {reviews.map((review) => (
          <div key={review.id} className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm">
            <div className="flex items-start space-x-4">
              <img
                src={review.avatar}
                alt={review.reviewer}
                className="w-12 h-12 rounded-full object-cover"
              />
              <div className="flex-1">
                <div className="flex items-center justify-between mb-2">
                  <div>
                    <h4 className="font-semibold text-gray-900 dark:text-white">{review.reviewer}</h4>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Purchased: {review.item}</p>
                  </div>
                  <span className="text-sm text-gray-500 dark:text-gray-400">{review.date}</span>
                </div>
                <div className="flex items-center mb-3">
                  {[...Array(5)].map((_, i) => (
                    <Star
                      key={i}
                      className={`w-4 h-4 ${
                        i < review.rating
                          ? 'text-yellow-400 fill-current'
                          : 'text-gray-300 dark:text-gray-600'
                      }`}
                    />
                  ))}
                </div>
                <p className="text-gray-700 dark:text-gray-300">{review.comment}</p>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderAbout = () => (
    <div className="space-y-6">
      <div className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">About {seller.name}</h3>
        <p className="text-gray-700 dark:text-gray-300 leading-relaxed mb-6">
          {seller.bio}
        </p>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-semibold text-gray-900 dark:text-white mb-3">Details</h4>
            <div className="space-y-2 text-sm">
              <div className="flex items-center space-x-2">
                <Calendar className="w-4 h-4 text-gray-400" />
                <span className="text-gray-600 dark:text-gray-400">Joined {seller.joinedDate}</span>
              </div>
              <div className="flex items-center space-x-2">
                <MapPin className="w-4 h-4 text-gray-400" />
                <span className="text-gray-600 dark:text-gray-400">{seller.location}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Shield className="w-4 h-4 text-gray-400" />
                <span className="text-gray-600 dark:text-gray-400">{seller.university}</span>
              </div>
            </div>
          </div>
          
          <div>
            <h4 className="font-semibold text-gray-900 dark:text-white mb-3">Achievements</h4>
            <div className="space-y-2">
              {seller.badges.map((badge, index) => {
                const IconComponent = badge.icon;
                return (
                  <div key={index} className="flex items-center space-x-2">
                    <IconComponent className={`w-4 h-4 ${badge.color}`} />
                    <span className="text-sm text-gray-700 dark:text-gray-300">{badge.name}</span>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 md:ml-64 overflow-x-hidden">
      <div className="max-w-6xl mx-auto px-2 sm:px-4 lg:px-6 xl:px-8 py-4 sm:py-6 lg:py-8 w-full">
        {/* Cover & Profile Header */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden mb-8">
          {/* Cover Image */}
          <div className="h-48 bg-gradient-to-r from-primary-500 to-accent-500 relative">
            <img
              src={seller.coverImage}
              alt="Cover"
              className="w-full h-full object-cover opacity-20"
            />
          </div>
          
          {/* Profile Info */}
          <div className="relative px-6 pb-6">
            <div className="flex flex-col md:flex-row md:items-end md:space-x-6 -mt-16">
              <div className="relative">
                <img
                  src={seller.avatar}
                  alt={seller.name}
                  className="w-32 h-32 rounded-2xl object-cover border-4 border-white dark:border-gray-800 shadow-lg"
                />
                {seller.verified && (
                  <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-primary-500 rounded-full flex items-center justify-center border-4 border-white dark:border-gray-800">
                    <Shield className="w-4 h-4 text-white" />
                  </div>
                )}
              </div>
              
              <div className="flex-1 mt-4 md:mt-0">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between">
                  <div>
                    <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                      {seller.name}
                    </h1>
                    <p className="text-gray-600 dark:text-gray-400 mb-2">{seller.username}</p>
                    <div className="flex items-center space-x-4 mb-4">
                      <div className="flex items-center space-x-1">
                        <Star className="w-5 h-5 text-yellow-400 fill-current" />
                        <span className="font-semibold text-gray-900 dark:text-white">{seller.rating}</span>
                        <span className="text-gray-500 dark:text-gray-400">({seller.reviewCount} reviews)</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <MapPin className="w-4 h-4 text-gray-400" />
                        <span className="text-gray-600 dark:text-gray-400">{seller.university}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-3">
                    <button className="p-2 border border-gray-300 dark:border-gray-600 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                      <Flag className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                    </button>
                    <button className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl font-semibold transition-colors flex items-center space-x-2">
                      <MessageCircle className="w-5 h-5" />
                      <span>Message</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 text-center">
            <div className="text-3xl font-bold text-primary-600 dark:text-primary-400 mb-2">
              {seller.stats.totalSales}
            </div>
            <p className="text-gray-600 dark:text-gray-400">Total Sales</p>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 text-center">
            <div className="text-3xl font-bold text-success-600 dark:text-success-400 mb-2">
              {seller.stats.responseRate}%
            </div>
            <p className="text-gray-600 dark:text-gray-400">Response Rate</p>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 text-center">
            <div className="text-3xl font-bold text-accent-600 dark:text-accent-400 mb-2">
              {seller.stats.responseTime}
            </div>
            <p className="text-gray-600 dark:text-gray-400">Response Time</p>
          </div>
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-6 text-center">
            <div className="text-3xl font-bold text-purple-600 dark:text-purple-400 mb-2">
              {seller.stats.repeatCustomers}
            </div>
            <p className="text-gray-600 dark:text-gray-400">Repeat Customers</p>
          </div>
        </div>

        {/* Tabs */}
        <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg overflow-hidden">
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="flex space-x-8 px-6">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-4 px-2 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-primary-500 text-primary-600 dark:text-primary-400'
                      : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300'
                  }`}
                >
                  {tab.label}
                  {tab.count !== null && (
                    <span className="ml-2 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 px-2 py-1 rounded-full text-xs">
                      {tab.count}
                    </span>
                  )}
                </button>
              ))}
            </nav>
          </div>
          
          <div className="p-6">
            {activeTab === 'listings' && renderListings()}
            {activeTab === 'reviews' && renderReviews()}
            {activeTab === 'about' && renderAbout()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SellerProfile;
// Simple commission calculation test
console.log('Testing Commission Structure:');
console.log('============================');

// Test case 1: $3 textbook (should be $0.50 flat fee)
const price1 = 3;
const category1 = 'textbooks';
const isTextbook1 = true;
const fee1 = price1 <= 5 ? 0.50 : price1 * (isTextbook1 ? 0.08 : 0.10);
const seller1 = price1 - fee1;
console.log(`$${price1} ${category1}: Fee=$${fee1}, Seller=$${seller1} ✅`);

// Test case 2: $8 textbook (should be 8% = $0.64)
const price2 = 8;
const category2 = 'textbooks';
const isTextbook2 = true;
const fee2 = price2 <= 5 ? 0.50 : price2 * (isTextbook2 ? 0.08 : 0.10);
const seller2 = price2 - fee2;
console.log(`$${price2} ${category2}: Fee=$${fee2.toFixed(2)}, Seller=$${seller2.toFixed(2)} ✅`);

// Test case 3: $20 textbook (should be 8% = $1.60)
const price3 = 20;
const category3 = 'textbooks';
const isTextbook3 = true;
const fee3 = price3 <= 5 ? 0.50 : price3 * (isTextbook3 ? 0.08 : 0.10);
const seller3 = price3 - fee3;
console.log(`$${price3} ${category3}: Fee=$${fee3.toFixed(2)}, Seller=$${seller3.toFixed(2)} ✅`);

// Test case 4: $10 electronics (should be 10% = $1.00)
const price4 = 10;
const category4 = 'electronics';
const isTextbook4 = false;
const fee4 = price4 <= 5 ? 0.50 : price4 * (isTextbook4 ? 0.08 : 0.10);
const seller4 = price4 - fee4;
console.log(`$${price4} ${category4}: Fee=$${fee4.toFixed(2)}, Seller=$${seller4.toFixed(2)} ✅`);

console.log('\nCommission tests completed successfully! 🎉');

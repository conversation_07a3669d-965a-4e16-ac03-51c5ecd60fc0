"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.onEscrowRelease = exports.onSecretCodeVerification = exports.onShippingLabelCreate = exports.onWalletTransactionCreate = exports.onReferralCreate = exports.onIssueCreate = exports.onFeedbackCreate = exports.onOrderCreate = exports.onListingUpdate = exports.onListingCreate = exports.onUserCreate = void 0;
exports.createAdminNotification = createAdminNotification;
const functions = __importStar(require("firebase-functions/v1"));
const admin = __importStar(require("firebase-admin"));
// Initialize Firebase Admin if not already initialized
if (!admin.apps.length) {
    admin.initializeApp();
}
const db = admin.firestore();
// Helper function to create admin notification
async function createAdminNotification(type, title, message, options = {}) {
    try {
        const iconMap = {
            user_signup: '👋',
            listing_created: '📦',
            listing_sold: '💰',
            listing_updated: '✏️',
            order_created: '🛒',
            payment_completed: '✅',
            payment_failed: '❌',
            feedback_submitted: '💬',
            referral_bonus: '🎁',
            dispute_created: '⚠️',
            wallet_used: '💳',
            wallet_added: '💰',
            shipping_label_created: '📮',
            secret_code_verified: '🔓',
            escrow_released: '🏦',
            user_issue: '🚨',
            system_error: '⚡'
        };
        await db.collection('admin_notifications').add({
            type,
            title,
            message,
            icon: iconMap[type] || '📢',
            userId: options.userId || null,
            username: options.username || null,
            orderId: options.orderId || null,
            listingId: options.listingId || null,
            amount: options.amount || null,
            metadata: options.metadata || {},
            actionUrl: options.actionUrl || null,
            read: false,
            createdAt: admin.firestore.FieldValue.serverTimestamp()
        });
        console.log(`Admin notification created: ${type} - ${title}`);
    }
    catch (error) {
        console.error('Error creating admin notification:', error);
    }
}
// Helper function to get user data
async function getUserData(userId) {
    var _a;
    try {
        const userDoc = await db.collection('users').doc(userId).get();
        if (userDoc.exists) {
            const userData = userDoc.data();
            return {
                username: (userData === null || userData === void 0 ? void 0 : userData.name) || (userData === null || userData === void 0 ? void 0 : userData.displayName) || ((_a = userData === null || userData === void 0 ? void 0 : userData.email) === null || _a === void 0 ? void 0 : _a.split('@')[0]),
                email: userData === null || userData === void 0 ? void 0 : userData.email
            };
        }
        return null;
    }
    catch (error) {
        console.error('Error getting user data:', error);
        return null;
    }
}
// Firestore Triggers
// 1. New user signup
exports.onUserCreate = functions.auth.user().onCreate(async (user) => {
    try {
        const { uid, email, displayName } = user;
        const username = displayName || (email === null || email === void 0 ? void 0 : email.split('@')[0]) || 'Unknown User';
        await createAdminNotification('user_signup', 'New User Signup', `${username} has joined the platform`, {
            userId: uid,
            username,
            metadata: { email }
        });
    }
    catch (error) {
        console.error('Error in onUserCreate notification:', error);
    }
});
// 2. New listing created
exports.onListingCreate = functions.firestore
    .document('listings/{listingId}')
    .onCreate(async (snap, context) => {
    try {
        const listingData = snap.data();
        const listingId = context.params.listingId;
        const userData = await getUserData(listingData.ownerId);
        await createAdminNotification('listing_created', 'New Listing Created', `${(userData === null || userData === void 0 ? void 0 : userData.username) || 'User'} created a new listing: ${listingData.title}`, {
            userId: listingData.ownerId,
            username: userData === null || userData === void 0 ? void 0 : userData.username,
            listingId,
            amount: listingData.price,
            metadata: {
                category: listingData.category,
                condition: listingData.condition
            },
            actionUrl: `/admin/listings?search=${listingId}`
        });
    }
    catch (error) {
        console.error('Error in onListingCreate notification:', error);
    }
});
// 3. Listing updated (sold or status change)
exports.onListingUpdate = functions.firestore
    .document('listings/{listingId}')
    .onUpdate(async (change, context) => {
    try {
        const before = change.before.data();
        const after = change.after.data();
        const listingId = context.params.listingId;
        // Check if listing was sold
        if (before.status !== 'sold' && after.status === 'sold') {
            const userData = await getUserData(after.ownerId);
            await createAdminNotification('listing_sold', 'Listing Sold', `${(userData === null || userData === void 0 ? void 0 : userData.username) || 'User'} sold: ${after.title}`, {
                userId: after.ownerId,
                username: userData === null || userData === void 0 ? void 0 : userData.username,
                listingId,
                amount: after.price,
                metadata: {
                    category: after.category,
                    buyerId: after.buyerId
                },
                actionUrl: `/admin/listings?search=${listingId}`
            });
        }
        // Check for other significant updates
        else if (before.status !== after.status || before.price !== after.price) {
            const userData = await getUserData(after.ownerId);
            await createAdminNotification('listing_updated', 'Listing Updated', `${(userData === null || userData === void 0 ? void 0 : userData.username) || 'User'} updated listing: ${after.title}`, {
                userId: after.ownerId,
                username: userData === null || userData === void 0 ? void 0 : userData.username,
                listingId,
                amount: after.price,
                metadata: {
                    oldStatus: before.status,
                    newStatus: after.status,
                    oldPrice: before.price,
                    newPrice: after.price
                },
                actionUrl: `/admin/listings?search=${listingId}`
            });
        }
    }
    catch (error) {
        console.error('Error in onListingUpdate notification:', error);
    }
});
// 4. New order created
exports.onOrderCreate = functions.firestore
    .document('orders/{orderId}')
    .onCreate(async (snap, context) => {
    var _a;
    try {
        const orderData = snap.data();
        const orderId = context.params.orderId;
        const buyerData = await getUserData(orderData.buyerId);
        const sellerData = await getUserData(orderData.sellerId);
        await createAdminNotification('order_created', 'New Order Created', `${(buyerData === null || buyerData === void 0 ? void 0 : buyerData.username) || 'User'} placed an order for $${orderData.totalAmount}`, {
            userId: orderData.buyerId,
            username: buyerData === null || buyerData === void 0 ? void 0 : buyerData.username,
            orderId,
            amount: orderData.totalAmount,
            metadata: {
                sellerId: orderData.sellerId,
                sellerName: sellerData === null || sellerData === void 0 ? void 0 : sellerData.username,
                itemCount: ((_a = orderData.items) === null || _a === void 0 ? void 0 : _a.length) || 1
            },
            actionUrl: `/admin/transactions?search=${orderId}`
        });
    }
    catch (error) {
        console.error('Error in onOrderCreate notification:', error);
    }
});
// 5. Feedback submitted
exports.onFeedbackCreate = functions.firestore
    .document('feedback/{feedbackId}')
    .onCreate(async (snap, context) => {
    try {
        const feedbackData = snap.data();
        const feedbackId = context.params.feedbackId;
        const userData = await getUserData(feedbackData.userId);
        await createAdminNotification('feedback_submitted', 'New Feedback Submitted', `${(userData === null || userData === void 0 ? void 0 : userData.username) || 'User'} submitted feedback: ${feedbackData.type}`, {
            userId: feedbackData.userId,
            username: userData === null || userData === void 0 ? void 0 : userData.username,
            metadata: {
                type: feedbackData.type,
                rating: feedbackData.rating,
                category: feedbackData.category
            },
            actionUrl: '/admin/reports'
        });
    }
    catch (error) {
        console.error('Error in onFeedbackCreate notification:', error);
    }
});
// 6. Issue/dispute created
exports.onIssueCreate = functions.firestore
    .document('issues/{issueId}')
    .onCreate(async (snap, context) => {
    try {
        const issueData = snap.data();
        const issueId = context.params.issueId;
        const userData = await getUserData(issueData.userId);
        await createAdminNotification('user_issue', 'New Issue Reported', `${(userData === null || userData === void 0 ? void 0 : userData.username) || 'User'} reported an issue: ${issueData.title}`, {
            userId: issueData.userId,
            username: userData === null || userData === void 0 ? void 0 : userData.username,
            metadata: {
                category: issueData.category,
                priority: issueData.priority,
                type: issueData.type
            },
            actionUrl: '/admin/reports'
        });
    }
    catch (error) {
        console.error('Error in onIssueCreate notification:', error);
    }
});
// 7. Referral bonus created
exports.onReferralCreate = functions.firestore
    .document('referrals/{referralId}')
    .onCreate(async (snap, context) => {
    try {
        const referralData = snap.data();
        const userData = await getUserData(referralData.referrerId);
        await createAdminNotification('referral_bonus', 'Referral Bonus Awarded', `${(userData === null || userData === void 0 ? void 0 : userData.username) || 'User'} earned a referral bonus of $${referralData.bonusAmount}`, {
            userId: referralData.referrerId,
            username: userData === null || userData === void 0 ? void 0 : userData.username,
            amount: referralData.bonusAmount,
            metadata: {
                referredUserId: referralData.referredUserId,
                bonusType: referralData.bonusType
            },
            actionUrl: '/admin/wallet-reports'
        });
    }
    catch (error) {
        console.error('Error in onReferralCreate notification:', error);
    }
});
// 8. Wallet transaction monitoring
exports.onWalletTransactionCreate = functions.firestore
    .document('walletTransactions/{transactionId}')
    .onCreate(async (snap, context) => {
    try {
        const transactionData = snap.data();
        // Only notify for significant transactions
        if (transactionData.amount >= 10) {
            const userData = await getUserData(transactionData.userId);
            const notificationType = transactionData.type === 'credit' ? 'wallet_added' : 'wallet_used';
            const title = transactionData.type === 'credit' ? 'Wallet Credit Added' : 'Wallet Balance Used';
            const message = `${(userData === null || userData === void 0 ? void 0 : userData.username) || 'User'} ${transactionData.type === 'credit' ? 'added' : 'used'} $${transactionData.amount} ${transactionData.type === 'credit' ? 'to' : 'from'} wallet`;
            await createAdminNotification(notificationType, title, message, {
                userId: transactionData.userId,
                username: userData === null || userData === void 0 ? void 0 : userData.username,
                amount: transactionData.amount,
                metadata: {
                    transactionType: transactionData.type,
                    description: transactionData.description,
                    orderId: transactionData.orderId
                },
                actionUrl: '/admin/wallet-reports'
            });
        }
    }
    catch (error) {
        console.error('Error in onWalletTransactionCreate notification:', error);
    }
});
// 9. Shipping label creation
exports.onShippingLabelCreate = functions.firestore
    .document('shippingLabels/{labelId}')
    .onCreate(async (snap, context) => {
    try {
        const labelData = snap.data();
        const userData = await getUserData(labelData.sellerId || labelData.userId);
        await createAdminNotification('shipping_label_created', 'Shipping Label Created', `${(userData === null || userData === void 0 ? void 0 : userData.username) || 'User'} created a shipping label for order`, {
            userId: labelData.sellerId || labelData.userId,
            username: userData === null || userData === void 0 ? void 0 : userData.username,
            orderId: labelData.orderId,
            metadata: {
                carrier: labelData.carrier,
                service: labelData.service,
                cost: labelData.cost,
                trackingNumber: labelData.trackingNumber
            },
            actionUrl: `/admin/shipping?search=${labelData.orderId}`
        });
    }
    catch (error) {
        console.error('Error in onShippingLabelCreate notification:', error);
    }
});
// 10. Secret code verification
exports.onSecretCodeVerification = functions.firestore
    .document('orders/{orderId}')
    .onUpdate(async (change, context) => {
    try {
        const before = change.before.data();
        const after = change.after.data();
        const orderId = context.params.orderId;
        // Check if secret code was just verified (status changed to delivered)
        if (before.status !== 'delivered' && after.status === 'delivered') {
            const buyerData = await getUserData(after.buyerId);
            await createAdminNotification('secret_code_verified', 'Delivery Confirmed', `${(buyerData === null || buyerData === void 0 ? void 0 : buyerData.username) || 'User'} confirmed delivery with secret code`, {
                userId: after.buyerId,
                username: buyerData === null || buyerData === void 0 ? void 0 : buyerData.username,
                orderId,
                amount: after.totalAmount,
                metadata: {
                    sellerId: after.sellerId,
                    deliveredAt: after.deliveredAt,
                    secretCodeUsed: true
                },
                actionUrl: `/admin/transactions?search=${orderId}`
            });
        }
    }
    catch (error) {
        console.error('Error in onSecretCodeVerification notification:', error);
    }
});
// 11. Escrow release
exports.onEscrowRelease = functions.firestore
    .document('orders/{orderId}')
    .onUpdate(async (change, context) => {
    try {
        const before = change.before.data();
        const after = change.after.data();
        const orderId = context.params.orderId;
        // Check if funds were released (escrow status changed)
        if (before.escrowStatus !== 'released' && after.escrowStatus === 'released') {
            const sellerData = await getUserData(after.sellerId);
            await createAdminNotification('escrow_released', 'Escrow Funds Released', `Funds released to ${(sellerData === null || sellerData === void 0 ? void 0 : sellerData.username) || 'seller'} for order $${after.totalAmount}`, {
                userId: after.sellerId,
                username: sellerData === null || sellerData === void 0 ? void 0 : sellerData.username,
                orderId,
                amount: after.totalAmount,
                metadata: {
                    buyerId: after.buyerId,
                    releasedAt: after.escrowReleasedAt,
                    releaseMethod: after.releaseMethod || 'auto'
                },
                actionUrl: `/admin/transactions?search=${orderId}`
            });
        }
    }
    catch (error) {
        console.error('Error in onEscrowRelease notification:', error);
    }
});

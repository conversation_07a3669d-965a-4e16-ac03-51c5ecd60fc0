<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hive Campus Escrow System Monitoring</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .dashboard {
            max-width: 1200px;
            margin: 0 auto;
        }
        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        .metric-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .metric-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        .metric-value {
            font-size: 32px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        .metric-change {
            font-size: 14px;
            color: #28a745;
        }
        .metric-change.negative {
            color: #dc3545;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-green { background: #28a745; }
        .status-yellow { background: #ffc107; }
        .status-red { background: #dc3545; }
        .function-list {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .function-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #eee;
        }
        .function-item:last-child {
            border-bottom: none;
        }
        .test-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-top: 20px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-results {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .commission-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .commission-table th,
        .commission-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        .commission-table th {
            background: #f8f9fa;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="dashboard">
        <div class="header">
            <h1>🏛️ Hive Campus Escrow System Monitoring</h1>
            <p>Real-time monitoring of commission-based escrow system with secret code release and Shippo integration</p>
            <p><strong>Deployment Status:</strong> 
                <span class="status-indicator status-green"></span>
                Live & Operational
            </p>
        </div>

        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-title">Commission Revenue (Today)</div>
                <div class="metric-value">$0.00</div>
                <div class="metric-change">+0% from yesterday</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-title">Orders in Escrow</div>
                <div class="metric-value">0</div>
                <div class="metric-change">Active orders awaiting release</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-title">Secret Code Redemption Rate</div>
                <div class="metric-value">0%</div>
                <div class="metric-change">No data yet</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-title">Auto-Release Rate</div>
                <div class="metric-value">0%</div>
                <div class="metric-change">3-day auto-release</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-title">Shipping Labels Generated</div>
                <div class="metric-value">0</div>
                <div class="metric-change">Via Shippo API</div>
            </div>
            
            <div class="metric-card">
                <div class="metric-title">Return Requests</div>
                <div class="metric-value">0</div>
                <div class="metric-change">Within 3-day window</div>
            </div>
        </div>

        <div class="function-list">
            <h3>📋 Deployed Functions Status</h3>
            <div class="function-item">
                <span><span class="status-indicator status-green"></span>releaseEscrowWithCode</span>
                <span>Secret code fund release</span>
            </div>
            <div class="function-item">
                <span><span class="status-indicator status-green"></span>autoReleaseEscrow</span>
                <span>Scheduled auto-release (every hour)</span>
            </div>
            <div class="function-item">
                <span><span class="status-indicator status-green"></span>markDeliveryCompleted</span>
                <span>In-person delivery confirmation</span>
            </div>
            <div class="function-item">
                <span><span class="status-indicator status-green"></span>requestReturn</span>
                <span>Buyer return requests</span>
            </div>
            <div class="function-item">
                <span><span class="status-indicator status-green"></span>createCheckoutSession</span>
                <span>Updated with new commission structure</span>
            </div>
        </div>

        <div class="test-section">
            <h3>🧪 Commission Structure Testing</h3>
            <p>Test the new commission calculation logic:</p>
            
            <table class="commission-table">
                <thead>
                    <tr>
                        <th>Price Range</th>
                        <th>Textbooks/Course Materials</th>
                        <th>Other Categories</th>
                        <th>Example</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>$1 - $5</td>
                        <td>Flat $0.50</td>
                        <td>Flat $0.50</td>
                        <td>$3 textbook → $0.50 fee, $2.50 to seller</td>
                    </tr>
                    <tr>
                        <td>$5 - $10</td>
                        <td>8%</td>
                        <td>10%</td>
                        <td>$8 textbook → $0.64 fee, $7.36 to seller</td>
                    </tr>
                    <tr>
                        <td>$10+</td>
                        <td>8%</td>
                        <td>10%</td>
                        <td>$20 textbook → $1.60 fee, $18.40 to seller</td>
                    </tr>
                </tbody>
            </table>

            <button class="test-button" onclick="testCommissionCalculation()">Test Commission Logic</button>
            <button class="test-button" onclick="testSecretCodeGeneration()">Test Secret Codes</button>
            <button class="test-button" onclick="testWalletScenarios()">Test Wallet Integration</button>
            
            <div id="testResults" class="test-results" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🔧 System Configuration</h3>
            <p><strong>Shippo API:</strong> <span class="status-indicator status-green"></span> Live API Key Configured</p>
            <p><strong>Stripe:</strong> <span class="status-indicator status-green"></span> Live Mode Active</p>
            <p><strong>Auto-Release:</strong> <span class="status-indicator status-green"></span> 3 Days After Delivery</p>
            <p><strong>Return Window:</strong> <span class="status-indicator status-green"></span> 3 Days After Delivery</p>
            <p><strong>Secret Codes:</strong> <span class="status-indicator status-green"></span> 6-Digit Random Generation</p>
        </div>
    </div>

    <script>
        function testCommissionCalculation() {
            const results = document.getElementById('testResults');
            results.style.display = 'block';
            
            const testCases = [
                { price: 3, category: 'textbooks', expected: { fee: 0.50, seller: 2.50 } },
                { price: 8, category: 'textbooks', expected: { fee: 0.64, seller: 7.36 } },
                { price: 20, category: 'textbooks', expected: { fee: 1.60, seller: 18.40 } },
                { price: 10, category: 'electronics', expected: { fee: 1.00, seller: 9.00 } },
                { price: 50, category: 'electronics', expected: { fee: 5.00, seller: 45.00 } }
            ];
            
            let output = 'Commission Calculation Test Results:\n';
            output += '=====================================\n\n';
            
            testCases.forEach((test, index) => {
                const isTextbook = ['textbooks', 'course-materials', 'books'].includes(test.category);
                let platformFee;
                
                if (test.price <= 5) {
                    platformFee = 0.50;
                } else if (test.price <= 10) {
                    platformFee = test.price * (isTextbook ? 0.08 : 0.10);
                } else {
                    platformFee = test.price * (isTextbook ? 0.08 : 0.10);
                }
                
                const sellerAmount = test.price - platformFee;
                const pass = Math.abs(platformFee - test.expected.fee) < 0.01 && 
                           Math.abs(sellerAmount - test.expected.seller) < 0.01;
                
                output += `Test ${index + 1}: $${test.price} ${test.category}\n`;
                output += `  Expected: Fee=$${test.expected.fee}, Seller=$${test.expected.seller}\n`;
                output += `  Actual:   Fee=$${platformFee.toFixed(2)}, Seller=$${sellerAmount.toFixed(2)}\n`;
                output += `  Result:   ${pass ? '✅ PASS' : '❌ FAIL'}\n\n`;
            });
            
            results.textContent = output;
        }
        
        function testSecretCodeGeneration() {
            const results = document.getElementById('testResults');
            results.style.display = 'block';
            
            let output = 'Secret Code Generation Test:\n';
            output += '============================\n\n';
            
            const codes = [];
            for (let i = 0; i < 10; i++) {
                const code = Math.floor(100000 + Math.random() * 900000).toString();
                codes.push(code);
                output += `Code ${i + 1}: ${code} (length: ${code.length})\n`;
            }
            
            const unique = new Set(codes);
            output += `\nUniqueness: ${unique.size}/${codes.length} unique ${unique.size === codes.length ? '✅' : '❌'}\n`;
            output += `Format: All 6-digit numbers ${codes.every(c => /^\d{6}$/.test(c)) ? '✅' : '❌'}\n`;
            
            results.textContent = output;
        }
        
        function testWalletScenarios() {
            const results = document.getElementById('testResults');
            results.style.display = 'block';
            
            const scenarios = [
                { name: '$3 Textbook with $2 Wallet', price: 3, category: 'textbooks', wallet: 2 },
                { name: '$20 Electronics with $5 Wallet', price: 20, category: 'electronics', wallet: 5 },
                { name: '$8 Course Materials with $10 Wallet', price: 8, category: 'course-materials', wallet: 10 }
            ];
            
            let output = 'Wallet Integration Test:\n';
            output += '========================\n\n';
            
            scenarios.forEach((scenario, index) => {
                const isTextbook = ['textbooks', 'course-materials', 'books'].includes(scenario.category);
                let platformFee;
                
                if (scenario.price <= 5) {
                    platformFee = 0.50;
                } else {
                    platformFee = scenario.price * (isTextbook ? 0.08 : 0.10);
                }
                
                const sellerReceives = scenario.price - platformFee;
                const walletUsed = Math.min(scenario.wallet, scenario.price);
                const stripeCharge = Math.max(0, scenario.price - walletUsed);
                
                output += `Scenario ${index + 1}: ${scenario.name}\n`;
                output += `  Item Price: $${scenario.price}\n`;
                output += `  Platform Fee: $${platformFee.toFixed(2)}\n`;
                output += `  Seller Receives: $${sellerReceives.toFixed(2)}\n`;
                output += `  Wallet Used: $${walletUsed.toFixed(2)}\n`;
                output += `  Stripe Charge: $${stripeCharge.toFixed(2)}\n\n`;
            });
            
            results.textContent = output;
        }
        
        // Auto-refresh metrics every 30 seconds
        setInterval(() => {
            console.log('Refreshing metrics...');
            // In a real implementation, this would fetch data from Firebase
        }, 30000);
    </script>
</body>
</html>

# Wallet System Deployment Checklist

## Pre-Deployment Verification

### ✅ Firebase Functions
- [ ] Deploy wallet functions: `firebase deploy --only functions`
- [ ] Verify functions are deployed:
  - [ ] `initializeWallet`
  - [ ] `processReferralCode`
  - [ ] `grantWalletCredit`
  - [ ] `getWalletData`
  - [ ] `validateReferralCode`

### ✅ Firestore Security Rules
- [ ] Deploy updated security rules: `firebase deploy --only firestore:rules`
- [ ] Verify wallet collection security:
  - [ ] Users can only read their own wallet
  - [ ] Only admins can write to wallets
  - [ ] Referral codes are readable by authenticated users

### ✅ Frontend Components
- [ ] Signup page includes referral code field
- [ ] Checkout flow shows wallet balance and credit option
- [ ] Admin panel has wallet management features
- [ ] All wallet-related UI components are responsive

### ✅ Environment Configuration
- [ ] Stripe keys are properly configured
- [ ] Firebase project settings are correct
- [ ] All environment variables are set:
  ```
  SIGNUP_BONUS_AMOUNT=5.00
  REFERRAL_BONUS_AMOUNT=5.00
  STRIPE_MINIMUM_CHARGE=0.50
  ```

## Testing Checklist

### 🧪 Unit Tests
- [ ] Run wallet system test suite
- [ ] Verify all security rules pass
- [ ] Test edge cases and error handling

### 🧪 Integration Tests
- [ ] Test complete signup flow with referral
- [ ] Test checkout with wallet credit
- [ ] Test admin credit granting
- [ ] Test Stripe webhook processing

### 🧪 Manual Testing Scenarios

#### Signup Flow
- [ ] Create account without referral code → receives $5 signup bonus
- [ ] Create account with valid referral code → both users get $5
- [ ] Try invalid referral code → shows error message
- [ ] Try using own referral code → shows error message

#### Checkout Flow
- [ ] Purchase with sufficient wallet credit → credit applied correctly
- [ ] Purchase with partial wallet credit → remaining charged to Stripe
- [ ] Purchase without using wallet → full amount to Stripe
- [ ] Failed payment → wallet balance unchanged

#### Admin Functions
- [ ] Admin can view user wallets
- [ ] Admin can grant credits
- [ ] Admin actions are logged
- [ ] Non-admin cannot access wallet management

#### Edge Cases
- [ ] Concurrent wallet operations
- [ ] Network failures during transactions
- [ ] Invalid amounts and user IDs
- [ ] Wallet operations with deleted users

## Production Deployment Steps

### 1. Database Migration
```bash
# No migration needed for new collections
# Existing users will get wallets created on first access
```

### 2. Deploy Functions
```bash
cd functions
npm install
npm run build
firebase deploy --only functions
```

### 3. Deploy Security Rules
```bash
firebase deploy --only firestore:rules
```

### 4. Deploy Frontend
```bash
npm run build
firebase deploy --only hosting
```

### 5. Verify Deployment
- [ ] Check Firebase Console for deployed functions
- [ ] Test function endpoints
- [ ] Verify security rules in Firestore
- [ ] Test frontend functionality

## Post-Deployment Monitoring

### 📊 Metrics to Monitor
- [ ] Wallet creation rate
- [ ] Referral code usage
- [ ] Credit redemption rates
- [ ] Failed wallet operations
- [ ] Admin credit grants

### 🚨 Alerts to Set Up
- [ ] Failed wallet function executions
- [ ] Unusual wallet balance changes
- [ ] High error rates in wallet operations
- [ ] Stripe webhook failures

### 📝 Logging
- [ ] Wallet transaction logs
- [ ] Admin action logs
- [ ] Error logs for debugging
- [ ] Performance metrics

## Rollback Plan

### If Issues Arise
1. **Disable wallet features in frontend**
   - Comment out wallet UI components
   - Disable wallet credit in checkout

2. **Revert functions if needed**
   ```bash
   firebase functions:delete initializeWallet
   firebase functions:delete processReferralCode
   firebase functions:delete grantWalletCredit
   firebase functions:delete getWalletData
   firebase functions:delete validateReferralCode
   ```

3. **Revert security rules**
   ```bash
   # Deploy previous version of firestore.rules
   firebase deploy --only firestore:rules
   ```

## Success Criteria

### ✅ System is Ready When:
- [ ] All tests pass
- [ ] Functions deploy successfully
- [ ] Security rules are active
- [ ] Frontend components work correctly
- [ ] Admin panel functions properly
- [ ] Stripe integration works
- [ ] Monitoring is in place

### 📈 Success Metrics (Week 1)
- [ ] >90% of new users receive signup bonus
- [ ] >80% referral code success rate
- [ ] <1% wallet operation failures
- [ ] Zero security incidents
- [ ] Positive user feedback on wallet features

## Support Documentation

### 🆘 Troubleshooting Guide
- **Wallet not initialized**: Check `initializeWallet` function logs
- **Referral not working**: Verify referral code in Firestore
- **Credit not applied**: Check Stripe webhook processing
- **Admin grants failing**: Verify admin permissions

### 📞 Emergency Contacts
- Development Team: [team-email]
- Firebase Support: [support-link]
- Stripe Support: [stripe-support]

### 📚 Documentation Links
- [Wallet System Documentation](./WALLET_SYSTEM.md)
- [Firebase Functions Logs](https://console.firebase.google.com)
- [Stripe Dashboard](https://dashboard.stripe.com)
- [Admin Panel Guide](./admin-guide.md)

---

**Deployment Lead**: _________________ **Date**: _________

**QA Approval**: _________________ **Date**: _________

**Final Approval**: _________________ **Date**: _________

.hive-loader-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: "Inter", sans-serif;
  font-weight: 300;
  color: white;
  border-radius: 50%;
  background-color: transparent;
  user-select: none;
}

.hive-loader {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  aspect-ratio: 1 / 1;
  border-radius: 50%;
  background-color: transparent;
  animation: hive-loader-rotate 2s linear infinite;
  z-index: 0;
}

@keyframes hive-loader-rotate {
  0% {
    transform: rotate(90deg);
    box-shadow:
      0 10px 20px 0 #fff inset,
      0 20px 30px 0 #ad5fff inset,
      0 60px 60px 0 #471eec inset;
  }
  50% {
    transform: rotate(270deg);
    box-shadow:
      0 10px 20px 0 #fff inset,
      0 20px 10px 0 #d60a47 inset,
      0 40px 60px 0 #311e80 inset;
  }
  100% {
    transform: rotate(450deg);
    box-shadow:
      0 10px 20px 0 #fff inset,
      0 20px 30px 0 #ad5fff inset,
      0 60px 60px 0 #471eec inset;
  }
}

.hive-loader-text {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  letter-spacing: 0.1em;
}

.hive-loader-letter {
  display: inline-block;
  opacity: 0.4;
  transform: translateY(0);
  animation: hive-loader-letter-anim 2s infinite;
  border-radius: 50ch;
  border: none;
}

.hive-loader-letter:nth-child(1) {
  animation-delay: 0s;
}
.hive-loader-letter:nth-child(2) {
  animation-delay: 0.1s;
}
.hive-loader-letter:nth-child(3) {
  animation-delay: 0.2s;
}
.hive-loader-letter:nth-child(4) {
  animation-delay: 0.3s;
}
.hive-loader-letter:nth-child(5) {
  animation-delay: 0.4s;
}
.hive-loader-letter:nth-child(6) {
  animation-delay: 0.5s;
}
.hive-loader-letter:nth-child(7) {
  animation-delay: 0.6s;
}
.hive-loader-letter:nth-child(8) {
  animation-delay: 0.7s;
}
.hive-loader-letter:nth-child(9) {
  animation-delay: 0.8s;
}
.hive-loader-letter:nth-child(10) {
  animation-delay: 0.9s;
}
.hive-loader-letter:nth-child(11) {
  animation-delay: 1.0s;
}

@keyframes hive-loader-letter-anim {
  0%,
  100% {
    opacity: 0.4;
    transform: translateY(0);
  }
  20% {
    opacity: 1;
    transform: scale(1.15);
  }
  40% {
    opacity: 0.7;
    transform: translateY(0);
  }
}

/* Dark mode support */
.dark .hive-loader-wrapper {
  color: #e5e7eb;
}

.dark .hive-loader {
  box-shadow:
    0 10px 20px 0 #e5e7eb inset,
    0 20px 30px 0 #ad5fff inset,
    0 60px 60px 0 #471eec inset;
}

/* Size variants */
.hive-loader-wrapper.w-24 {
  width: 6rem;
  height: 6rem;
  font-size: 0.75rem;
}

.hive-loader-wrapper.w-36 {
  width: 9rem;
  height: 9rem;
  font-size: 0.875rem;
}

.hive-loader-wrapper.w-44 {
  width: 11rem;
  height: 11rem;
  font-size: 1rem;
}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stripe Integration Test - Hive Campus</title>
    <script src="https://js.stripe.com/v3/"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 3px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎓 Hive Campus - Stripe Integration Test</h1>
        <p>This page tests the Stripe webhook and payment integration without Firebase dependencies.</p>

        <!-- Webhook Test -->
        <div class="test-section">
            <h3>🔗 Webhook Endpoint Test</h3>
            <p><strong>URL:</strong> https://us-central1-h1c1-798a8.cloudfunctions.net/stripeWebhook</p>
            <p><strong>Secret:</strong> whsec_Ggd0wEt8z8NzRV5kyEyvXH2iB1xrWSZq</p>
            <button onclick="testWebhookEndpoint()">Test Webhook</button>
            <div id="webhook-status"></div>
        </div>

        <!-- Function Tests -->
        <div class="test-section">
            <h3>⚡ Firebase Functions Test</h3>
            <button onclick="testCreateCheckoutFunction()">Test createCheckoutSession</button>
            <button onclick="testTestFunction()">Test testFunction</button>
            <div id="functions-status"></div>
        </div>

        <!-- Stripe Test -->
        <div class="test-section">
            <h3>💳 Stripe Configuration Test</h3>
            <p><strong>Publishable Key:</strong> pk_live_51Rhyb4Fs3MvmXUcFWvWmpJvZ8SoI6c7DTtvL47didZUyvTz4UGqlmtxXDaZk32jXgpMYjGaY5lXPoZ7UStDH3GXc00xZyM8fUw</p>
            <button onclick="testStripeConfig()">Test Stripe Configuration</button>
            <div id="stripe-status"></div>
        </div>

        <!-- Manual Payment Test -->
        <div class="test-section">
            <h3>🧪 Manual Payment Test</h3>
            <p>For testing actual payments, you'll need to:</p>
            <ol>
                <li>Use your main Hive Campus application</li>
                <li>Sign in with a valid account</li>
                <li>Create or find a listing to purchase</li>
                <li>Use the "Buy Now" button</li>
                <li>Complete payment with test card: <code>4242 4242 4242 4242</code></li>
            </ol>
            <button onclick="openMainApp()">Open Main App</button>
            <div id="manual-status"></div>
        </div>

        <!-- Test Results -->
        <div class="test-section">
            <h3>📊 Test Results</h3>
            <div id="results-summary"></div>
        </div>

        <!-- Logs -->
        <div class="test-section">
            <h3>📋 Test Logs</h3>
            <button onclick="clearLogs()">Clear Logs</button>
            <div id="logs" class="log"></div>
        </div>
    </div>

    <script>
        // Test results tracking
        const testResults = {
            webhook: null,
            createCheckout: null,
            testFunction: null,
            stripe: null
        };

        // Logging function
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('logs');
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(logEntry);
        }

        function clearLogs() {
            document.getElementById('logs').textContent = '';
        }

        function updateStatus(elementId, message, isSuccess) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${isSuccess ? 'success' : 'error'}">${message}</div>`;
        }

        function updateResults() {
            const resultsElement = document.getElementById('results-summary');
            const total = Object.keys(testResults).length;
            const passed = Object.values(testResults).filter(result => result === true).length;
            const failed = Object.values(testResults).filter(result => result === false).length;
            const pending = total - passed - failed;

            resultsElement.innerHTML = `
                <div class="status info">
                    <strong>Test Summary:</strong><br>
                    ✅ Passed: ${passed}<br>
                    ❌ Failed: ${failed}<br>
                    ⏳ Pending: ${pending}<br>
                    📊 Total: ${total}
                </div>
            `;
        }

        // Test webhook endpoint
        async function testWebhookEndpoint() {
            try {
                log('Testing webhook endpoint...');
                
                const response = await fetch('https://us-central1-h1c1-798a8.cloudfunctions.net/stripeWebhook', {
                    method: 'GET'
                });
                
                log(`Webhook response: ${response.status} ${response.statusText}`);
                
                if (response.status === 405) {
                    log('✅ Webhook endpoint working correctly (405 Method Not Allowed for GET)');
                    updateStatus('webhook-status', '✅ Webhook endpoint is working correctly', true);
                    testResults.webhook = true;
                } else {
                    log('⚠️ Unexpected webhook response');
                    updateStatus('webhook-status', '⚠️ Unexpected response from webhook', false);
                    testResults.webhook = false;
                }
                
            } catch (error) {
                log(`❌ Webhook test failed: ${error.message}`, 'error');
                updateStatus('webhook-status', `❌ Webhook test failed: ${error.message}`, false);
                testResults.webhook = false;
            }
            updateResults();
        }

        // Test createCheckoutSession function
        async function testCreateCheckoutFunction() {
            try {
                log('Testing createCheckoutSession function...');
                
                const response = await fetch('https://us-central1-h1c1-798a8.cloudfunctions.net/createCheckoutSession', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        data: {
                            listingId: 'test-listing-123',
                            quantity: 1
                        }
                    })
                });
                
                log(`createCheckoutSession response: ${response.status}`);
                
                if (response.status === 401 || response.status === 500) {
                    log('✅ Function correctly requires authentication');
                    updateStatus('functions-status', '✅ createCheckoutSession requires authentication (correct)', true);
                    testResults.createCheckout = true;
                } else {
                    log('⚠️ Unexpected function response');
                    updateStatus('functions-status', '⚠️ Unexpected response from function', false);
                    testResults.createCheckout = false;
                }
                
            } catch (error) {
                log(`❌ Function test failed: ${error.message}`, 'error');
                updateStatus('functions-status', `❌ Function test failed: ${error.message}`, false);
                testResults.createCheckout = false;
            }
            updateResults();
        }

        // Test testFunction
        async function testTestFunction() {
            try {
                log('Testing testFunction...');
                
                const response = await fetch('https://us-central1-h1c1-798a8.cloudfunctions.net/testFunction', {
                    method: 'GET'
                });
                
                const responseText = await response.text();
                log(`testFunction response: ${response.status} - ${responseText}`);
                
                if (response.status === 200) {
                    log('✅ testFunction working correctly');
                    updateStatus('functions-status', '✅ testFunction is working correctly', true);
                    testResults.testFunction = true;
                } else {
                    log('❌ testFunction failed');
                    updateStatus('functions-status', '❌ testFunction failed', false);
                    testResults.testFunction = false;
                }
                
            } catch (error) {
                log(`❌ testFunction failed: ${error.message}`, 'error');
                updateStatus('functions-status', `❌ testFunction failed: ${error.message}`, false);
                testResults.testFunction = false;
            }
            updateResults();
        }

        // Test Stripe configuration
        async function testStripeConfig() {
            try {
                log('Testing Stripe configuration...');
                
                const stripe = Stripe('pk_live_51Rhyb4Fs3MvmXUcFWvWmpJvZ8SoI6c7DTtvL47didZUyvTz4UGqlmtxXDaZk32jXgpMYjGaY5lXPoZ7UStDH3GXc00xZyM8fUw');
                
                if (stripe) {
                    log('✅ Stripe SDK loaded successfully');
                    updateStatus('stripe-status', '✅ Stripe configuration is valid', true);
                    testResults.stripe = true;
                } else {
                    log('❌ Stripe SDK failed to load');
                    updateStatus('stripe-status', '❌ Stripe configuration failed', false);
                    testResults.stripe = false;
                }
                
            } catch (error) {
                log(`❌ Stripe test failed: ${error.message}`, 'error');
                updateStatus('stripe-status', `❌ Stripe test failed: ${error.message}`, false);
                testResults.stripe = false;
            }
            updateResults();
        }

        // Open main app
        function openMainApp() {
            log('Opening main Hive Campus application...');
            window.open('https://h1c1-798a8.web.app', '_blank');
            updateStatus('manual-status', '🚀 Main app opened in new tab', true);
        }

        // Initialize page
        window.onload = function() {
            log('🚀 Stripe integration test page loaded');
            log('📋 Available tests:');
            log('1. Webhook endpoint test');
            log('2. Firebase Functions test');
            log('3. Stripe configuration test');
            log('4. Manual payment test in main app');
            updateResults();
        };
    </script>
</body>
</html>

/**
 * Feature flags for the application
 * 
 * This file controls which features are enabled or disabled.
 * Useful for gradual rollouts and preventing errors during development.
 */

export const FEATURE_FLAGS = {
  // Admin Notification System
  // Set to true after deploying Firebase Functions and Firestore Rules
  ADMIN_NOTIFICATIONS: true,

  // Other feature flags can be added here
  // ADVANCED_ANALYTICS: true,
  // BETA_FEATURES: false,
} as const;

/**
 * Check if a feature is enabled
 */
export const isFeatureEnabled = (feature: keyof typeof FEATURE_FLAGS): boolean => {
  return FEATURE_FLAGS[feature];
};

/**
 * Environment-based feature overrides
 * Features can be enabled/disabled based on environment
 */
export const getFeatureStatus = (feature: keyof typeof FEATURE_FLAGS): boolean => {
  // In development, you might want to enable certain features for testing
  if (import.meta.env.DEV) {
    // Override specific features in development if needed
    // return true; // Uncomment to enable all features in dev
  }
  
  return FEATURE_FLAGS[feature];
};

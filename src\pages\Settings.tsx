import React, { useState } from 'react';
import { 
  Bell, 
  Moon, 
  Sun, 
  Lock, 
  User, 
  Mail, 
  Shield, 
  HelpCircle, 
  LogOut,
  ChevronRight,
  Wallet,
  CreditCard
} from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface SettingsProps {
  toggleDarkMode: () => void;
  isDarkMode: boolean;
}

const Settings: React.FC<SettingsProps> = ({ toggleDarkMode, isDarkMode }) => {
  const navigate = useNavigate();
  const [notifications, setNotifications] = useState({
    messages: true,
    priceDrops: true,
    newListings: false,
    marketing: false
  });

  const handleNotificationChange = (key: keyof typeof notifications) => {
    setNotifications(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const settingSections = [
    {
      title: 'Account',
      items: [
        {
          icon: User,
          label: 'Edit Profile',
          description: 'Update your personal information',
          action: () => console.log('Edit profile')
        },
        {
          icon: Mail,
          label: 'Email Settings',
          description: 'Manage your email preferences',
          action: () => console.log('Email settings')
        },
        {
          icon: Lock,
          label: 'Change Password',
          description: 'Update your password',
          action: () => console.log('Change password')
        }
      ]
    },
    {
      title: 'Payments & Wallet',
      items: [
        {
          icon: Wallet,
          label: 'Wallet',
          description: 'Manage your Hive Campus wallet',
          action: () => navigate('/wallet')
        },
        {
          icon: CreditCard,
          label: 'Payment Settings',
          description: 'Set up payment methods and selling preferences',
          action: () => navigate('/settings/payment')
        }
      ]
    },
    {
      title: 'Privacy & Security',
      items: [
        {
          icon: Shield,
          label: 'Privacy Settings',
          description: 'Control who can see your information',
          action: () => console.log('Privacy settings')
        },
        {
          icon: Lock,
          label: 'Two-Factor Authentication',
          description: 'Add an extra layer of security',
          action: () => console.log('2FA settings')
        }
      ]
    },
    {
      title: 'Support',
      items: [
        {
          icon: HelpCircle,
          label: 'Help Center',
          description: 'Get help and support',
          action: () => console.log('Help center')
        }
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 md:ml-64 overflow-x-hidden">
      <div className="max-w-4xl mx-auto px-2 sm:px-4 lg:px-6 xl:px-8 py-4 sm:py-6 lg:py-8 w-full">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">Settings</h1>
          <p className="text-gray-600 dark:text-gray-400">Manage your account and preferences</p>
        </div>

        <div className="space-y-6">
          {/* Appearance */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">Appearance</h2>
            <div className="flex items-center justify-between py-2">
              <div className="flex items-center space-x-3 flex-1 min-w-0">
                <div className="p-2 bg-gray-100 dark:bg-gray-700 rounded-lg flex-shrink-0">
                  {isDarkMode ? (
                    <Moon className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                  ) : (
                    <Sun className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                  )}
                </div>
                <div className="min-w-0 flex-1">
                  <p className="font-medium text-gray-900 dark:text-white text-sm sm:text-base">Dark Mode</p>
                  <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-400">
                    {isDarkMode ? 'Dark theme is enabled' : 'Light theme is enabled'}
                  </p>
                </div>
              </div>
              <button
                onClick={toggleDarkMode}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 flex-shrink-0 ml-3 ${
                  isDarkMode ? 'bg-primary-600' : 'bg-gray-200'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    isDarkMode ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>
          </div>

          {/* Notifications */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">Notifications</h2>
            <div className="space-y-4">
              <button
                onClick={() => navigate('/notifications')}
                className="w-full flex items-center justify-between p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                <div className="flex items-center space-x-3">
                  <Bell className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                  <div className="text-left">
                    <p className="font-medium text-gray-900 dark:text-white">
                      Notification Settings
                    </p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Manage push notifications, email alerts, and preferences
                    </p>
                  </div>
                </div>
                <ChevronRight className="w-5 h-5 text-gray-400" />
              </button>

              {/* Quick toggle for basic notifications */}
              {Object.entries(notifications).slice(0, 2).map(([key, value]) => (
                <div key={key} className="flex items-center justify-between py-2">
                  <div className="flex items-center space-x-3 flex-1 min-w-0">
                    <Bell className="w-5 h-5 text-gray-600 dark:text-gray-400 flex-shrink-0" />
                    <div className="min-w-0 flex-1">
                      <p className="font-medium text-gray-900 dark:text-white capitalize text-sm sm:text-base">
                        {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                      </p>
                      <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-400 truncate">
                        {key === 'messages' && 'Get notified when you receive new messages'}
                        {key === 'priceDrops' && 'Get notified when items in your wishlist drop in price'}
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => handleNotificationChange(key as keyof typeof notifications)}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 flex-shrink-0 ml-3 ${
                      value ? 'bg-primary-600' : 'bg-gray-200 dark:bg-gray-600'
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        value ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>
              ))}
            </div>
          </div>

          {/* Settings Sections */}
          {settingSections.map((section) => (
            <div key={section.title} className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">{section.title}</h2>
              <div className="space-y-2">
                {section.items.map((item) => (
                  <button
                    key={item.label}
                    onClick={item.action}
                    className="w-full flex items-center justify-between p-4 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                  >
                    <div className="flex items-center space-x-3">
                      <item.icon className="w-5 h-5 text-gray-600 dark:text-gray-400" />
                      <div className="text-left">
                        <p className="font-medium text-gray-900 dark:text-white">{item.label}</p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">{item.description}</p>
                      </div>
                    </div>
                    <ChevronRight className="w-5 h-5 text-gray-400" />
                  </button>
                ))}
              </div>
            </div>
          ))}

          {/* Danger Zone */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
            <h2 className="text-xl font-semibold text-red-600 dark:text-red-400 mb-6">Danger Zone</h2>
            <div className="space-y-4">
              <button 
                onClick={() => {
                  if (confirm('Are you sure you want to sign out?')) {
                    // In a real app, this would call your logout function
                    navigate('/');
                  }
                }}
                className="w-full flex items-center justify-between p-4 rounded-xl border border-red-200 dark:border-red-800 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
              >
                <div className="flex items-center space-x-3">
                  <LogOut className="w-5 h-5 text-red-600 dark:text-red-400" />
                  <div className="text-left">
                    <p className="font-medium text-red-600 dark:text-red-400">Sign Out</p>
                    <p className="text-sm text-red-500 dark:text-red-500">Sign out of your account</p>
                  </div>
                </div>
                <ChevronRight className="w-5 h-5 text-red-400" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Settings;
import React, { useState } from 'react';
import { 
  Store, 
  Search, 
  Filter, 
  MapPin, 
  Star, 
  Users, 
  MessageCircle,
  ChevronDown,
  Shield
} from 'lucide-react';
import { Link } from 'react-router-dom';


const MerchantDirectory: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedLocation, setSelectedLocation] = useState('all');
  const [sortBy, setSortBy] = useState('rating');
  const [showFilters, setShowFilters] = useState(false);

  // Real merchant data will be fetched from Firebase
  const merchants: any[] = [];



  const categories = [
    'all', 'Electronics', 'Textbooks', 'Lifestyle', 'Food & Beverage', 
    'Furniture', 'Software', 'Sports', 'Health', 'Services'
  ];

  const locations = [
    'all', 'California', 'Massachusetts', 'New York', 'Texas', 
    'Washington', 'Florida', 'Illinois', 'Pennsylvania'
  ];

  const sortOptions = [
    { id: 'rating', name: 'Highest Rated' },
    { id: 'sales', name: 'Most Sales' },
    { id: 'products', name: 'Most Products' },
    { id: 'newest', name: 'Newest First' },
    { id: 'alphabetical', name: 'Alphabetical' }
  ];

  // Filter and sort merchants
  const filteredMerchants = merchants
    .filter(merchant => {
      const matchesSearch = merchant.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           merchant.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           merchant.specialties.some(specialty => 
                             specialty.toLowerCase().includes(searchQuery.toLowerCase())
                           );
      const matchesCategory = selectedCategory === 'all' || merchant.category === selectedCategory;
      const matchesLocation = selectedLocation === 'all' || merchant.location === selectedLocation;
      return matchesSearch && matchesCategory && matchesLocation;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'rating':
          return b.rating - a.rating;
        case 'sales':
          return b.totalSales - a.totalSales;
        case 'products':
          return b.totalProducts - a.totalProducts;
        case 'newest':
          return new Date(b.joinedDate).getTime() - new Date(a.joinedDate).getTime();
        case 'alphabetical':
          return a.name.localeCompare(b.name);
        default:
          return b.rating - a.rating;
      }
    });

  const handleChatMerchant = (merchantId: number) => {
    // Navigate to messages with the merchant
    console.log('Chat with merchant:', merchantId);
  };

  return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center space-x-3 mb-4">
              <div className="w-12 h-12 bg-gradient-to-r from-accent-500 to-orange-500 rounded-2xl flex items-center justify-center">
                <Store className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Merchant Directory</h1>
                <p className="text-gray-600 dark:text-gray-400">Connect and collaborate with fellow merchant partners</p>
              </div>
            </div>
          </div>

          {/* Search and Filters */}
          <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6 mb-8">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
              <div className="flex-1 max-w-md">
                <div className="relative">
                  <Search className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                  <input
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Search merchants..."
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-accent-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  />
                </div>
              </div>
              
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className="flex items-center space-x-2 px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  <Filter className="w-4 h-4" />
                  <span>Filters</span>
                  <ChevronDown className={`w-4 h-4 transition-transform ${showFilters ? 'rotate-180' : ''}`} />
                </button>
              </div>
            </div>

            {showFilters && (
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 pt-6 border-t border-gray-200 dark:border-gray-700">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Category
                  </label>
                  <select
                    value={selectedCategory}
                    onChange={(e) => setSelectedCategory(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  >
                    {categories.map((category) => (
                      <option key={category} value={category}>
                        {category === 'all' ? 'All Categories' : category}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Location
                  </label>
                  <select
                    value={selectedLocation}
                    onChange={(e) => setSelectedLocation(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  >
                    {locations.map((location) => (
                      <option key={location} value={location}>
                        {location === 'all' ? 'All Locations' : location}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Sort By
                  </label>
                  <select
                    value={sortBy}
                    onChange={(e) => setSortBy(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-accent-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                  >
                    {sortOptions.map((option) => (
                      <option key={option.id} value={option.id}>
                        {option.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>
            )}
          </div>

          {/* Results Count */}
          <div className="mb-6">
            <p className="text-gray-600 dark:text-gray-400">
              Showing {filteredMerchants.length} of {merchants.length} merchants
            </p>
          </div>

          {/* Merchants Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {filteredMerchants.map((merchant) => (
              <div key={merchant.id} className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden">
                {/* Cover Image */}
                <div className="h-32 bg-gradient-to-r from-accent-500 to-orange-500 relative">
                  <img
                    src={merchant.coverImage}
                    alt={merchant.name}
                    className="w-full h-full object-cover opacity-30"
                  />
                  <div className="absolute top-4 right-4 flex space-x-2">
                    {merchant.verified && (
                      <div className="bg-white/90 backdrop-blur-sm rounded-full p-1">
                        <Shield className="w-4 h-4 text-accent-500" />
                      </div>
                    )}
                    <span className="bg-white/90 backdrop-blur-sm text-gray-700 px-3 py-1 rounded-full text-sm font-medium">
                      {merchant.category}
                    </span>
                  </div>
                </div>

                {/* Merchant Info */}
                <div className="p-6">
                  <div className="flex items-start space-x-4 mb-4">
                    <img
                      src={merchant.avatar}
                      alt={merchant.name}
                      className="w-16 h-16 rounded-xl object-cover border-4 border-white dark:border-gray-800 -mt-8 relative z-10"
                    />
                    <div className="flex-1 pt-2">
                      <div className="flex items-center justify-between mb-2">
                        <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                          {merchant.name}
                        </h3>
                        <div className="flex items-center space-x-1">
                          <Star className="w-4 h-4 text-yellow-400 fill-current" />
                          <span className="font-semibold text-gray-900 dark:text-white">{merchant.rating}</span>
                          <span className="text-sm text-gray-500 dark:text-gray-400">({merchant.reviewCount})</span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 mb-2">
                        <MapPin className="w-4 h-4" />
                        <span>{merchant.city}, {merchant.location}</span>
                      </div>
                      <p className="text-gray-700 dark:text-gray-300 text-sm line-clamp-2">
                        {merchant.description}
                      </p>
                    </div>
                  </div>

                  {/* Badges */}
                  <div className="flex flex-wrap gap-2 mb-4">
                    {merchant.badges.map((badge, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 bg-accent-100 dark:bg-accent-900/20 text-accent-700 dark:text-accent-400 rounded-full text-xs font-medium"
                      >
                        {badge}
                      </span>
                    ))}
                  </div>

                  {/* Specialties */}
                  <div className="mb-4">
                    <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-2">Specialties</h4>
                    <div className="flex flex-wrap gap-2">
                      {merchant.specialties.slice(0, 4).map((specialty, index) => (
                        <span
                          key={index}
                          className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded text-xs"
                        >
                          {specialty}
                        </span>
                      ))}
                      {merchant.specialties.length > 4 && (
                        <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded text-xs">
                          +{merchant.specialties.length - 4} more
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Stats */}
                  <div className="grid grid-cols-3 gap-4 mb-6 text-center text-sm">
                    <div>
                      <p className="font-semibold text-gray-900 dark:text-white">{merchant.totalProducts}</p>
                      <p className="text-gray-500 dark:text-gray-400">Products</p>
                    </div>
                    <div>
                      <p className="font-semibold text-gray-900 dark:text-white">{merchant.totalSales.toLocaleString()}</p>
                      <p className="text-gray-500 dark:text-gray-400">Sales</p>
                    </div>
                    <div>
                      <p className="font-semibold text-gray-900 dark:text-white">{merchant.universities}</p>
                      <p className="text-gray-500 dark:text-gray-400">Universities</p>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex space-x-3">
                    <Link
                      to={`/merchant/${merchant.id}`}
                      className="flex-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 py-3 rounded-xl font-semibold hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors text-center"
                    >
                      View Profile
                    </Link>
                    <button
                      onClick={() => handleChatMerchant(merchant.id)}
                      className="flex-1 bg-accent-600 hover:bg-accent-700 text-white py-3 rounded-xl font-semibold transition-colors flex items-center justify-center space-x-2"
                    >
                      <MessageCircle className="w-4 h-4" />
                      <span>Message</span>
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* No Results */}
          {filteredMerchants.length === 0 && (
            <div className="text-center py-12">
              <Store className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                No merchants found
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                Try adjusting your search criteria or filters
              </p>
            </div>
          )}

          {/* Footer Info */}
          <div className="mt-12 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-2xl p-6">
            <div className="flex items-start space-x-4">
              <div className="w-10 h-10 bg-blue-100 dark:bg-blue-800 rounded-xl flex items-center justify-center flex-shrink-0">
                <Users className="w-5 h-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-200 mb-2">
                  Merchant Partner Network
                </h3>
                <p className="text-blue-800 dark:text-blue-300 text-sm leading-relaxed">
                  Connect with verified merchant partners to explore collaboration opportunities, share resources, 
                  and grow your business together. Our directory features trusted partners across various categories 
                  serving students nationwide.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
  );
};

export default MerchantDirectory;
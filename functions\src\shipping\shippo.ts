import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';

// Shippo API configuration
const SHIPPO_API_KEY = functions.config().shippo?.api_key || process.env.SHIPPO_API_KEY;
const SHIPPO_BASE_URL = 'https://api.goshippo.com';

interface ShippoAddress {
  name: string;
  street1: string;
  street2?: string;
  city: string;
  state: string;
  zip: string;
  country: string;
  phone?: string;
}

interface ShippoParcel {
  length: string;
  width: string;
  height: string;
  distance_unit: 'in' | 'cm';
  weight: string;
  mass_unit: 'oz' | 'lb';
}

interface ShippoRate {
  object_id: string;
  amount: string;
  currency: string;
  provider: string;
  servicelevel: {
    name: string;
    token: string;
  };
  estimated_days: number;
}

interface ShippoShipment {
  address_from: ShippoAddress;
  address_to: ShippoAddress;
  parcels: ShippoParcel[];
  rates?: ShippoRate[];
}

// Get shipping rates for an order
export const getShippingRates = functions.https.onCall(async (data: any, context: any) => {
  try {
    if (!context?.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const { listingId, shippingAddress } = data;

    if (!listingId || !shippingAddress) {
      throw new functions.https.HttpsError('invalid-argument', 'Listing ID and shipping address are required');
    }

    // Get listing details
    const listingDoc = await admin.firestore().collection('listings').doc(listingId).get();
    if (!listingDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'Listing not found');
    }

    const listing = listingDoc.data();
    
    // Check if listing supports shipping
    if (listing?.deliveryMethod !== 'mail') {
      throw new functions.https.HttpsError('invalid-argument', 'This listing does not support shipping');
    }

    // Get seller address from listing
    const sellerAddress = listing?.shippingOptions?.sellerAddress;
    if (!sellerAddress) {
      throw new functions.https.HttpsError('failed-precondition', 'Seller address not configured');
    }

    // Get package details
    const packageDetails = listing?.shippingOptions?.packageDetails;
    if (!packageDetails) {
      throw new functions.https.HttpsError('failed-precondition', 'Package details not configured');
    }

    // Create shipment to get rates
    const shipment = await createShippoShipment(sellerAddress, shippingAddress, packageDetails);
    
    // Filter and format rates
    const formattedRates = shipment.rates?.map((rate: ShippoRate) => ({
      id: rate.object_id,
      amount: parseFloat(rate.amount),
      currency: rate.currency,
      provider: rate.provider,
      service: rate.servicelevel.name,
      estimatedDays: rate.estimated_days,
      displayName: `${rate.provider} ${rate.servicelevel.name} (${rate.estimated_days} days)`
    })) || [];

    return {
      success: true,
      rates: formattedRates.sort((a, b) => a.amount - b.amount) // Sort by price
    };

  } catch (error) {
    console.error('Error getting shipping rates:', error);
    throw error;
  }
});

// Generate shipping label after payment
export const generateShippingLabel = functions.https.onCall(async (data: any, context: any) => {
  try {
    if (!context?.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const { orderId, rateId } = data;

    if (!orderId || !rateId) {
      throw new functions.https.HttpsError('invalid-argument', 'Order ID and rate ID are required');
    }

    // Get order details
    const orderDoc = await admin.firestore().collection('orders').doc(orderId);
    const orderSnapshot = await orderDoc.get();

    if (!orderSnapshot.exists) {
      throw new functions.https.HttpsError('not-found', 'Order not found');
    }

    const orderData = orderSnapshot.data();

    // Verify user is the seller
    if (orderData?.sellerId !== context?.auth?.uid) {
      throw new functions.https.HttpsError('permission-denied', 'Only the seller can generate shipping labels');
    }

    // Create shipping label transaction
    const transaction = await createShippoTransaction(rateId);

    if (transaction.status !== 'SUCCESS') {
      throw new functions.https.HttpsError('internal', 'Failed to create shipping label');
    }

    // Update order with shipping information
    await orderDoc.update({
      status: 'shipped',
      shippingLabelUrl: transaction.label_url,
      shippingTrackingUrl: transaction.tracking_url_provider,
      shippingTrackingNumber: transaction.tracking_number,
      shippedAt: admin.firestore.Timestamp.now(),
      deliveryConfirmedAt: null, // Will be set when delivered
      returnEligibleUntil: null, // Will be set when delivered
      updatedAt: admin.firestore.Timestamp.now()
    });

    // Store shipping label details
    await admin.firestore().collection('shippingLabels').doc(orderId).set({
      orderId,
      labelUrl: transaction.label_url,
      trackingNumber: transaction.tracking_number,
      trackingUrl: transaction.tracking_url_provider,
      carrier: transaction.rate?.provider,
      service: transaction.rate?.servicelevel?.name,
      createdAt: admin.firestore.Timestamp.now()
    });

    return {
      success: true,
      labelUrl: transaction.label_url,
      trackingNumber: transaction.tracking_number,
      trackingUrl: transaction.tracking_url_provider
    };

  } catch (error) {
    console.error('Error generating shipping label:', error);
    throw error;
  }
});

// Track shipment status
export const trackShipment = functions.https.onCall(async (data: any, context: any) => {
  try {
    if (!context?.auth) {
      throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }

    const { orderId } = data;

    if (!orderId) {
      throw new functions.https.HttpsError('invalid-argument', 'Order ID is required');
    }

    // Get order details
    const orderDoc = await admin.firestore().collection('orders').doc(orderId).get();
    if (!orderDoc.exists) {
      throw new functions.https.HttpsError('not-found', 'Order not found');
    }

    const orderData = orderDoc.data();
    const trackingNumber = orderData?.shippingTrackingNumber;

    if (!trackingNumber) {
      throw new functions.https.HttpsError('failed-precondition', 'No tracking number available');
    }

    // Get tracking info from Shippo
    const trackingInfo = await getShippoTracking(trackingNumber);

    // Check if delivered
    if (trackingInfo.tracking_status?.status === 'DELIVERED') {
      // Update order status and set return window
      const deliveryDate = new Date();
      const returnDeadline = new Date(deliveryDate.getTime() + 3 * 24 * 60 * 60 * 1000); // 3 days

      await admin.firestore().collection('orders').doc(orderId).update({
        status: 'delivered',
        deliveryConfirmedAt: admin.firestore.Timestamp.fromDate(deliveryDate),
        returnEligibleUntil: admin.firestore.Timestamp.fromDate(returnDeadline),
        updatedAt: admin.firestore.Timestamp.now()
      });
    }

    return {
      success: true,
      tracking: trackingInfo
    };

  } catch (error) {
    console.error('Error tracking shipment:', error);
    throw error;
  }
});

// Helper functions for Shippo API calls
async function createShippoShipment(fromAddress: ShippoAddress, toAddress: ShippoAddress, packageDetails: any): Promise<ShippoShipment> {
  const shipmentData = {
    address_from: fromAddress,
    address_to: toAddress,
    parcels: [{
      length: packageDetails.dimensions?.length?.toString() || '10',
      width: packageDetails.dimensions?.width?.toString() || '8',
      height: packageDetails.dimensions?.height?.toString() || '4',
      distance_unit: packageDetails.dimensions?.unit || 'in',
      weight: packageDetails.weight?.value?.toString() || '1',
      mass_unit: packageDetails.weight?.unit || 'lb'
    }],
    async: false
  };

  const response = await fetch(`${SHIPPO_BASE_URL}/shipments/`, {
    method: 'POST',
    headers: {
      'Authorization': `ShippoToken ${SHIPPO_API_KEY}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(shipmentData)
  });

  if (!response.ok) {
    throw new Error(`Shippo API error: ${response.status}`);
  }

  return await response.json();
}

async function createShippoTransaction(rateId: string): Promise<any> {
  const transactionData = {
    rate: rateId,
    label_file_type: 'PDF',
    async: false
  };

  const response = await fetch(`${SHIPPO_BASE_URL}/transactions/`, {
    method: 'POST',
    headers: {
      'Authorization': `ShippoToken ${SHIPPO_API_KEY}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(transactionData)
  });

  if (!response.ok) {
    throw new Error(`Shippo transaction error: ${response.status}`);
  }

  return await response.json();
}

async function getShippoTracking(trackingNumber: string): Promise<any> {
  const response = await fetch(`${SHIPPO_BASE_URL}/tracks/${trackingNumber}`, {
    method: 'GET',
    headers: {
      'Authorization': `ShippoToken ${SHIPPO_API_KEY}`
    }
  });

  if (!response.ok) {
    throw new Error(`Shippo tracking error: ${response.status}`);
  }

  return await response.json();
}

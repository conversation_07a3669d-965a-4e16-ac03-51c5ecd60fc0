import { httpsCallable } from 'firebase/functions';
import { functions } from '../firebase/config';
import { NotificationSendRequest } from '../types/notifications';

// Service for sending notifications via Firebase Functions
export class NotificationService {
  private static sendNotificationFunction = httpsCallable(functions, 'sendNotification');
  private static sendBatchNotificationsFunction = httpsCallable(functions, 'sendBatchNotifications');

  /**
   * Send a notification to a single user
   */
  static async sendNotification(request: NotificationSendRequest): Promise<{
    success: boolean;
    channels: string[];
    error?: string;
  }> {
    try {
      const result = await this.sendNotificationFunction(request);
      return result.data as { success: boolean; channels: string[] };
    } catch (error) {
      console.error('Error sending notification:', error);
      return {
        success: false,
        channels: [],
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Send notifications to multiple users
   */
  static async sendBatchNotifications(
    userIds: string[],
    notification: Omit<NotificationSendRequest, 'userId'>
  ): Promise<{
    success: boolean;
    results: Array<{ userId: string; success: boolean; error?: string }>;
    error?: string;
  }> {
    try {
      const result = await this.sendBatchNotificationsFunction({
        userIds,
        notification
      });
      return result.data as {
        success: boolean;
        results: Array<{ userId: string; success: boolean; error?: string }>;
      };
    } catch (error) {
      console.error('Error sending batch notifications:', error);
      return {
        success: false,
        results: [],
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Send a listing sold notification
   */
  static async sendListingSoldNotification(
    sellerId: string,
    listingTitle: string,
    amount: number,
    buyerName: string,
    orderId: string
  ) {
    return this.sendNotification({
      userId: sellerId,
      type: 'listing_sold',
      title: 'Item Sold!',
      message: `Your "${listingTitle}" was purchased by ${buyerName} for $${amount.toFixed(2)}`,
      channels: ['in_app', 'push', 'email'],
      priority: 'high',
      link: `/orders/${orderId}`,
      amount,
      orderId,
      metadata: {
        buyerName,
        listingTitle
      }
    });
  }

  /**
   * Send an order confirmed notification
   */
  static async sendOrderConfirmedNotification(
    buyerId: string,
    listingTitle: string,
    amount: number,
    orderId: string,
    secretCode?: string
  ) {
    return this.sendNotification({
      userId: buyerId,
      type: 'order_confirmed',
      title: 'Order Confirmed!',
      message: `Your order for "${listingTitle}" has been confirmed. ${secretCode ? `Secret code: ${secretCode}` : ''}`,
      channels: ['in_app', 'push', 'email'],
      priority: 'high',
      link: `/orders/${orderId}`,
      amount,
      orderId,
      secretCode,
      metadata: {
        listingTitle
      }
    });
  }

  /**
   * Send a wallet credited notification
   */
  static async sendWalletCreditedNotification(
    userId: string,
    amount: number,
    reason: string,
    transactionId?: string
  ) {
    return this.sendNotification({
      userId,
      type: 'wallet_credited',
      title: 'Wallet Credited',
      message: `$${amount.toFixed(2)} has been added to your wallet. ${reason}`,
      channels: ['in_app', 'push', 'email'],
      priority: 'normal',
      link: '/wallet',
      amount,
      metadata: {
        reason,
        transactionId
      }
    });
  }

  /**
   * Send a wallet debited notification
   */
  static async sendWalletDebitedNotification(
    userId: string,
    amount: number,
    reason: string,
    transactionId?: string
  ) {
    return this.sendNotification({
      userId,
      type: 'wallet_debited',
      title: 'Wallet Debited',
      message: `$${amount.toFixed(2)} has been deducted from your wallet. ${reason}`,
      channels: ['in_app', 'push'],
      priority: 'normal',
      link: '/wallet',
      amount,
      metadata: {
        reason,
        transactionId
      }
    });
  }

  /**
   * Send a shipping reminder notification
   */
  static async sendShippingReminderNotification(
    sellerId: string,
    orderId: string,
    listingTitle: string,
    hoursRemaining: number
  ) {
    return this.sendNotification({
      userId: sellerId,
      type: '48_hour_shipping_reminder',
      title: 'Shipping Reminder',
      message: `Please ship "${listingTitle}" within ${hoursRemaining} hours to avoid penalties.`,
      channels: ['in_app', 'push', 'email'],
      priority: 'high',
      link: `/orders/${orderId}`,
      actionRequired: true,
      orderId,
      metadata: {
        listingTitle,
        hoursRemaining
      }
    });
  }

  /**
   * Send a payment failed notification
   */
  static async sendPaymentFailedNotification(
    userId: string,
    orderId: string,
    amount: number,
    reason: string
  ) {
    return this.sendNotification({
      userId,
      type: 'payment_failed',
      title: 'Payment Failed',
      message: `Your payment of $${amount.toFixed(2)} failed. ${reason}`,
      channels: ['in_app', 'push', 'email'],
      priority: 'urgent',
      link: `/orders/${orderId}`,
      actionRequired: true,
      amount,
      orderId,
      metadata: {
        reason
      }
    });
  }

  /**
   * Send a delivery confirmation notification
   */
  static async sendDeliveryConfirmationNotification(
    buyerId: string,
    orderId: string,
    listingTitle: string,
    secretCode: string
  ) {
    return this.sendNotification({
      userId: buyerId,
      type: 'delivery_confirmation',
      title: 'Delivery Confirmation Required',
      message: `Please confirm delivery of "${listingTitle}" using your secret code.`,
      channels: ['in_app', 'push'],
      priority: 'high',
      link: `/orders/${orderId}`,
      actionRequired: true,
      orderId,
      secretCode,
      metadata: {
        listingTitle
      }
    });
  }

  /**
   * Send a user warning notification
   */
  static async sendUserWarningNotification(
    userId: string,
    title: string,
    message: string,
    actionUrl?: string
  ) {
    return this.sendNotification({
      userId,
      type: 'user_warning',
      title,
      message,
      channels: ['in_app', 'push', 'email'],
      priority: 'urgent',
      link: actionUrl,
      actionRequired: true,
      metadata: {
        warningType: 'user_warning'
      }
    });
  }

  /**
   * Send a platform announcement to all users
   */
  static async sendPlatformAnnouncement(
    userIds: string[],
    title: string,
    message: string,
    actionUrl?: string
  ) {
    return this.sendBatchNotifications(userIds, {
      type: 'platform_announcement',
      title,
      message,
      channels: ['in_app', 'push'],
      priority: 'normal',
      link: actionUrl,
      metadata: {
        announcementType: 'platform'
      }
    });
  }
}

export default NotificationService;

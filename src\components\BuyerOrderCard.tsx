import React, { useState } from 'react';
import { 
  Clock, Package, Truck, CheckCircle, AlertCircle, Eye, RotateCcw, 
  MessageCircle, Copy, ExternalLink, Shield, MapPin, Calendar,
  DollarSign, User, FileText
} from 'lucide-react';
import { Link } from 'react-router-dom';
import { useStripeCheckout } from '../hooks/useStripeCheckout';
import { Order } from '../firebase/types';
import { formatPrice } from '../utils/priceUtils';

interface BuyerOrderCardProps {
  order: Order;
  onOrderUpdate?: () => void;
}

const BuyerOrderCard: React.FC<BuyerOrderCardProps> = ({ order, onOrderUpdate }) => {
  const { releaseFundsWithCode, requestReturn, isLoading } = useStripeCheckout();
  const [secretCode, setSecretCode] = useState('');
  const [showSecretInput, setShowSecretInput] = useState(false);
  const [showReturnForm, setShowReturnForm] = useState(false);
  const [returnReason, setReturnReason] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [showDetails, setShowDetails] = useState(false);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'in_progress':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      case 'shipped':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';
      case 'delivered':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'completed':
        return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400';
      case 'return_requested':
        return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'in_progress':
        return 'In Progress';
      case 'shipped':
        return 'Shipped';
      case 'delivered':
        return 'Delivered';
      case 'completed':
        return 'Completed';
      case 'return_requested':
        return 'Return Requested';
      default:
        return status;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'in_progress':
        return <Clock className="w-4 h-4" />;
      case 'shipped':
        return <Truck className="w-4 h-4" />;
      case 'delivered':
        return <Package className="w-4 h-4" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4" />;
      case 'return_requested':
        return <RotateCcw className="w-4 h-4" />;
      default:
        return <AlertCircle className="w-4 h-4" />;
    }
  };

  const handleSecretCodeSubmit = async () => {
    if (!secretCode || secretCode.length !== 6) {
      setError('Please enter a valid 6-digit secret code');
      return;
    }

    try {
      setError(null);
      await releaseFundsWithCode(order.id, secretCode);
      setSuccess('Funds released successfully! Order completed.');
      setShowSecretInput(false);
      setSecretCode('');
      onOrderUpdate?.();
      
      // Log to memory
      console.log(`🎯 BUYER ACTION: Secret code entered for order ${order.id}`);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to release funds');
    }
  };

  const handleRequestReturn = async () => {
    try {
      setError(null);
      await requestReturn(order.id, returnReason);
      setSuccess('Return request submitted successfully!');
      setShowReturnForm(false);
      setReturnReason('');
      onOrderUpdate?.();
      
      // Log to memory
      console.log(`🔄 BUYER ACTION: Return requested for order ${order.id} - Reason: ${returnReason}`);
    } catch (error) {
      setError(error instanceof Error ? error.message : 'Failed to request return');
    }
  };

  const isReturnEligible = () => {
    if (!order.returnEligibleUntil || order.status !== 'delivered') return false;
    const deadline = order.returnEligibleUntil.toDate();
    return new Date() < deadline;
  };

  const getTimeRemaining = () => {
    if (!order.returnEligibleUntil) return null;
    const deadline = order.returnEligibleUntil.toDate();
    const now = new Date();
    const diff = deadline.getTime() - now.getTime();
    
    if (diff <= 0) return 'Expired';
    
    const hours = Math.floor(diff / (1000 * 60 * 60));
    if (hours < 24) {
      return `${hours}h remaining`;
    }
    const days = Math.floor(hours / 24);
    return `${days}d remaining`;
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    setSuccess('Copied to clipboard!');
    setTimeout(() => setSuccess(null), 2000);
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6 border border-gray-200 dark:border-gray-700">
      {/* Header */}
      <div className="flex items-start justify-between mb-4">
        <div className="flex items-start space-x-4">
          <img
            src={order.listingImage || '/placeholder-image.jpg'}
            alt={order.title || order.listingTitle}
            className="w-16 h-16 rounded-xl object-cover"
          />
          <div>
            <h3 className="font-semibold text-gray-900 dark:text-white mb-1">
              {order.title || order.listingTitle}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Order #{order.id.slice(-8)} • Buyer View
            </p>
            <p className="text-lg font-bold text-gray-900 dark:text-white">
              ${formatPrice(order.finalStripeAmount || order.amount)}
            </p>
            {order.walletAmountUsed && order.walletAmountUsed > 0 && (
              <p className="text-sm text-green-600 dark:text-green-400">
                💰 Wallet credit used: ${formatPrice(order.walletAmountUsed)}
              </p>
            )}
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {getStatusIcon(order.status)}
          <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(order.status)}`}>
            {getStatusText(order.status)}
          </span>
        </div>
      </div>

      {/* Delivery Type & Tracking */}
      <div className="mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <MapPin className="w-4 h-4 text-gray-600 dark:text-gray-400" />
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Delivery: {order.deliveryType === 'shipping' ? 'Shipping' : 'In-Person'}
            </span>
          </div>
          {order.shippingTrackingNumber && (
            <a
              href={order.shippingTrackingUrl || '#'}
              target="_blank"
              rel="noopener noreferrer"
              className="flex items-center space-x-1 text-blue-600 dark:text-blue-400 hover:text-blue-700 text-sm"
            >
              <Truck className="w-4 h-4" />
              <span>Track Package</span>
              <ExternalLink className="w-3 h-3" />
            </a>
          )}
        </div>
        {order.shippingTrackingNumber && (
          <div className="mt-2 flex items-center space-x-2">
            <span className="text-xs text-gray-600 dark:text-gray-400">Tracking:</span>
            <code className="text-xs bg-gray-200 dark:bg-gray-600 px-2 py-1 rounded">
              {order.shippingTrackingNumber}
            </code>
            <button
              onClick={() => copyToClipboard(order.shippingTrackingNumber!)}
              className="text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
            >
              <Copy className="w-3 h-3" />
            </button>
          </div>
        )}
      </div>

      {/* Secret Code Display - Show to buyer when payment is completed */}
      {order.secretCode && ['payment_succeeded', 'payment_completed', 'in_progress', 'shipped', 'delivered'].includes(order.status) && (
        <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-blue-900 dark:text-blue-200">🔐 Your Secret Code</p>
              <p className="text-xs text-blue-700 dark:text-blue-300 mb-1">
                Use this code to confirm delivery and release payment to seller
              </p>
              <code className="text-lg font-mono font-bold text-blue-800 dark:text-blue-300">
                {order.secretCode}
              </code>
            </div>
            <button
              onClick={() => copyToClipboard(order.secretCode!)}
              className="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
              title="Copy secret code"
            >
              <Copy className="w-4 h-4" />
            </button>
          </div>
        </div>
      )}

      {/* Return Window Info */}
      {order.status === 'delivered' && (
        <div className="mb-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
          <div className="flex items-center justify-between">
            <span className="text-sm text-yellow-800 dark:text-yellow-400">
              Return window: {getTimeRemaining()}
            </span>
            {isReturnEligible() && (
              <button
                onClick={() => setShowReturnForm(true)}
                className="text-sm text-yellow-600 hover:text-yellow-700 dark:text-yellow-400 dark:hover:text-yellow-300"
              >
                Request Return
              </button>
            )}
          </div>
        </div>
      )}

      {/* Error/Success Messages */}
      {error && (
        <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
          <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
        </div>
      )}
      
      {success && (
        <div className="mb-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <p className="text-sm text-green-600 dark:text-green-400">{success}</p>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex flex-wrap gap-2 mb-4">
        {/* Enter Secret Code */}
        {order.status === 'delivered' && !order.releasedToSeller && (
          <button
            onClick={() => setShowSecretInput(true)}
            disabled={isLoading}
            className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
          >
            <Shield className="w-4 h-4" />
            <span>Enter Secret Code</span>
          </button>
        )}

        {/* Message Seller */}
        <Link
          to={`/messages?userId=${order.sellerId}`}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
        >
          <MessageCircle className="w-4 h-4" />
          <span>Message Seller</span>
        </Link>

        {/* View Item */}
        <Link
          to={`/listing/${order.listingId}`}
          className="flex items-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
        >
          <Eye className="w-4 h-4" />
          <span>View Item</span>
        </Link>

        {/* Toggle Details */}
        <button
          onClick={() => setShowDetails(!showDetails)}
          className="flex items-center space-x-2 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
        >
          <FileText className="w-4 h-4" />
          <span>{showDetails ? 'Hide' : 'Show'} Details</span>
        </button>
      </div>

      {/* Expanded Details */}
      {showDetails && (
        <div className="border-t border-gray-200 dark:border-gray-700 pt-4 space-y-4">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600 dark:text-gray-400">Order Date:</span>
              <p className="font-medium">{new Date(order.createdAt.toDate()).toLocaleDateString()}</p>
            </div>
            <div>
              <span className="text-gray-600 dark:text-gray-400">Platform Fee:</span>
              <p className="font-medium">${formatPrice(order.platformFee || 0)}</p>
            </div>
            <div>
              <span className="text-gray-600 dark:text-gray-400">Seller Receives:</span>
              <p className="font-medium">${formatPrice(order.sellerAmount || 0)}</p>
            </div>
            <div>
              <span className="text-gray-600 dark:text-gray-400">Status:</span>
              <p className="font-medium">{order.status}</p>
            </div>
          </div>
        </div>
      )}

      {/* Secret Code Input Modal */}
      {showSecretInput && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">Enter Secret Code</h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
              Enter the 6-digit secret code provided by the seller to confirm delivery and release funds.
            </p>
            <input
              type="text"
              value={secretCode}
              onChange={(e) => setSecretCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
              placeholder="000000"
              className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg mb-4 text-center text-2xl tracking-widest"
              maxLength={6}
            />
            <div className="flex space-x-3">
              <button
                onClick={() => setShowSecretInput(false)}
                className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                Cancel
              </button>
              <button
                onClick={handleSecretCodeSubmit}
                disabled={isLoading || secretCode.length !== 6}
                className="flex-1 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
              >
                Confirm
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Return Request Modal */}
      {showReturnForm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">Request Return</h3>
            <textarea
              value={returnReason}
              onChange={(e) => setReturnReason(e.target.value)}
              placeholder="Please explain why you want to return this item..."
              className="w-full px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg mb-4 h-24 resize-none"
            />
            <div className="flex space-x-3">
              <button
                onClick={() => setShowReturnForm(false)}
                className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700"
              >
                Cancel
              </button>
              <button
                onClick={handleRequestReturn}
                disabled={isLoading}
                className="flex-1 px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50"
              >
                Request Return
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BuyerOrderCard;

# Hive Campus Marketplace Information

## Summary
Hive Campus is a student-exclusive marketplace application built with React, TypeScript, and Firebase. It enables students to buy and sell items, communicate through real-time messaging, and process payments via Stripe integration. The application features role-based access control (student, merchant, admin), Microsoft SSO authentication for .edu emails, and a modern UI with a liquid glass aesthetic.

## Structure
- **src/**: Frontend React application code (components, contexts, hooks, pages)
- **functions/**: Firebase Cloud Functions for backend services
- **public/**: Static assets and PWA configuration
- **docs/**: Documentation for various integrations (Reeflex, Sentry, etc.)

## Language & Runtime
**Language**: TypeScript/JavaScript
**Version**: TypeScript 5.5.4
**Build System**: Vite 5.4.2
**Package Manager**: npm

## Dependencies
**Main Dependencies**:
- React 18.3.1
- Firebase 11.9.1
- React Router 6.20.1
- Sentry 9.33.0
- Stripe (via Firebase Functions)

**Development Dependencies**:
- TypeScript 5.5.4
- Vite 5.4.2
- Tailwind CSS 3.4.1
- ESLint 9.9.1
- Vite PWA Plugin 1.0.0

## Build & Installation
```bash
# Install dependencies
npm install
cd functions
npm install
cd ..

# Development
npm run dev

# Production build
npm run build

# Deploy Firebase Functions
cd functions
npm run deploy
```

## Firebase Backend
**Configuration**: Firebase project with Authentication, Firestore, Storage, and Functions
**Node Version**: Node.js 18 (specified in functions/package.json)
**Services**:
- Authentication: Email/password and Microsoft SSO
- Firestore: Database for users, listings, messages, feedback
- Storage: File storage for user profiles, listings, and issue reports
- Functions: Backend logic for auth, listings, messaging, payments

## Stripe Integration
**Implementation**: Firebase Functions with Express endpoints
**Features**:
- Checkout sessions for purchases
- Connect accounts for sellers
- Escrow system with release codes
- Wallet balance management
- Shipping label generation

## Frontend Architecture
**Main Entry Point**: src/main.tsx
**State Management**: React Context API with custom hooks
**Routing**: React Router v6
**UI Framework**: Custom components with Tailwind CSS
**PWA Support**: Configured with vite-plugin-pwa

## Third-Party Integrations
**Sentry**: Error tracking and performance monitoring
**Reeflex**: Analytics integration
**Microsoft Auth**: SSO for university accounts

## Security
- Firestore and Storage security rules
- Authentication validation for .edu emails
- Role-based access control
- Input validation on client and server
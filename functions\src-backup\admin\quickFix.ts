import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';

// Quick fix function that can be called without authentication
// This is a one-time setup function - should be removed after use
export const quickFixAdmin = functions.https.onRequest(async (req, res) => {
  try {
    // Only allow this in development/setup mode
    const isDev = process.env.NODE_ENV !== 'production';
    
    if (!isDev) {
      res.status(403).json({ error: 'This function is only available in development mode' });
      return;
    }

    const adminEmail = '<EMAIL>';
    
    console.log('Quick fixing admin user:', adminEmail);
    
    // Get user by email
    let userRecord;
    try {
      userRecord = await admin.auth().getUserByEmail(adminEmail);
    } catch (_error) {
      res.status(404).json({ error: 'Admin user not found. Please create the user first.' });
      return;
    }
    
    console.log('Found user:', userRecord.uid);
    
    // Set custom claims with both admin and role
    await admin.auth().setCustomUserClaims(userRecord.uid, { 
      admin: true,
      role: 'admin'
    });
    console.log('✅ Custom claims set');
    
    // Update or create user profile in Firestore with complete admin setup
    await admin.firestore().collection('users').doc(userRecord.uid).set({
      uid: userRecord.uid,
      name: userRecord.displayName || 'Admin User',
      email: userRecord.email,
      role: 'admin',
      university: 'Hive Campus Admin',
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now(),
      emailVerified: true,
      status: 'active',
      adminLevel: 'super',
      permissions: ['user_management', 'content_moderation', 'analytics', 'system_settings', 'super_admin']
    }, { merge: true });
    console.log('✅ Firestore document updated');
    
    // Create admin settings document if it doesn't exist
    await admin.firestore().collection('adminSettings').doc('security').set({
      adminPinRequired: true,
      sessionTimeoutMinutes: 240, // 4 hours
      maxLoginAttempts: 5,
      createdAt: admin.firestore.Timestamp.now()
    }, { merge: true });
    console.log('✅ Admin settings created');
    
    // Create some missing collections with default documents
    const collections = [
      { name: 'reports', doc: 'default', data: { created: admin.firestore.Timestamp.now() } },
      { name: 'shippingLabels', doc: 'default', data: { created: admin.firestore.Timestamp.now() } },
      { name: 'walletTransactions', doc: 'default', data: { created: admin.firestore.Timestamp.now() } },
      { name: 'walletReports', doc: 'default', data: { created: admin.firestore.Timestamp.now() } },
      { name: 'universityAnalytics', doc: 'default', data: { created: admin.firestore.Timestamp.now() } },
      { name: 'systemMetrics', doc: 'default', data: { created: admin.firestore.Timestamp.now() } },
      { name: 'adminLogs', doc: 'default', data: { created: admin.firestore.Timestamp.now() } }
    ];

    for (const collection of collections) {
      await admin.firestore().collection(collection.name).doc(collection.doc).set(collection.data, { merge: true });
    }
    console.log('✅ Default collections created');
    
    console.log('🎉 Admin user fixed successfully!');
    
    res.json({
      success: true,
      message: `Admin user fixed for ${adminEmail}`,
      uid: userRecord.uid,
      instructions: [
        '1. Admin user is now properly configured',
        '2. <NAME_EMAIL>',
        '3. Navigate to admin dashboard',
        '4. All admin sections should now work',
        '5. Set up 8-digit PIN when ready'
      ]
    });

  } catch (error) {
    console.error('❌ Error fixing admin user:', error);
    res.status(500).json({ 
      error: 'Failed to fix admin user', 
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Function to check admin status
export const checkAdminStatus = functions.https.onRequest(async (req, res) => {
  try {
    const adminEmail = '<EMAIL>';
    
    // Get user by email
    const userRecord = await admin.auth().getUserByEmail(adminEmail);
    
    // Get custom claims
    const customClaims = userRecord.customClaims || {};
    
    // Get Firestore document
    const userDoc = await admin.firestore().collection('users').doc(userRecord.uid).get();
    const userData = userDoc.data();
    
    res.json({
      uid: userRecord.uid,
      email: userRecord.email,
      emailVerified: userRecord.emailVerified,
      customClaims,
      firestoreData: userData,
      isAdminConfigured: customClaims.admin === true && userData?.role === 'admin'
    });

  } catch (error) {
    console.error('Error checking admin status:', error);
    res.status(500).json({ 
      error: 'Failed to check admin status', 
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

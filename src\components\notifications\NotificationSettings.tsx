import React, { useState } from 'react';
import { 
  Bell, 
  BellOff, 
  Mail, 
  Smartphone, 
  Settings, 
  Volume2, 
  VolumeX,
  Check,
  X
} from 'lucide-react';
import { useNotificationPreferences } from '../../hooks/useNotificationPreferences';
import { useFCMTokenManager } from '../../hooks/useFCMTokenManager';
import { NotificationType } from '../../types/notifications';

interface NotificationSettingsProps {
  className?: string;
}

// Notification type labels and descriptions
const NOTIFICATION_TYPE_CONFIG: Record<NotificationType, { label: string; description: string; category: string }> = {
  'listing_sold': { label: 'Listing Sold', description: 'When your items are purchased', category: 'Sales' },
  'order_confirmed': { label: 'Order Confirmed', description: 'When your orders are confirmed', category: 'Orders' },
  'order_delivered': { label: 'Order Delivered', description: 'When your orders are delivered', category: 'Orders' },
  'wallet_credited': { label: 'Wallet Credited', description: 'When money is added to your wallet', category: 'Payments' },
  'wallet_debited': { label: 'Wallet Debited', description: 'When money is deducted from your wallet', category: 'Payments' },
  'new_chat_message': { label: 'New Messages', description: 'When you receive chat messages', category: 'Communication' },
  '48_hour_shipping_reminder': { label: 'Shipping Reminders', description: 'Reminders to ship your sold items', category: 'Orders' },
  'platform_announcement': { label: 'Platform Updates', description: 'Important platform announcements', category: 'System' },
  'auction_update': { label: 'Auction Updates', description: 'Updates on auction items', category: 'Sales' },
  'payment_failed': { label: 'Payment Issues', description: 'When payments fail or have issues', category: 'Payments' },
  'user_warning': { label: 'Account Warnings', description: 'Important account-related warnings', category: 'System' },
  'delivery_confirmation': { label: 'Delivery Confirmations', description: 'When delivery confirmation is needed', category: 'Orders' },
  'admin_warning': { label: 'Admin Warnings', description: 'Important warnings from administrators', category: 'System' },
  'admin_broadcast': { label: 'Admin Announcements', description: 'Announcements from administrators', category: 'System' },
  'payment_success': { label: 'Payment Success', description: 'When payments are successful', category: 'Payments' }
};

export const NotificationSettings: React.FC<NotificationSettingsProps> = ({ className = '' }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  const {
    preferences,
    isLoading: preferencesLoading,
    togglePushNotifications,
    toggleEmailNotifications,
    togglePauseAll,
    muteCategory,
    unmuteCategory,
    toggleChannelForType
  } = useNotificationPreferences();

  const {
    isSupported: fcmSupported,
    permission,
    requestPermission,
    token
  } = useFCMTokenManager();

  // Show message temporarily
  const showMessage = (type: 'success' | 'error', text: string) => {
    setMessage({ type, text });
    setTimeout(() => setMessage(null), 3000);
  };

  // Handle push notification toggle
  const handlePushToggle = async () => {
    setIsLoading(true);
    try {
      if (!preferences.enable_push && permission !== 'granted') {
        const granted = await requestPermission();
        if (!granted) {
          showMessage('error', 'Push notification permission denied');
          setIsLoading(false);
          return;
        }
      }
      
      await togglePushNotifications();
      showMessage('success', `Push notifications ${!preferences.enable_push ? 'enabled' : 'disabled'}`);
    } catch (error) {
      showMessage('error', 'Failed to update push notification settings');
    }
    setIsLoading(false);
  };

  // Handle email notification toggle
  const handleEmailToggle = async () => {
    setIsLoading(true);
    try {
      await toggleEmailNotifications();
      showMessage('success', `Email notifications ${!preferences.enable_email ? 'enabled' : 'disabled'}`);
    } catch (error) {
      showMessage('error', 'Failed to update email notification settings');
    }
    setIsLoading(false);
  };

  // Handle pause all toggle
  const handlePauseAllToggle = async () => {
    setIsLoading(true);
    try {
      await togglePauseAll();
      showMessage('success', `All notifications ${!preferences.pause_all ? 'paused' : 'resumed'}`);
    } catch (error) {
      showMessage('error', 'Failed to update notification settings');
    }
    setIsLoading(false);
  };

  // Handle category mute toggle
  const handleCategoryToggle = async (type: NotificationType) => {
    setIsLoading(true);
    try {
      const isMuted = preferences.muted_categories.includes(type);
      if (isMuted) {
        await unmuteCategory(type);
      } else {
        await muteCategory(type);
      }
      showMessage('success', `${NOTIFICATION_TYPE_CONFIG[type].label} ${isMuted ? 'unmuted' : 'muted'}`);
    } catch (error) {
      showMessage('error', 'Failed to update notification settings');
    }
    setIsLoading(false);
  };

  // Group notification types by category
  const groupedTypes = Object.entries(NOTIFICATION_TYPE_CONFIG).reduce((acc, [type, config]) => {
    if (!acc[config.category]) {
      acc[config.category] = [];
    }
    acc[config.category].push({ type: type as NotificationType, ...config });
    return acc;
  }, {} as Record<string, Array<{ type: NotificationType; label: string; description: string; category: string }>>);

  if (preferencesLoading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="space-y-4">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded"></div>
          <div className="h-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center space-x-3">
        <Settings className="h-6 w-6 text-primary-600 dark:text-primary-400" />
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
          Notification Settings
        </h2>
      </div>

      {/* Status Message */}
      {message && (
        <div className={`
          p-3 rounded-lg flex items-center space-x-2
          ${message.type === 'success' ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' : ''}
          ${message.type === 'error' ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400' : ''}
        `}>
          {message.type === 'success' ? <Check className="h-4 w-4" /> : <X className="h-4 w-4" />}
          <span className="text-sm font-medium">{message.text}</span>
        </div>
      )}

      {/* Main Controls */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          General Settings
        </h3>
        
        <div className="space-y-4">
          {/* Pause All */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {preferences.pause_all ? <VolumeX className="h-5 w-5 text-gray-400" /> : <Volume2 className="h-5 w-5 text-gray-400" />}
              <div>
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  {preferences.pause_all ? 'Resume All Notifications' : 'Pause All Notifications'}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {preferences.pause_all ? 'Turn notifications back on' : 'Temporarily disable all notifications'}
                </p>
              </div>
            </div>
            <button
              onClick={handlePauseAllToggle}
              disabled={isLoading}
              className={`
                relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2
                ${preferences.pause_all ? 'bg-gray-200 dark:bg-gray-600' : 'bg-primary-600'}
                ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}
              `}
            >
              <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${preferences.pause_all ? 'translate-x-1' : 'translate-x-6'}`} />
            </button>
          </div>

          {/* Push Notifications */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Smartphone className="h-5 w-5 text-gray-400" />
              <div>
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  Push Notifications
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {fcmSupported ? 'Receive notifications on this device' : 'Not supported on this device'}
                </p>
                {token && (
                  <p className="text-xs text-green-600 dark:text-green-400">
                    ✓ Connected
                  </p>
                )}
              </div>
            </div>
            <button
              onClick={handlePushToggle}
              disabled={isLoading || !fcmSupported || preferences.pause_all}
              className={`
                relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2
                ${preferences.enable_push && !preferences.pause_all ? 'bg-primary-600' : 'bg-gray-200 dark:bg-gray-600'}
                ${isLoading || !fcmSupported || preferences.pause_all ? 'opacity-50 cursor-not-allowed' : ''}
              `}
            >
              <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${preferences.enable_push && !preferences.pause_all ? 'translate-x-6' : 'translate-x-1'}`} />
            </button>
          </div>

          {/* Email Notifications */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Mail className="h-5 w-5 text-gray-400" />
              <div>
                <p className="text-sm font-medium text-gray-900 dark:text-white">
                  Email Notifications
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  Receive important notifications via email
                </p>
              </div>
            </div>
            <button
              onClick={handleEmailToggle}
              disabled={isLoading || preferences.pause_all}
              className={`
                relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2
                ${preferences.enable_email && !preferences.pause_all ? 'bg-primary-600' : 'bg-gray-200 dark:bg-gray-600'}
                ${isLoading || preferences.pause_all ? 'opacity-50 cursor-not-allowed' : ''}
              `}
            >
              <span className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${preferences.enable_email && !preferences.pause_all ? 'translate-x-6' : 'translate-x-1'}`} />
            </button>
          </div>
        </div>
      </div>

      {/* Notification Categories */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6">
        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Notification Categories
        </h3>
        
        <div className="space-y-6">
          {Object.entries(groupedTypes).map(([category, types]) => (
            <div key={category}>
              <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                {category}
              </h4>
              <div className="space-y-3">
                {types.map(({ type, label, description }) => {
                  const isMuted = preferences.muted_categories.includes(type);
                  const isDisabled = preferences.pause_all || isLoading;
                  
                  return (
                    <div key={type} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        {isMuted ? <BellOff className="h-4 w-4 text-gray-400" /> : <Bell className="h-4 w-4 text-gray-400" />}
                        <div>
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {label}
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            {description}
                          </p>
                        </div>
                      </div>
                      <button
                        onClick={() => handleCategoryToggle(type)}
                        disabled={isDisabled}
                        className={`
                          relative inline-flex h-5 w-9 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2
                          ${!isMuted && !preferences.pause_all ? 'bg-primary-600' : 'bg-gray-200 dark:bg-gray-600'}
                          ${isDisabled ? 'opacity-50 cursor-not-allowed' : ''}
                        `}
                      >
                        <span className={`inline-block h-3 w-3 transform rounded-full bg-white transition-transform ${!isMuted && !preferences.pause_all ? 'translate-x-5' : 'translate-x-1'}`} />
                      </button>
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

@echo off
echo 🚀 Deploying Wallet Functions...
echo.

cd functions
echo 📦 Installing dependencies...
call npm install cors
if %errorlevel% neq 0 (
    echo ❌ Failed to install cors
    pause
    exit /b 1
)

call npm install
if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies
    pause
    exit /b 1
)

echo 🔨 Building functions...
call npm run build
if %errorlevel% neq 0 (
    echo ❌ Failed to build functions
    pause
    exit /b 1
)

echo 🚀 Deploying to Firebase...
call firebase deploy --only functions
if %errorlevel% neq 0 (
    echo ❌ Failed to deploy functions
    pause
    exit /b 1
)

echo.
echo ✅ Wallet functions deployed successfully!
echo.
echo 📋 Next Steps:
echo 1. Test the wallet page - should load without CORS errors
echo 2. Log into admin panel and go to 'Wallet Settings'
echo 3. Configure signup and referral bonuses as desired
echo.
pause

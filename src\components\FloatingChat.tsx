
import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../hooks/useAuth';
import './FloatingChat.css';


interface FloatingChatProps {
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
}

/**
 * FloatingChat - A floating chat button that provides quick access to messaging
 * Shows recent conversations and allows starting new chats
 */
const FloatingChat: React.FC<FloatingChatProps> = ({
  position = 'bottom-right'
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { currentUser } = useAuth();

  // Don't render if user is not authenticated
  if (!currentUser) {
    return null;
  }

  // Don't render if user is on the messages page
  if (location.pathname === '/messages') {
    return null;
  }

  // Adjust positioning to be higher and avoid mobile navigation
  const positionClasses = {
    'bottom-right': 'bottom-24 right-4 md:bottom-4 md:right-4',  // Higher on mobile, normal on desktop
    'bottom-left': 'bottom-24 left-4 md:bottom-4 md:left-4',
    'top-right': 'top-4 right-4',
    'top-left': 'top-4 left-4'
  };



  const handleFloatingChatClick = () => {
    navigate('/messages');
  };



  return (
    <div className={`fixed z-30 ${positionClasses[position]}`}>
      {/* Chat Button */}
      <button
        onClick={handleFloatingChatClick}
        className="faq-button"
        aria-label="Open messages"
      >
        <svg viewBox="0 0 24 24" fill="currentColor">
          <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2zm-2 12H6v-2h12v2zm0-3H6V9h12v2zm0-3H6V6h12v2z"/>
        </svg>
        <div className="tooltip">Chat</div>
      </button>
    </div>
  );
};

export default FloatingChat;
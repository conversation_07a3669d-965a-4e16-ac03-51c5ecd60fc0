import { useState, useEffect } from 'react';
import { useAuth } from './useAuth';
import { db } from '../firebase/config';
import { collection, query, where, onSnapshot, doc, updateDoc, Timestamp } from 'firebase/firestore';

interface PopupNotification {
  id: string;
  userId: string;
  type: 'delivery_confirmation' | 'payment_reminder' | 'order_update' | 'new_message' | 'admin_warning';
  orderId?: string;
  chatId?: string;
  senderId?: string;
  senderName?: string;
  title: string;
  message: string;
  actionRequired: boolean;
  expiresAt: Timestamp;
  createdAt: Timestamp;
  dismissed?: boolean;
}

export const usePopupNotifications = () => {
  const { user } = useAuth();
  const [notifications, setNotifications] = useState<PopupNotification[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    if (!user) {
      setNotifications([]);
      setIsLoading(false);
      return;
    }

    // Listen for popup notifications for the current user
    const notificationsQuery = query(
      collection(db, 'popup_notifications'),
      where('userId', '==', user.uid),
      where('dismissed', '!=', true)
    );

    const unsubscribe = onSnapshot(
      notificationsQuery,
      (snapshot) => {
        const notificationsList: PopupNotification[] = [];
        
        snapshot.forEach((doc) => {
          const data = doc.data();
          
          // Check if notification hasn't expired
          const now = new Date();
          const expiresAt = data.expiresAt.toDate();
          
          if (expiresAt > now) {
            notificationsList.push({
              id: doc.id,
              ...data
            } as PopupNotification);
          } else {
            // Auto-dismiss expired notifications
            dismissNotification(doc.id);
          }
        });
        
        // Sort by creation date (newest first)
        notificationsList.sort((a, b) => 
          b.createdAt.toDate().getTime() - a.createdAt.toDate().getTime()
        );
        
        setNotifications(notificationsList);
        setIsLoading(false);
      },
      (error) => {
        console.error('Error listening to popup notifications:', error);
        setIsLoading(false);
      }
    );

    return () => unsubscribe();
  }, [user]);

  // Dismiss a notification
  const dismissNotification = async (notificationId: string) => {
    try {
      await updateDoc(doc(db, 'popup_notifications', notificationId), {
        dismissed: true,
        dismissedAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error dismissing notification:', error);
    }
  };

  // Get the most urgent notification (delivery confirmations first)
  const getUrgentNotification = (): PopupNotification | null => {
    if (notifications.length === 0) return null;
    
    // Prioritize delivery confirmations
    const deliveryNotification = notifications.find(n => n.type === 'delivery_confirmation');
    if (deliveryNotification) return deliveryNotification;
    
    // Then other action-required notifications
    const actionRequired = notifications.find(n => n.actionRequired);
    if (actionRequired) return actionRequired;
    
    // Finally, any notification
    return notifications[0];
  };

  // Check if there are any delivery confirmations pending
  const hasDeliveryConfirmations = (): boolean => {
    return notifications.some(n => n.type === 'delivery_confirmation');
  };

  // Get count of notifications by type
  const getNotificationCounts = () => {
    return {
      total: notifications.length,
      deliveryConfirmations: notifications.filter(n => n.type === 'delivery_confirmation').length,
      paymentReminders: notifications.filter(n => n.type === 'payment_reminder').length,
      orderUpdates: notifications.filter(n => n.type === 'order_update').length,
      newMessages: notifications.filter(n => n.type === 'new_message').length,
      adminWarnings: notifications.filter(n => n.type === 'admin_warning').length,
      actionRequired: notifications.filter(n => n.actionRequired).length
    };
  };

  return {
    notifications,
    isLoading,
    dismissNotification,
    getUrgentNotification,
    hasDeliveryConfirmations,
    getNotificationCounts
  };
};

import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';

// Notification types
type NotificationType = 
  | 'listing_sold'
  | 'order_confirmed'
  | 'order_delivered'
  | 'wallet_credited'
  | 'wallet_debited'
  | 'new_chat_message'
  | '48_hour_shipping_reminder'
  | 'platform_announcement'
  | 'auction_update'
  | 'payment_failed'
  | 'user_warning'
  | 'delivery_confirmation'
  | 'admin_warning'
  | 'admin_broadcast'
  | 'payment_success';

type NotificationChannel = 'in_app' | 'push' | 'email';
type NotificationPriority = 'low' | 'normal' | 'high' | 'urgent';

interface NotificationRequest {
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  channels?: NotificationChannel[];
  priority?: NotificationPriority;
  link?: string;
  icon?: string;
  actionRequired?: boolean;
  expiresAt?: admin.firestore.Timestamp;
  metadata?: Record<string, any>;
  orderId?: string;
  listingId?: string;
  chatId?: string;
  senderId?: string;
  amount?: number;
  secretCode?: string;
}

interface NotificationPreferences {
  enable_push: boolean;
  enable_email: boolean;
  pause_all: boolean;
  muted_categories: NotificationType[];
  channels: {
    [key in NotificationChannel]: {
      enabled: boolean;
      types: NotificationType[];
    };
  };
}

// Default notification preferences
const DEFAULT_PREFERENCES: NotificationPreferences = {
  enable_push: true,
  enable_email: true,
  pause_all: false,
  muted_categories: [],
  channels: {
    in_app: {
      enabled: true,
      types: [
        'listing_sold', 'order_confirmed', 'order_delivered', 'wallet_credited',
        'wallet_debited', 'new_chat_message', '48_hour_shipping_reminder',
        'platform_announcement', 'auction_update', 'payment_failed', 'user_warning',
        'delivery_confirmation', 'admin_warning', 'admin_broadcast', 'payment_success'
      ]
    },
    push: {
      enabled: true,
      types: [
        'listing_sold', 'order_confirmed', 'order_delivered', 'wallet_credited',
        'new_chat_message', '48_hour_shipping_reminder', 'payment_failed',
        'user_warning', 'delivery_confirmation', 'admin_warning'
      ]
    },
    email: {
      enabled: true,
      types: [
        'wallet_credited', 'order_confirmed', '48_hour_shipping_reminder',
        'user_warning', 'payment_failed', 'payment_success'
      ]
    }
  }
};

// Get user notification preferences
async function getUserPreferences(userId: string): Promise<NotificationPreferences> {
  try {
    const userDoc = await admin.firestore().doc(`users/${userId}`).get();
    if (userDoc.exists()) {
      const userData = userDoc.data();
      const preferences = userData?.preferences?.notifications;
      if (preferences) {
        return {
          ...DEFAULT_PREFERENCES,
          ...preferences,
          channels: {
            ...DEFAULT_PREFERENCES.channels,
            ...preferences.channels
          }
        };
      }
    }
    return DEFAULT_PREFERENCES;
  } catch (error) {
    console.error('Error getting user preferences:', error);
    return DEFAULT_PREFERENCES;
  }
}

// Check if notification should be sent based on preferences
function shouldSendNotification(
  type: NotificationType,
  channel: NotificationChannel,
  preferences: NotificationPreferences
): boolean {
  // Check if all notifications are paused
  if (preferences.pause_all) {
    return false;
  }

  // Check if category is muted
  if (preferences.muted_categories.includes(type)) {
    return false;
  }

  // Check channel-specific settings
  const channelPrefs = preferences.channels[channel];
  if (!channelPrefs.enabled || !channelPrefs.types.includes(type)) {
    return false;
  }

  // Check global channel settings
  if (channel === 'push' && !preferences.enable_push) {
    return false;
  }
  if (channel === 'email' && !preferences.enable_email) {
    return false;
  }

  return true;
}

// Create in-app notification
async function createInAppNotification(request: NotificationRequest): Promise<void> {
  try {
    const notificationData = {
      type: request.type,
      title: request.title,
      message: request.message,
      icon: request.icon || '/icons/icon-192.png',
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      read: false,
      link: request.link,
      priority: request.priority || 'normal',
      actionRequired: request.actionRequired || false,
      expiresAt: request.expiresAt,
      metadata: request.metadata || {},
      orderId: request.orderId,
      listingId: request.listingId,
      chatId: request.chatId,
      senderId: request.senderId,
      amount: request.amount,
      secretCode: request.secretCode
    };

    await admin.firestore()
      .collection(`users/${request.userId}/notifications`)
      .add(notificationData);

    console.log(`In-app notification created for user ${request.userId}`);
  } catch (error) {
    console.error('Error creating in-app notification:', error);
    throw error;
  }
}

// Send push notification
async function sendPushNotification(request: NotificationRequest): Promise<void> {
  try {
    // Get user's FCM token
    const tokenDoc = await admin.firestore()
      .doc(`users/${request.userId}/fcmTokens/web`)
      .get();

    if (!tokenDoc.exists()) {
      console.log(`No FCM token found for user ${request.userId}`);
      return;
    }

    const tokenData = tokenDoc.data();
    if (!tokenData?.active || !tokenData?.token) {
      console.log(`Inactive FCM token for user ${request.userId}`);
      return;
    }

    // Prepare push notification payload
    const payload: admin.messaging.Message = {
      token: tokenData.token,
      notification: {
        title: request.title,
        body: request.message,
        imageUrl: request.icon
      },
      data: {
        type: request.type,
        link: request.link || '/',
        orderId: request.orderId || '',
        listingId: request.listingId || '',
        chatId: request.chatId || '',
        requireInteraction: request.actionRequired ? 'true' : 'false'
      },
      webpush: {
        headers: {
          TTL: '86400', // 24 hours
          Urgency: request.priority === 'urgent' ? 'high' : 'normal'
        },
        notification: {
          icon: request.icon || '/icons/icon-192.png',
          badge: '/icons/icon-96.png',
          tag: request.type,
          requireInteraction: request.actionRequired || false,
          actions: [
            {
              action: 'view',
              title: 'View'
            },
            {
              action: 'dismiss',
              title: 'Dismiss'
            }
          ],
          vibrate: [200, 100, 200]
        }
      }
    };

    // Send the notification
    await admin.messaging().send(payload);
    console.log(`Push notification sent to user ${request.userId}`);
  } catch (error) {
    console.error('Error sending push notification:', error);
    
    // If token is invalid, mark it as inactive
    if (error.code === 'messaging/registration-token-not-registered') {
      try {
        await admin.firestore()
          .doc(`users/${request.userId}/fcmTokens/web`)
          .update({ active: false });
      } catch (updateError) {
        console.error('Error updating invalid FCM token:', updateError);
      }
    }
  }
}

// Send email notification (placeholder - implement with your email service)
async function sendEmailNotification(request: NotificationRequest): Promise<void> {
  try {
    // Get user email
    const userDoc = await admin.firestore().doc(`users/${request.userId}`).get();
    if (!userDoc.exists()) {
      console.log(`User ${request.userId} not found for email notification`);
      return;
    }

    const userData = userDoc.data();
    const email = userData?.email;
    if (!email) {
      console.log(`No email found for user ${request.userId}`);
      return;
    }

    // TODO: Implement email sending with your preferred service (SendGrid, Mailgun, etc.)
    console.log(`Email notification would be sent to ${email}:`, {
      subject: request.title,
      body: request.message,
      type: request.type
    });

    // For now, just log the email notification
    // In production, integrate with your email service here
  } catch (error) {
    console.error('Error sending email notification:', error);
  }
}

// Main function to send notifications
export const sendNotification = functions.https.onCall(async (data: NotificationRequest, context) => {
  // Verify authentication
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  try {
    // Get user preferences
    const preferences = await getUserPreferences(data.userId);

    // Determine which channels to use
    const channels = data.channels || ['in_app', 'push', 'email'];
    const activeChannels: NotificationChannel[] = [];

    for (const channel of channels) {
      if (shouldSendNotification(data.type, channel, preferences)) {
        activeChannels.push(channel);
      }
    }

    if (activeChannels.length === 0) {
      console.log(`No active channels for notification type ${data.type} for user ${data.userId}`);
      return { success: true, channels: [] };
    }

    // Send notifications through active channels
    const promises: Promise<void>[] = [];

    if (activeChannels.includes('in_app')) {
      promises.push(createInAppNotification(data));
    }

    if (activeChannels.includes('push')) {
      promises.push(sendPushNotification(data));
    }

    if (activeChannels.includes('email')) {
      promises.push(sendEmailNotification(data));
    }

    // Wait for all notifications to be sent
    await Promise.allSettled(promises);

    console.log(`Notifications sent for user ${data.userId} via channels:`, activeChannels);
    return { success: true, channels: activeChannels };
  } catch (error) {
    console.error('Error sending notification:', error);
    throw new functions.https.HttpsError('internal', 'Failed to send notification');
  }
});

// Batch notification function for multiple users
export const sendBatchNotifications = functions.https.onCall(async (data: {
  userIds: string[];
  notification: Omit<NotificationRequest, 'userId'>;
}, context) => {
  // Verify authentication and admin privileges
  if (!context.auth) {
    throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
  }

  // Check if user is admin (you may want to implement proper admin check)
  const userDoc = await admin.firestore().doc(`users/${context.auth.uid}`).get();
  const userData = userDoc.data();
  if (!userData?.isAdmin) {
    throw new functions.https.HttpsError('permission-denied', 'Only admins can send batch notifications');
  }

  try {
    const { userIds, notification } = data;
    const results: { userId: string; success: boolean; error?: string }[] = [];

    // Send notifications to each user
    for (const userId of userIds) {
      try {
        const notificationRequest: NotificationRequest = {
          ...notification,
          userId
        };

        // Get user preferences
        const preferences = await getUserPreferences(userId);

        // Determine active channels
        const channels = notification.channels || ['in_app', 'push', 'email'];
        const activeChannels: NotificationChannel[] = [];

        for (const channel of channels) {
          if (shouldSendNotification(notification.type, channel, preferences)) {
            activeChannels.push(channel);
          }
        }

        if (activeChannels.length > 0) {
          const promises: Promise<void>[] = [];

          if (activeChannels.includes('in_app')) {
            promises.push(createInAppNotification(notificationRequest));
          }

          if (activeChannels.includes('push')) {
            promises.push(sendPushNotification(notificationRequest));
          }

          if (activeChannels.includes('email')) {
            promises.push(sendEmailNotification(notificationRequest));
          }

          await Promise.allSettled(promises);
        }

        results.push({ userId, success: true });
      } catch (error) {
        console.error(`Error sending notification to user ${userId}:`, error);
        results.push({ 
          userId, 
          success: false, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        });
      }
    }

    console.log(`Batch notifications sent to ${userIds.length} users`);
    return { success: true, results };
  } catch (error) {
    console.error('Error sending batch notifications:', error);
    throw new functions.https.HttpsError('internal', 'Failed to send batch notifications');
  }
});

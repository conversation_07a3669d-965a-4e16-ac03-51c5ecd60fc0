import React, { useState, useEffect } from 'react';
import {
  FileText,
  Search,
  Filter,
  Eye,
  Flag,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  User,
  Package,
  MessageSquare,
  Calendar,

  Trash2
} from 'lucide-react';
import {
  collection,
  getDocs,
  query,
  orderBy,
  where,
  doc,
  updateDoc,
  deleteDoc,
  Timestamp
} from 'firebase/firestore';
import { firestore } from '../../../firebase/config';
import { logAdminAction } from '../../../utils/adminAuth';
import { useAuth } from '../../../hooks/useAuth';

interface Report {
  id: string;
  type: 'user' | 'listing' | 'message' | 'other';
  reportedItemId: string;
  reportedUserId?: string;
  reporterUserId: string;
  reporterName?: string;
  reason: string;
  description: string;
  status: 'pending' | 'investigating' | 'resolved' | 'dismissed';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  createdAt: any;
  updatedAt?: any;
  resolvedAt?: any;
  resolvedBy?: string;
  resolution?: string;
  metadata?: {
    reportedUserName?: string;
    listingTitle?: string;
    messageContent?: string;
  };
}

const AdminReports: React.FC = () => {
  const { userProfile } = useAuth();
  const [reports, setReports] = useState<Report[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedStatus, setSelectedStatus] = useState<string>('');
  const [selectedType, setSelectedType] = useState<string>('');
  const [selectedPriority, setSelectedPriority] = useState<string>('');
  const [showFilters, setShowFilters] = useState(false);
  const [_selectedReport, _setSelectedReport] = useState<Report | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Modal states for replacing prompt()
  const [showReasonModal, setShowReasonModal] = useState(false);
  const [modalConfig, setModalConfig] = useState<{
    title: string;
    placeholder: string;
    action: string;
    reportId: string;
  } | null>(null);
  const [reasonInput, setReasonInput] = useState('');

  const reportStatuses = [
    'pending',
    'investigating',
    'resolved',
    'dismissed'
  ];

  const reportTypes = [
    'user',
    'listing',
    'message',
    'other'
  ];

  const reportPriorities = [
    'low',
    'medium',
    'high',
    'urgent'
  ];



  useEffect(() => {
    fetchReports();
  }, [selectedStatus, selectedType, selectedPriority]);

  const fetchReports = async () => {
    try {
      setLoading(true);
      setError(null);

      let reportsQuery = query(
        collection(firestore, 'reports'),
        orderBy('createdAt', 'desc')
      );

      // Apply filters
      if (selectedStatus) {
        reportsQuery = query(reportsQuery, where('status', '==', selectedStatus));
      }
      if (selectedType) {
        reportsQuery = query(reportsQuery, where('type', '==', selectedType));
      }
      if (selectedPriority) {
        reportsQuery = query(reportsQuery, where('priority', '==', selectedPriority));
      }

      const reportsSnapshot = await getDocs(reportsQuery);

      if (reportsSnapshot.empty) {
        // No reports exist, show empty state without error
        setReports([]);
        setError(null);
        return;
      }

      const reportsData: Report[] = reportsSnapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          type: data.type || 'user_report',
          title: data.title || 'Report',
          description: data.description || 'No description available',
          reportedBy: data.reportedBy || 'Anonymous',
          reportedItem: data.reportedItem || 'N/A',
          reportedItemId: data.reportedItemId || '',
          reporterUserId: data.reporterUserId || data.reportedBy || '',
          reason: data.reason || data.description || 'No reason provided',
          status: data.status || 'pending',
          priority: data.priority || 'medium',
          createdAt: data.createdAt || new Date(),
          resolvedAt: data.resolvedAt,
          resolvedBy: data.resolvedBy,
          adminNotes: data.adminNotes || '',
          metadata: data.metadata || {}
        } as Report;
      });

      setReports(reportsData);
      setError(null);
    } catch (err) {
      console.error('Error fetching reports:', err);
      // Don't show error for missing collections, just show empty state
      setReports([]);
      setError(null);
    } finally {
      setLoading(false);
    }
  };

  const handleReportAction = async (reportId: string, action: string, resolution?: string) => {
    try {
      if (userProfile) {
        await logAdminAction(userProfile, `report_${action}`, { reportId, resolution });
      }

      const reportRef = doc(firestore, 'reports', reportId);
      const updateData: any = {
        updatedAt: Timestamp.now()
      };

      switch (action) {
        case 'investigate':
          updateData.status = 'investigating';
          break;
        case 'resolve':
          updateData.status = 'resolved';
          updateData.resolvedAt = Timestamp.now();
          updateData.resolvedBy = userProfile?.uid;
          if (resolution) updateData.resolution = resolution;
          break;
        case 'dismiss':
          updateData.status = 'dismissed';
          updateData.resolvedAt = Timestamp.now();
          updateData.resolvedBy = userProfile?.uid;
          if (resolution) updateData.resolution = resolution;
          break;
        case 'escalate':
          updateData.priority = 'urgent';
          break;
        case 'delete':
          if (confirm('Are you sure you want to delete this report?')) {
            await deleteDoc(reportRef);
            await fetchReports();
            return;
          }
          break;
      }

      await updateDoc(reportRef, updateData);
      await fetchReports();
    } catch (err) {
      console.error(`Error performing ${action} on report:`, err);
    }
  };

  const handleModalSubmit = async () => {
    if (!modalConfig || !reasonInput.trim()) return;

    try {
      await handleReportAction(modalConfig.reportId, modalConfig.action, reasonInput.trim());

      // Close modal
      setShowReasonModal(false);
      setModalConfig(null);
      setReasonInput('');
    } catch (err) {
      console.error(`Error performing ${modalConfig.action} on report:`, err);
    }
  };

  const handleModalCancel = () => {
    setShowReasonModal(false);
    setModalConfig(null);
    setReasonInput('');
  };

  const filteredReports = reports.filter(report =>
    report.reason.toLowerCase().includes(searchTerm.toLowerCase()) ||
    report.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
    report.reporterName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    report.metadata?.reportedUserName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    report.metadata?.listingTitle?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const formatDate = (timestamp: any) => {
    if (!timestamp) return 'N/A';
    const date = timestamp.toDate ? timestamp.toDate() : new Date(timestamp);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300';
      case 'investigating': return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300';
      case 'resolved': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
      case 'dismissed': return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low': return 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300';
      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300';
      case 'high': return 'bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-300';
      case 'urgent': return 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-300';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'user': return <User className="h-4 w-4" />;
      case 'listing': return <Package className="h-4 w-4" />;
      case 'message': return <MessageSquare className="h-4 w-4" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600 dark:text-gray-400">Loading reports...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="space-y-6">
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex">
            <AlertTriangle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                Error Loading Reports
              </h3>
              <p className="mt-1 text-sm text-red-700 dark:text-red-300">{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Reports & Moderation</h2>
          <p className="text-gray-600 dark:text-gray-400">
            Review and moderate user reports ({reports.length} reports)
          </p>
        </div>
        <div className="flex space-x-3">
          <button className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600">
            <Flag className="h-4 w-4 mr-2" />
            Pending ({reports.filter(r => r.status === 'pending').length})
          </button>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white dark:bg-gray-800 shadow-sm rounded-lg border border-gray-200 dark:border-gray-700 p-4">
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search reports by reason, description, or user..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="block w-full pl-10 pr-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md leading-5 bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              />
            </div>
          </div>
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
          >
            <Filter className="h-4 w-4 mr-2" />
            Filters
          </button>
        </div>

        {/* Filter Options */}
        {showFilters && (
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Status
                </label>
                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                >
                  <option value="">All Statuses</option>
                  {reportStatuses.map(status => (
                    <option key={status} value={status}>
                      {status.charAt(0).toUpperCase() + status.slice(1)}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Type
                </label>
                <select
                  value={selectedType}
                  onChange={(e) => setSelectedType(e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                >
                  <option value="">All Types</option>
                  {reportTypes.map(type => (
                    <option key={type} value={type}>
                      {type.charAt(0).toUpperCase() + type.slice(1)}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Priority
                </label>
                <select
                  value={selectedPriority}
                  onChange={(e) => setSelectedPriority(e.target.value)}
                  className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                >
                  <option value="">All Priorities</option>
                  {reportPriorities.map(priority => (
                    <option key={priority} value={priority}>
                      {priority.charAt(0).toUpperCase() + priority.slice(1)}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Reports Table */}
      <div className="bg-white dark:bg-gray-800 shadow-sm rounded-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
        {filteredReports.length === 0 ? (
          <div className="px-4 py-12">
            <div className="text-center">
              <Flag className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No reports found</h3>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                {searchTerm ? 'Try adjusting your search terms.' : 'No reports match the current filters.'}
              </p>
            </div>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-700">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Report
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Reporter
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Reported Item
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Reason
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Date
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {filteredReports.map((report) => (
                  <tr key={report.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div className="flex-shrink-0">
                          {getTypeIcon(report.type)}
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {report.id.substring(0, 8)}...
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(report.status)}`}>
                              {report.status === 'pending' && <Clock className="h-3 w-3 mr-1" />}
                              {report.status === 'investigating' && <Eye className="h-3 w-3 mr-1" />}
                              {report.status === 'resolved' && <CheckCircle className="h-3 w-3 mr-1" />}
                              {report.status === 'dismissed' && <XCircle className="h-3 w-3 mr-1" />}
                              {report.status}
                            </span>
                            <span className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${getPriorityColor(report.priority)}`}>
                              {report.priority === 'urgent' && <AlertTriangle className="h-3 w-3 mr-1" />}
                              {report.priority}
                            </span>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center text-sm text-gray-900 dark:text-white">
                        <User className="h-4 w-4 mr-2 text-gray-400" />
                        {report.reporterName || 'Anonymous'}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900 dark:text-white">
                        <div className="flex items-center">
                          {getTypeIcon(report.type)}
                          <span className="ml-2">
                            {report.type === 'user' && (report.metadata?.reportedUserName || 'User')}
                            {report.type === 'listing' && (report.metadata?.listingTitle || 'Listing')}
                            {report.type === 'message' && 'Message'}
                            {report.type === 'other' && 'Other'}
                          </span>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900 dark:text-white">
                        <div className="font-medium">{report.reason}</div>
                        <div className="text-gray-500 dark:text-gray-400 truncate max-w-xs">
                          {report.description}
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                        <Calendar className="h-4 w-4 mr-2" />
                        {formatDate(report.createdAt)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <div className="flex items-center justify-end space-x-2">
                        {report.status === 'pending' && (
                          <>
                            <button
                              onClick={() => handleReportAction(report.id, 'investigate')}
                              className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                              title="Start Investigation"
                            >
                              <Eye className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => handleReportAction(report.id, 'escalate')}
                              className="text-orange-600 hover:text-orange-900 dark:text-orange-400 dark:hover:text-orange-300"
                              title="Escalate Priority"
                            >
                              <AlertTriangle className="h-4 w-4" />
                            </button>
                          </>
                        )}
                        {(report.status === 'pending' || report.status === 'investigating') && (
                          <>
                            <button
                              onClick={() => {
                                setModalConfig({
                                  title: 'Resolve Report',
                                  placeholder: 'Enter resolution notes...',
                                  action: 'resolve',
                                  reportId: report.id
                                });
                                setShowReasonModal(true);
                              }}
                              className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300"
                              title="Resolve Report"
                            >
                              <CheckCircle className="h-4 w-4" />
                            </button>
                            <button
                              onClick={() => {
                                setModalConfig({
                                  title: 'Dismiss Report',
                                  placeholder: 'Enter dismissal reason...',
                                  action: 'dismiss',
                                  reportId: report.id
                                });
                                setShowReasonModal(true);
                              }}
                              className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300"
                              title="Dismiss Report"
                            >
                              <XCircle className="h-4 w-4" />
                            </button>
                          </>
                        )}
                        <button
                          onClick={() => handleReportAction(report.id, 'delete')}
                          className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                          title="Delete Report"
                        >
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Reason Input Modal */}
      {showReasonModal && modalConfig && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md mx-4">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              {modalConfig.title}
            </h3>

            <div className="mb-4">
              <label htmlFor="report-reason-input" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                {modalConfig.action === 'resolve' ? 'Resolution Notes' : 'Reason'}
              </label>
              <textarea
                id="report-reason-input"
                name="reason"
                value={reasonInput}
                onChange={(e) => setReasonInput(e.target.value)}
                placeholder={modalConfig.placeholder}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white resize-none"
                rows={3}
                required
              />
            </div>

            <div className="flex justify-end space-x-3">
              <button
                type="button"
                onClick={handleModalCancel}
                className="px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleModalSubmit}
                disabled={!reasonInput.trim()}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Submit
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AdminReports;

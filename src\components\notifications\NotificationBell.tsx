import React, { useState, useRef, useEffect } from 'react';
import { Bell, BellRing } from 'lucide-react';
import { useUserNotifications } from '../../hooks/useUserNotifications';
import { useFCM } from '../../hooks/useFCMTokenManager';
import { NotificationsDropdown } from './NotificationsDropdown';

interface NotificationBellProps {
  className?: string;
  showDropdown?: boolean;
  maxDropdownItems?: number;
}

export const NotificationBell: React.FC<NotificationBellProps> = ({
  className = '',
  showDropdown = true,
  maxDropdownItems = 5
}) => {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [hasNewNotifications, setHasNewNotifications] = useState(false);
  const bellRef = useRef<HTMLButtonElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  const { unreadCount, notifications, markAsRead } = useUserNotifications();
  const { onForegroundMessage, isSupported, permission, requestPermission } = useFCM();

  // Handle foreground messages and auto-request permission
  useEffect(() => {
    // Auto-request permission if supported and not already granted/denied
    if (isSupported && permission === 'default') {
      // Small delay to avoid overwhelming the user immediately
      const timer = setTimeout(() => {
        requestPermission().catch(console.error);
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [isSupported, permission, requestPermission]);

  // Handle foreground messages
  useEffect(() => {
    const unsubscribe = onForegroundMessage?.((payload) => {
      console.log('FCM: Foreground message received:', payload);

      // Show visual indicator for new notification
      setHasNewNotifications(true);

      // Auto-hide the indicator after 3 seconds
      setTimeout(() => {
        setHasNewNotifications(false);
      }, 3000);

      // The FCM hook already handles showing toast notifications
      // No need to show additional browser notifications here
    });

    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [onForegroundMessage]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node) &&
        bellRef.current &&
        !bellRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    };

    if (isDropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isDropdownOpen]);

  // Handle bell click
  const handleBellClick = () => {
    if (showDropdown) {
      setIsDropdownOpen(!isDropdownOpen);
      setHasNewNotifications(false);
    }
  };

  // Handle notification click
  const handleNotificationClick = async (notificationId: string, link?: string) => {
    try {
      await markAsRead(notificationId);
      
      if (link) {
        // Navigate to the link
        window.location.href = link;
      }
      
      setIsDropdownOpen(false);
    } catch (error) {
      console.error('Error marking notification as read:', error);
    }
  };

  // Determine bell icon and styling
  const hasNotifications = unreadCount > 0 || hasNewNotifications;
  const BellIcon = hasNotifications ? BellRing : Bell;

  return (
    <div className={`relative ${className}`}>
      {/* Bell Button */}
      <button
        ref={bellRef}
        onClick={handleBellClick}
        className={`
          relative p-2 rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2
          ${hasNotifications 
            ? 'text-primary-600 hover:text-primary-700 hover:bg-primary-50 dark:text-primary-400 dark:hover:text-primary-300 dark:hover:bg-primary-900/20' 
            : 'text-gray-400 hover:text-gray-500 hover:bg-gray-50 dark:text-gray-500 dark:hover:text-gray-400 dark:hover:bg-gray-800'
          }
          ${hasNewNotifications ? 'animate-pulse' : ''}
        `}
        title={`${unreadCount} unread notifications`}
        aria-label={`Notifications. ${unreadCount} unread.`}
      >
        <BellIcon 
          className={`h-6 w-6 transition-transform duration-200 ${hasNewNotifications ? 'animate-bounce' : ''}`} 
        />
        
        {/* Notification Badge */}
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 flex h-5 w-5 items-center justify-center rounded-full bg-red-500 text-xs font-medium text-white ring-2 ring-white dark:ring-gray-900">
            {unreadCount > 99 ? '99+' : unreadCount}
          </span>
        )}

        {/* New notification indicator */}
        {hasNewNotifications && unreadCount === 0 && (
          <span className="absolute -top-1 -right-1 flex h-3 w-3 rounded-full bg-primary-500 ring-2 ring-white dark:ring-gray-900">
            <span className="absolute inline-flex h-full w-full animate-ping rounded-full bg-primary-400 opacity-75"></span>
          </span>
        )}
      </button>

      {/* Dropdown */}
      {showDropdown && isDropdownOpen && (
        <div ref={dropdownRef}>
          <NotificationsDropdown
            notifications={notifications.slice(0, maxDropdownItems)}
            onNotificationClick={handleNotificationClick}
            onClose={() => setIsDropdownOpen(false)}
            className="absolute right-0 top-full mt-2 z-50"
          />
        </div>
      )}
    </div>
  );
};

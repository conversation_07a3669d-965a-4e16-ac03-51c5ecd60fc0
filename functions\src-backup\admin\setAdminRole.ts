import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';

// Function to create admin account with any email domain
export const createAdminAccount = functions.https.onCall(async (data, _context) => {
  try {
    const { email, password, name } = data;

    if (!email || !password || !name) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Email, password, and name are required'
      );
    }

    // Create user account directly in Firebase Auth (bypassing email validation)
    const userRecord = await admin.auth().createUser({
      email,
      password,
      displayName: name,
      emailVerified: true // Auto-verify admin email
    });

    // Set admin custom claims
    await admin.auth().setCustomUserClaims(userRecord.uid, {
      admin: true,
      role: 'admin'
    });

    // Create user document in Firestore with admin role
    await admin.firestore().collection('users').doc(userRecord.uid).set({
      uid: userRecord.uid,
      name,
      email,
      role: 'admin',
      university: 'Hive Campus Admin',
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now(),
      emailVerified: true,
      status: 'active',
      adminLevel: 'super',
      permissions: ['user_management', 'content_moderation', 'analytics', 'system_settings', 'super_admin']
    });

    console.log(`Admin account created for: ${email}`);

    return {
      success: true,
      message: `Admin account created for ${email}`,
      uid: userRecord.uid
    };

  } catch (error) {
    console.error('Error creating admin account:', error);
    throw new functions.https.HttpsError(
      'internal',
      'Failed to create admin account',
      error
    );
  }
});

// Function to set admin role for a user
export const setAdminRole = functions.https.onCall(async (data, _context) => {
  try {
    const { email } = data;

    if (!email) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Email is required'
      );
    }

    // Get user by email
    const userRecord = await admin.auth().getUserByEmail(email);

    // Set custom claims
    await admin.auth().setCustomUserClaims(userRecord.uid, {
      admin: true,
      role: 'admin'
    });

    // Update user profile in Firestore
    await admin.firestore().collection('users').doc(userRecord.uid).update({
      role: 'admin',
      updatedAt: admin.firestore.Timestamp.now(),
      status: 'active',
      adminLevel: 'super',
      permissions: ['user_management', 'content_moderation', 'analytics', 'system_settings', 'super_admin']
    });

    console.log(`Admin role set for user: ${email}`);

    return {
      success: true,
      message: `Admin role set for ${email}`,
      uid: userRecord.uid
    };

  } catch (error) {
    console.error('Error setting admin role:', error);
    throw new functions.https.HttpsError(
      'internal',
      'Failed to set admin role',
      error
    );
  }
});



// Function to remove admin role from a user
export const removeAdminRole = functions.https.onCall(async (data, context) => {
  try {
    // Only existing admins can remove admin roles
    if (!context.auth || !context.auth.token.admin) {
      throw new functions.https.HttpsError(
        'permission-denied',
        'Only admins can remove admin roles'
      );
    }

    const { email } = data;
    
    if (!email) {
      throw new functions.https.HttpsError(
        'invalid-argument',
        'Email is required'
      );
    }

    // Get user by email
    const userRecord = await admin.auth().getUserByEmail(email);
    
    // Remove custom claims
    await admin.auth().setCustomUserClaims(userRecord.uid, { admin: false });
    
    // Update user profile in Firestore
    await admin.firestore().collection('users').doc(userRecord.uid).update({
      role: 'student',
      updatedAt: admin.firestore.Timestamp.now()
    });

    console.log(`Admin role removed for user: ${email}`);
    
    return {
      success: true,
      message: `Admin role removed for ${email}`,
      uid: userRecord.uid
    };

  } catch (error) {
    console.error('Error removing admin role:', error);
    throw new functions.https.HttpsError(
      'internal',
      'Failed to remove admin role',
      error
    );
  }
});

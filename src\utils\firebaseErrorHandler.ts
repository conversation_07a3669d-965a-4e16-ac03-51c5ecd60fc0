/**
 * Firebase Error Handler and Connection Manager
 * Handles common Firebase connection issues and async listener errors
 */

import { FirebaseError } from 'firebase/app';

export interface ConnectionState {
  isOnline: boolean;
  lastConnected: Date | null;
  retryCount: number;
  maxRetries: number;
}

class FirebaseErrorHandler {
  private connectionState: ConnectionState = {
    isOnline: navigator.onLine,
    lastConnected: null,
    retryCount: 0,
    maxRetries: 3
  };

  private listeners: Set<() => void> = new Set();
  private retryTimeouts: Map<string, NodeJS.Timeout> = new Map();

  constructor() {
    this.setupNetworkListeners();
    this.setupUnloadHandler();
  }

  /**
   * Setup network connectivity listeners
   */
  private setupNetworkListeners() {
    window.addEventListener('online', () => {
      this.connectionState.isOnline = true;
      this.connectionState.lastConnected = new Date();
      this.connectionState.retryCount = 0;
      console.log('🟢 Network connection restored');
    });

    window.addEventListener('offline', () => {
      this.connectionState.isOnline = false;
      console.log('🔴 Network connection lost');
    });
  }

  /**
   * Setup page unload handler to clean up listeners
   */
  private setupUnloadHandler() {
    window.addEventListener('beforeunload', () => {
      this.cleanupAllListeners();
    });

    // Handle visibility change to pause/resume listeners
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        console.log('📱 Page hidden - pausing Firebase listeners');
      } else {
        console.log('📱 Page visible - resuming Firebase listeners');
      }
    });
  }

  /**
   * Wrap Firebase listeners to handle connection issues
   */
  wrapListener<T>(
    listenerFn: () => () => void,
    errorHandler?: (error: Error) => void,
    retryKey?: string
  ): () => void {
    let unsubscribe: (() => void) | null = null;
    let isActive = true;

    const setupListener = () => {
      if (!isActive || !this.connectionState.isOnline) {
        return;
      }

      try {
        unsubscribe = listenerFn();
        this.listeners.add(() => {
          if (unsubscribe) {
            unsubscribe();
            unsubscribe = null;
          }
        });
      } catch (error) {
        this.handleListenerError(error as Error, errorHandler, retryKey);
      }
    };

    // Initial setup
    setupListener();

    // Return cleanup function
    return () => {
      isActive = false;
      if (unsubscribe) {
        try {
          unsubscribe();
        } catch (error) {
          console.warn('Error cleaning up listener:', error);
        }
        unsubscribe = null;
      }
      
      if (retryKey && this.retryTimeouts.has(retryKey)) {
        clearTimeout(this.retryTimeouts.get(retryKey)!);
        this.retryTimeouts.delete(retryKey);
      }
    };
  }

  /**
   * Handle listener errors with retry logic
   */
  private handleListenerError(
    error: Error,
    errorHandler?: (error: Error) => void,
    retryKey?: string
  ) {
    console.error('Firebase listener error:', error);

    // Check if it's a network-related error
    if (this.isNetworkError(error)) {
      if (retryKey && this.connectionState.retryCount < this.connectionState.maxRetries) {
        this.scheduleRetry(retryKey, () => {
          this.connectionState.retryCount++;
          console.log(`🔄 Retrying Firebase listener (${this.connectionState.retryCount}/${this.connectionState.maxRetries})`);
        });
      }
    }

    // Call custom error handler
    if (errorHandler) {
      errorHandler(error);
    }
  }

  /**
   * Check if error is network-related
   */
  private isNetworkError(error: Error): boolean {
    const message = error.message.toLowerCase();
    const networkKeywords = [
      'network',
      'connection',
      'timeout',
      'unavailable',
      'blocked',
      'failed to fetch',
      'err_blocked_by_client',
      'err_network_changed'
    ];

    return networkKeywords.some(keyword => message.includes(keyword));
  }

  /**
   * Schedule retry with exponential backoff
   */
  private scheduleRetry(key: string, retryFn: () => void) {
    if (this.retryTimeouts.has(key)) {
      clearTimeout(this.retryTimeouts.get(key)!);
    }

    const delay = Math.min(1000 * Math.pow(2, this.connectionState.retryCount), 30000);
    
    const timeout = setTimeout(() => {
      if (this.connectionState.isOnline) {
        retryFn();
      }
      this.retryTimeouts.delete(key);
    }, delay);

    this.retryTimeouts.set(key, timeout);
  }

  /**
   * Clean up all listeners and timeouts
   */
  private cleanupAllListeners() {
    this.listeners.forEach(cleanup => {
      try {
        cleanup();
      } catch (error) {
        console.warn('Error during listener cleanup:', error);
      }
    });
    this.listeners.clear();

    this.retryTimeouts.forEach(timeout => clearTimeout(timeout));
    this.retryTimeouts.clear();
  }

  /**
   * Handle Firebase errors with user-friendly messages
   */
  handleFirebaseError(error: unknown): string {
    if (error instanceof FirebaseError) {
      switch (error.code) {
        case 'permission-denied':
          return 'Access denied. Please check your permissions.';
        case 'unavailable':
          return 'Service temporarily unavailable. Please try again.';
        case 'deadline-exceeded':
          return 'Request timed out. Please check your connection.';
        case 'resource-exhausted':
          return 'Service is busy. Please try again in a moment.';
        default:
          return `Service error: ${error.message}`;
      }
    }

    if (error instanceof Error) {
      if (this.isNetworkError(error)) {
        return 'Connection issue. Please check your internet connection.';
      }
      return error.message;
    }

    return 'An unexpected error occurred. Please try again.';
  }

  /**
   * Get current connection state
   */
  getConnectionState(): ConnectionState {
    return { ...this.connectionState };
  }
}

// Export singleton instance
export const firebaseErrorHandler = new FirebaseErrorHandler();

/**
 * Utility function to wrap async operations with error handling
 */
export async function withFirebaseErrorHandling<T>(
  operation: () => Promise<T>,
  fallback?: T,
  retryOptions?: {
    maxRetries?: number;
    baseDelay?: number;
    shouldRetry?: (error: Error) => boolean;
  }
): Promise<T> {
  const { maxRetries = 3, baseDelay = 1000, shouldRetry } = retryOptions || {};
  let lastError: Error;

  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;
      console.error(`Firebase operation failed (attempt ${attempt + 1}/${maxRetries}):`, error);

      // Check if this is a blocked by client error
      const errorMessage = lastError.message.toLowerCase();
      if (errorMessage.includes('err_blocked_by_client') ||
          errorMessage.includes('blocked by client') ||
          errorMessage.includes('failed to fetch')) {

        // Don't retry blocked requests, but provide helpful error
        const helpfulError = new Error(
          'Connection blocked by browser or extension. Please:\n' +
          '• Disable ad blockers for this site\n' +
          '• Try incognito/private browsing mode\n' +
          '• Check browser extensions that might block requests\n' +
          '• Ensure firestore.googleapis.com is not blocked'
        );

        if (fallback !== undefined) {
          console.warn('Using fallback value due to blocked connection');
          return fallback;
        }

        throw helpfulError;
      }

      // Check if we should retry this error
      const isRetryable = shouldRetry ? shouldRetry(lastError) :
        (errorMessage.includes('unavailable') ||
         errorMessage.includes('timeout') ||
         errorMessage.includes('deadline-exceeded') ||
         errorMessage.includes('internal'));

      // If this is the last attempt or error is not retryable, throw
      if (attempt === maxRetries - 1 || !isRetryable) {
        if (fallback !== undefined) {
          console.warn('Using fallback value due to persistent error');
          return fallback;
        }
        throw new Error(firebaseErrorHandler.handleFirebaseError(lastError));
      }

      // Wait before retrying with exponential backoff
      const delay = Math.min(baseDelay * Math.pow(2, attempt), 10000);
      console.log(`Retrying in ${delay}ms...`);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }

  throw new Error(firebaseErrorHandler.handleFirebaseError(lastError!));
}

/**
 * Debounce function to prevent rapid successive calls
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  wait: number
): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeout);
    timeout = setTimeout(() => func(...args), wait);
  };
}

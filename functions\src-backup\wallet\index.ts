import * as functions from 'firebase-functions';
import * as admin from 'firebase-admin';
import { verifyAuth, handleError } from '../utils/helpers';
import { WalletTransaction, ReferralCode, WalletAnalytics } from '../utils/types';
import { db } from '../config/firebase';

// CORS configuration
// eslint-disable-next-line @typescript-eslint/no-require-imports
const cors = require('cors')({
  origin: [
    'https://h1c1-798a8.web.app',
    'https://h1c1-798a8.firebaseapp.com',
    'https://hivecampus.app',
    'https://www.hivecampus.app',
    'http://localhost:5173',
    'http://localhost:3000',
    'http://localhost:5000'
  ],
  credentials: true
});

/**
 * HTTP endpoint version of getWalletData with explicit CORS handling
 */
export const getWalletDataHttp = functions.https.onRequest(async (req, res) => {
  // Handle CORS
  cors(req, res, async () => {
    try {
      // Only allow POST requests
      if (req.method !== 'POST') {
        res.status(405).json({ error: 'Method not allowed' });
        return;
      }

      // Get the authorization token
      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const token = authHeader.split('Bearer ')[1];
      const decodedToken = await admin.auth().verifyIdToken(token);
      const userId = decodedToken.uid;

      const walletDoc = await db.collection('wallets').doc(userId).get();

      if (!walletDoc.exists) {
        // Initialize wallet if it doesn't exist
        const walletData = {
          userId,
          balance: 0,
          referralCode: `user${userId.substring(0, 6)}`,
          usedReferral: false,
          history: [],
          grantedBy: 'system',
          createdAt: admin.firestore.Timestamp.now(),
          lastUpdated: admin.firestore.Timestamp.now()
        };

        await db.collection('wallets').doc(userId).set(walletData);
        res.status(200).json(walletData);
        return;
      }

      res.status(200).json(walletDoc.data());
    } catch (error) {
      console.error('Error getting wallet data:', error);
      res.status(500).json({ error: error instanceof Error ? error.message : 'Unknown error' });
    }
  });
});

/**
 * HTTP endpoint version of getWalletSettings with explicit CORS handling
 */
export const getWalletSettingsHttp = functions.https.onRequest(async (req, res) => {
  // Handle CORS
  cors(req, res, async () => {
    try {
      // Only allow POST requests
      if (req.method !== 'POST') {
        res.status(405).json({ error: 'Method not allowed' });
        return;
      }

      // Get the authorization token
      const authHeader = req.headers.authorization;
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        res.status(401).json({ error: 'Unauthorized' });
        return;
      }

      const token = authHeader.split('Bearer ')[1];
      const decodedToken = await admin.auth().verifyIdToken(token);
      const userId = decodedToken.uid;

      // Verify admin role
      const adminDoc = await db.collection('users').doc(userId).get();
      if (!adminDoc.exists || adminDoc.data()?.role !== 'admin') {
        res.status(403).json({ error: 'Unauthorized: Admin access required' });
        return;
      }

      const settings = await getWalletConfig();
      res.status(200).json({
        success: true,
        settings
      });
    } catch (error) {
      console.error('Error getting wallet settings:', error);
      res.status(500).json({ error: error instanceof Error ? error.message : 'Unknown error' });
    }
  });
});

// Helper function to calculate expiration date
function calculateExpirationDate(days: number = 30): admin.firestore.Timestamp {
  const expirationDate = new Date();
  expirationDate.setDate(expirationDate.getDate() + days);
  return admin.firestore.Timestamp.fromDate(expirationDate);
}

// Helper function to track ReeFlex events
async function trackWalletEvent(eventType: string, userId: string, data: any) {
  try {
    await db.collection('reeflex_events').add({
      event: `wallet_${eventType}`,
      userId,
      data,
      timestamp: admin.firestore.Timestamp.now(),
      source: 'wallet_system'
    });
  } catch (error) {
    console.error('Error tracking ReeFlex event:', error);
  }
}

// Helper function to update wallet analytics
async function updateWalletAnalytics(userId: string, action: string, amount?: number) {
  try {
    const analyticsRef = db.collection('walletAnalytics').doc(userId);
    const analyticsDoc = await analyticsRef.get();

    let analytics: WalletAnalytics;
    if (analyticsDoc.exists) {
      analytics = analyticsDoc.data() as WalletAnalytics;
    } else {
      analytics = {
        userId,
        totalCreditsEarned: 0,
        totalCreditsUsed: 0,
        referralCount: 0,
        lastActivity: admin.firestore.Timestamp.now(),
        suspiciousActivity: false,
        activityScore: 50
      };
    }

    // Update based on action
    switch (action) {
      case 'credit_earned':
        analytics.totalCreditsEarned += amount || 0;
        break;
      case 'credit_used':
        analytics.totalCreditsUsed += amount || 0;
        break;
      case 'referral_made':
        analytics.referralCount += 1;
        break;
    }

    // Update activity score and check for suspicious patterns
    analytics.lastActivity = admin.firestore.Timestamp.now();
    analytics.activityScore = calculateActivityScore(analytics);
    analytics.suspiciousActivity = detectSuspiciousActivity(analytics);

    await analyticsRef.set(analytics);
  } catch (error) {
    console.error('Error updating wallet analytics:', error);
  }
}

// Calculate activity score based on usage patterns
function calculateActivityScore(analytics: WalletAnalytics): number {
  let score = 50; // Base score

  // Positive factors
  if (analytics.referralCount > 0) score += Math.min(analytics.referralCount * 5, 20);
  if (analytics.totalCreditsUsed > 0) score += 10;

  // Negative factors for suspicious patterns
  if (analytics.totalCreditsEarned > analytics.totalCreditsUsed * 10) score -= 20;
  if (analytics.referralCount > 10) score -= 10; // Too many referrals might be suspicious

  return Math.max(0, Math.min(100, score));
}

// Detect suspicious activity patterns
function detectSuspiciousActivity(analytics: WalletAnalytics): boolean {
  // Flag as suspicious if:
  // 1. Too many referrals in short time
  // 2. High credit earning but no usage
  // 3. Unusual patterns

  if (analytics.referralCount > 20) return true;
  if (analytics.totalCreditsEarned > 100 && analytics.totalCreditsUsed === 0) return true;
  if (analytics.activityScore < 20) return true;

  return false;
}

// Get wallet settings from admin configuration
async function getWalletConfig() {
  try {
    const settingsDoc = await db.collection('adminSettings').doc('walletConfig').get();
    if (settingsDoc.exists) {
      const settings = settingsDoc.data();
      return {
        signupBonus: settings?.signupBonus || 0,
        referralBonus: settings?.referralBonus || 0,
        enableSignupBonus: settings?.enableSignupBonus || false,
        enableReferralBonus: settings?.enableReferralBonus || false
      };
    }
    return {
      signupBonus: 0,
      referralBonus: 0,
      enableSignupBonus: false,
      enableReferralBonus: false
    };
  } catch (error) {
    console.error('Error getting wallet settings:', error);
    return {
      signupBonus: 0,
      referralBonus: 0,
      enableSignupBonus: false,
      enableReferralBonus: false
    };
  }
}

/**
 * Initialize wallet for new user with signup bonus and referral code
 */
export const initializeWallet = functions.auth.user().onCreate(async (user) => {
  try {
    const { uid, email, displayName } = user;
    
    if (!email) {
      throw new Error('User email is required');
    }

    // Get wallet settings
    const settings = await getWalletConfig();

    // Generate unique referral code
    const username = displayName?.replace(/\s+/g, '').toLowerCase() || email.split('@')[0];
    const randomSuffix = Math.random().toString(36).substring(2, 5).toUpperCase();
    const referralCode = `${username}${randomSuffix}`;

    // Create wallet with configurable signup bonus
    const signupBonus = settings.enableSignupBonus ? settings.signupBonus : 0;
    const history: WalletTransaction[] = [];

    if (signupBonus > 0) {
      const signupTransaction: WalletTransaction = {
        id: admin.firestore().collection('temp').doc().id,
        userId: uid,
        type: 'signup_bonus',
        amount: signupBonus,
        description: 'Welcome bonus for joining Hive Campus!',
        timestamp: admin.firestore.Timestamp.now(),
        expiresAt: calculateExpirationDate(30), // 30 days expiration
        source: 'signup_bonus',
        createdAt: admin.firestore.Timestamp.now()
      };
      history.push(signupTransaction);

      // Track ReeFlex event
      await trackWalletEvent('signup_bonus_granted', uid, {
        amount: signupBonus,
        expiresAt: signupTransaction.expiresAt
      });

      // Update analytics
      await updateWalletAnalytics(uid, 'credit_earned', signupBonus);
    }

    const walletData = {
      userId: uid,
      balance: signupBonus,
      referralCode,
      usedReferral: false,
      history,
      grantedBy: signupBonus > 0 ? 'signup' : 'system',
      createdAt: admin.firestore.Timestamp.now(),
      lastUpdated: admin.firestore.Timestamp.now()
    };

    // Create referral code document
    const referralCodeData: ReferralCode = {
      code: referralCode,
      userId: uid,
      usedBy: [],
      totalRewards: 0,
      isActive: true,
      createdAt: admin.firestore.Timestamp.now()
    };

    // Use batch write for atomicity
    const batch = db.batch();
    batch.set(db.collection('wallets').doc(uid), walletData);
    batch.set(db.collection('referralCodes').doc(referralCode), referralCodeData);
    
    await batch.commit();

    console.log(`Wallet initialized for user ${uid} with referral code ${referralCode}`);
    return { success: true, referralCode };
  } catch (error) {
    console.error('Error initializing wallet:', error);
    return handleError(error);
  }
});

/**
 * Process referral code during signup
 */
export const processReferralCode = functions.https.onCall(async (data, context) => {
  try {
    verifyAuth(context);

    const { referralCode } = data;
    const userId = context.auth!.uid;

    if (!referralCode) {
      throw new Error('Referral code is required');
    }

    // Wait for wallet to be initialized and retry if needed
    let userWalletDoc = await db.collection('wallets').doc(userId).get();
    let retryCount = 0;
    const maxRetries = 5;

    while (!userWalletDoc.exists && retryCount < maxRetries) {
      console.log(`Wallet not found for user ${userId}, retrying... (${retryCount + 1}/${maxRetries})`);
      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
      userWalletDoc = await db.collection('wallets').doc(userId).get();
      retryCount++;
    }

    if (!userWalletDoc.exists) {
      throw new Error('User wallet not found after retries. Please try again in a moment.');
    }

    const userWallet = userWalletDoc.data();
    if (userWallet?.usedReferral) {
      throw new Error('You have already used a referral code');
    }

    // Validate referral code
    const referralDoc = await db.collection('referralCodes').doc(referralCode).get();
    if (!referralDoc.exists) {
      throw new Error('Invalid referral code');
    }

    const referralData = referralDoc.data() as ReferralCode;
    if (!referralData.isActive) {
      throw new Error('This referral code is no longer active');
    }

    if (referralData.userId === userId) {
      throw new Error('You cannot use your own referral code');
    }

    // Check if user already used this code
    if (referralData.usedBy.includes(userId)) {
      throw new Error('You have already used this referral code');
    }

    // Get wallet settings for referral bonus
    const settings = await getWalletConfig();

    if (!settings.enableReferralBonus || settings.referralBonus <= 0) {
      throw new Error('Referral bonuses are currently disabled');
    }

    // Process referral rewards
    const referralBonus = settings.referralBonus;
    const batch = db.batch();

    // Add bonus to new user
    const newUserTransaction: WalletTransaction = {
      id: db.collection('temp').doc().id,
      userId: userId,
      type: 'referral_bonus',
      amount: referralBonus,
      description: `Referral bonus from ${referralCode}`,
      timestamp: admin.firestore.Timestamp.now(),
      relatedUserId: referralData.userId,
      expiresAt: calculateExpirationDate(30), // 30 days expiration
      source: 'referral_bonus',
      referralUserId: referralData.userId,
      createdAt: admin.firestore.Timestamp.now()
    };

    batch.update(db.collection('wallets').doc(userId), {
      balance: admin.firestore.FieldValue.increment(referralBonus),
      usedReferral: true,
      history: admin.firestore.FieldValue.arrayUnion(newUserTransaction),
      lastUpdated: admin.firestore.Timestamp.now()
    });

    // Add bonus to referrer
    const referrerTransaction: WalletTransaction = {
      id: db.collection('temp').doc().id,
      userId: referralData.userId,
      type: 'referral_bonus',
      amount: referralBonus,
      description: `Referral reward for inviting new user`,
      timestamp: admin.firestore.Timestamp.now(),
      relatedUserId: userId,
      expiresAt: calculateExpirationDate(30), // 30 days expiration
      source: 'referral_bonus',
      referralUserId: userId,
      createdAt: admin.firestore.Timestamp.now()
    };

    batch.update(db.collection('wallets').doc(referralData.userId), {
      balance: admin.firestore.FieldValue.increment(referralBonus),
      history: admin.firestore.FieldValue.arrayUnion(referrerTransaction),
      lastUpdated: admin.firestore.Timestamp.now()
    });

    // Update referral code usage
    batch.update(db.collection('referralCodes').doc(referralCode), {
      usedBy: admin.firestore.FieldValue.arrayUnion(userId),
      totalRewards: admin.firestore.FieldValue.increment(referralBonus),
      updatedAt: admin.firestore.Timestamp.now()
    });

    await batch.commit();

    // Track ReeFlex events
    await trackWalletEvent('referral_bonus_granted', userId, {
      amount: referralBonus,
      referrerId: referralData.userId,
      referralCode
    });

    await trackWalletEvent('referral_bonus_earned', referralData.userId, {
      amount: referralBonus,
      newUserId: userId,
      referralCode
    });

    // Update analytics
    await updateWalletAnalytics(userId, 'credit_earned', referralBonus);
    await updateWalletAnalytics(referralData.userId, 'credit_earned', referralBonus);
    await updateWalletAnalytics(referralData.userId, 'referral_made');

    return {
      success: true,
      message: `Referral bonus of $${referralBonus} added to both accounts!`,
      bonusAmount: referralBonus
    };
  } catch (error) {
    console.error('Error processing referral code:', error);
    throw new functions.https.HttpsError('internal', error instanceof Error ? error.message : 'Unknown error');
  }
});

/**
 * Admin function to grant wallet credit
 */
export const grantWalletCredit = functions.https.onCall(async (data, context) => {
  try {
    verifyAuth(context);
    
    // Verify admin role
    const adminDoc = await db.collection('users').doc(context.auth!.uid).get();
    if (!adminDoc.exists || adminDoc.data()?.role !== 'admin') {
      throw new Error('Unauthorized: Admin access required');
    }

    const { userId, amount, description } = data;

    if (!userId || !amount || amount <= 0) {
      throw new Error('Valid user ID and positive amount are required');
    }

    // Check if target user exists
    const userDoc = await db.collection('users').doc(userId).get();
    if (!userDoc.exists) {
      throw new Error('User not found');
    }

    // Create transaction record
    const transaction: WalletTransaction = {
      id: db.collection('temp').doc().id,
      userId: userId,
      type: 'admin_grant',
      amount: parseFloat(amount.toFixed(2)),
      description: description || `Admin credit grant`,
      timestamp: admin.firestore.Timestamp.now(),
      grantedBy: context.auth!.uid,
      source: 'admin_grant',
      createdAt: admin.firestore.Timestamp.now()
    };

    // Update wallet
    await db.collection('wallets').doc(userId).update({
      balance: admin.firestore.FieldValue.increment(transaction.amount),
      history: admin.firestore.FieldValue.arrayUnion(transaction),
      lastUpdated: admin.firestore.Timestamp.now()
    });

    return {
      success: true,
      message: `Successfully granted $${transaction.amount} to user`,
      transaction
    };
  } catch (error) {
    console.error('Error granting wallet credit:', error);
    throw new functions.https.HttpsError('internal', error instanceof Error ? error.message : 'Unknown error');
  }
});

/**
 * Get wallet balance and history
 */
export const getWalletData = functions.https.onCall(async (_data, context) => {
  try {
    verifyAuth(context);
    
    const userId = context.auth!.uid;
    const walletDoc = await db.collection('wallets').doc(userId).get();
    
    if (!walletDoc.exists) {
      // Initialize wallet if it doesn't exist
      const walletData = {
        userId,
        balance: 0,
        referralCode: `user${userId.substring(0, 6)}`,
        usedReferral: false,
        history: [],
        grantedBy: 'system',
        createdAt: admin.firestore.Timestamp.now(),
        lastUpdated: admin.firestore.Timestamp.now()
      };
      
      await db.collection('wallets').doc(userId).set(walletData);
      return walletData;
    }

    return walletDoc.data();
  } catch (error) {
    console.error('Error getting wallet data:', error);
    throw new functions.https.HttpsError('internal', error instanceof Error ? error.message : 'Unknown error');
  }
});

/**
 * Validate referral code
 */
export const validateReferralCode = functions.https.onCall(async (data, context) => {
  try {
    verifyAuth(context);
    
    const { referralCode } = data;
    const userId = context.auth!.uid;

    if (!referralCode) {
      return { valid: false, message: 'Referral code is required' };
    }

    const referralDoc = await db.collection('referralCodes').doc(referralCode).get();
    if (!referralDoc.exists) {
      return { valid: false, message: 'Invalid referral code' };
    }

    const referralData = referralDoc.data() as ReferralCode;
    
    if (!referralData.isActive) {
      return { valid: false, message: 'This referral code is no longer active' };
    }

    if (referralData.userId === userId) {
      return { valid: false, message: 'You cannot use your own referral code' };
    }

    if (referralData.usedBy.includes(userId)) {
      return { valid: false, message: 'You have already used this referral code' };
    }

    // Get wallet settings for bonus amount
    const settings = await getWalletConfig();

    if (!settings.enableReferralBonus || settings.referralBonus <= 0) {
      return { valid: false, message: 'Referral bonuses are currently disabled' };
    }

    // Get referrer info
    const referrerDoc = await db.collection('users').doc(referralData.userId).get();
    const referrerName = referrerDoc.exists ? referrerDoc.data()?.name : 'Unknown User';

    return {
      valid: true,
      message: `Valid referral code from ${referrerName}`,
      referrerName,
      bonusAmount: settings.referralBonus
    };
  } catch (error) {
    console.error('Error validating referral code:', error);
    return { valid: false, message: 'Error validating referral code' };
  }
});

/**
 * Admin function to configure wallet settings with CORS support
 */
export const configureWalletSettings = functions.https.onRequest(async (req, res) => {
  // Handle OPTIONS preflight request
  if (req.method === 'OPTIONS') {
    const allowedOrigins = [
      'https://h1c1-798a8.web.app',
      'https://h1c1-798a8.firebaseapp.com',
      'https://hivecampus.app',
      'https://www.hivecampus.app',
      'http://localhost:5173',
      'http://localhost:3000',
      'http://localhost:5000'
    ];

    const origin = req.headers.origin;
    // Always set CORS headers for preflight, but validate origin
    if (origin && allowedOrigins.includes(origin)) {
      res.set('Access-Control-Allow-Origin', origin);
    } else {
      // Fallback to main domain for production
      res.set('Access-Control-Allow-Origin', 'https://h1c1-798a8.web.app');
    }

    res.set('Access-Control-Allow-Methods', 'POST, OPTIONS');
    res.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
    res.set('Access-Control-Allow-Credentials', 'true');
    res.set('Access-Control-Max-Age', '3600');
    res.status(204).send('');
    return;
  }

  // Set CORS headers for actual request
  const allowedOrigins = [
    'https://h1c1-798a8.web.app',
    'https://h1c1-798a8.firebaseapp.com',
    'https://hivecampus.app',
    'https://www.hivecampus.app',
    'http://localhost:5173',
    'http://localhost:3000',
    'http://localhost:5000'
  ];

  const origin = req.headers.origin;
  if (origin && allowedOrigins.includes(origin)) {
    res.set('Access-Control-Allow-Origin', origin);
  } else {
    // Fallback to main domain for production
    res.set('Access-Control-Allow-Origin', 'https://h1c1-798a8.web.app');
  }
  res.set('Access-Control-Allow-Credentials', 'true');

  try {
    // Only allow POST requests
    if (req.method !== 'POST') {
      console.error(`Method not allowed: ${req.method}`);
      res.status(405).json({
        error: 'Method not allowed',
        allowedMethods: ['POST']
      });
      return;
    }

    // Get and validate authorization token
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      console.error('Missing or invalid authorization header');
      res.status(401).json({
        error: 'Unauthorized',
        message: 'Bearer token required'
      });
      return;
    }

    const token = authHeader.split('Bearer ')[1];
    let decodedToken;
    try {
      decodedToken = await admin.auth().verifyIdToken(token);
    } catch (tokenError) {
      console.error('Invalid token:', tokenError);
      res.status(401).json({
        error: 'Unauthorized',
        message: 'Invalid authentication token'
      });
      return;
    }

    const userId = decodedToken.uid;

    // Verify admin role
    const adminDoc = await db.collection('users').doc(userId).get();
    if (!adminDoc.exists || adminDoc.data()?.role !== 'admin') {
      console.error(`Unauthorized access attempt by user: ${userId}`);
      res.status(403).json({
        error: 'Forbidden',
        message: 'Admin access required'
      });
      return;
    }

    const { signupBonus, referralBonus, enableSignupBonus, enableReferralBonus } = req.body;

    // Validate input data
    if (typeof signupBonus !== 'number' || signupBonus < 0) {
      console.error(`Invalid signup bonus: ${signupBonus}`);
      res.status(400).json({
        error: 'Bad Request',
        message: 'Invalid signup bonus amount - must be a non-negative number'
      });
      return;
    }
    if (typeof referralBonus !== 'number' || referralBonus < 0) {
      console.error(`Invalid referral bonus: ${referralBonus}`);
      res.status(400).json({
        error: 'Bad Request',
        message: 'Invalid referral bonus amount - must be a non-negative number'
      });
      return;
    }

    // Update wallet settings in Firestore
    const settingsData = {
      signupBonus: parseFloat(signupBonus.toFixed(2)),
      referralBonus: parseFloat(referralBonus.toFixed(2)),
      enableSignupBonus: Boolean(enableSignupBonus),
      enableReferralBonus: Boolean(enableReferralBonus),
      updatedAt: admin.firestore.Timestamp.now(),
      updatedBy: userId
    };

    await db.collection('adminSettings').doc('walletConfig').set(settingsData);

    console.log(`Wallet settings updated successfully by admin: ${userId}`, settingsData);

    res.status(200).json({
      success: true,
      message: 'Wallet settings updated successfully',
      settings: {
        signupBonus: settingsData.signupBonus,
        referralBonus: settingsData.referralBonus,
        enableSignupBonus: settingsData.enableSignupBonus,
        enableReferralBonus: settingsData.enableReferralBonus
      }
    });

  } catch (error) {
    console.error('Error configuring wallet settings:', {
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      userId: req.headers.authorization ? 'present' : 'missing'
    });

    res.status(500).json({
      error: 'Internal Server Error',
      message: error instanceof Error ? error.message : 'An unexpected error occurred'
    });
  }
});



/**
 * Get current wallet settings (admin only)
 */
export const getWalletSettings = functions.https.onCall(async (_data, context) => {
  try {
    verifyAuth(context);

    // Verify admin role
    const adminDoc = await db.collection('users').doc(context.auth!.uid).get();
    if (!adminDoc.exists || adminDoc.data()?.role !== 'admin') {
      throw new Error('Unauthorized: Admin access required');
    }

    const settings = await getWalletConfig();
    return {
      success: true,
      settings
    };
  } catch (error) {
    console.error('Error getting wallet settings:', error);
    throw new functions.https.HttpsError('internal', error instanceof Error ? error.message : 'Unknown error');
  }
});

/**
 * Scheduled function to expire old wallet credits
 */
export const expireWalletCredits = functions.pubsub.schedule('0 2 * * *') // Daily at 2 AM
  .timeZone('America/New_York')
  .onRun(async (_context) => {
    try {
      console.log('Starting wallet credit expiration process...');

      const now = admin.firestore.Timestamp.now();
      const walletsSnapshot = await db.collection('wallets').get();

      let expiredCreditsCount = 0;
      let totalExpiredAmount = 0;

      const batch = db.batch();

      for (const walletDoc of walletsSnapshot.docs) {
        const walletData = walletDoc.data();
        const history = walletData.history || [];

        let balanceReduction = 0;
        const updatedHistory = history.map((transaction: any) => {
          if (
            transaction.type === 'credit' &&
            transaction.expiresAt &&
            !transaction.isExpired &&
            transaction.expiresAt.toMillis() < now.toMillis()
          ) {
            // Mark as expired
            transaction.isExpired = true;
            balanceReduction += transaction.amount;
            expiredCreditsCount++;
            totalExpiredAmount += transaction.amount;

            console.log(`Expired credit: ${transaction.amount} for user ${walletDoc.id}`);
          }
          return transaction;
        });

        if (balanceReduction > 0) {
          // Add expiration transaction
          const expirationTransaction = {
            id: db.collection('temp').doc().id,
            type: 'debit',
            amount: balanceReduction,
            description: 'Expired promotional credits',
            source: 'credit_expiration',
            createdAt: now
          };

          updatedHistory.push(expirationTransaction);

          // Update wallet
          batch.update(walletDoc.ref, {
            balance: Math.max(0, walletData.balance - balanceReduction),
            history: updatedHistory,
            lastUpdated: now
          });

          // Track expiration event
          await trackWalletEvent('credits_expired', walletDoc.id, {
            expiredAmount: balanceReduction,
            expiredCount: expiredCreditsCount
          });
        }
      }

      await batch.commit();

      // Log summary to ReeFlex
      await trackWalletEvent('daily_expiration_summary', 'system', {
        totalExpiredCredits: expiredCreditsCount,
        totalExpiredAmount,
        processedAt: now
      });

      console.log(`Expired ${expiredCreditsCount} credits totaling $${totalExpiredAmount}`);

      return {
        success: true,
        expiredCreditsCount,
        totalExpiredAmount
      };
    } catch (error) {
      console.error('Error expiring wallet credits:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  });

/**
 * Generate daily wallet reports
 */
export const generateDailyWalletReport = functions.pubsub.schedule('0 9 * * *') // Daily at 9 AM
  .timeZone('America/New_York')
  .onRun(async (_context) => {
    try {
      console.log('Generating daily wallet report...');

      const today = new Date();
      const yesterday = new Date(today);
      yesterday.setDate(yesterday.getDate() - 1);

      const startOfDay = admin.firestore.Timestamp.fromDate(
        new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate())
      );
      const endOfDay = admin.firestore.Timestamp.fromDate(
        new Date(today.getFullYear(), today.getMonth(), today.getDate())
      );

      // Get ReeFlex events from yesterday
      const eventsSnapshot = await db.collection('reeflex_events')
        .where('timestamp', '>=', startOfDay)
        .where('timestamp', '<', endOfDay)
        .where('source', '==', 'wallet_system')
        .get();

      let totalCreditsGranted = 0;
      const referralCreditsUsed = 0;
      const referrerStats: { [userId: string]: { count: number, amount: number, name?: string } } = {};

      // Process events
      for (const eventDoc of eventsSnapshot.docs) {
        const event = eventDoc.data();

        switch (event.event) {
          case 'wallet_signup_bonus_granted':
          case 'wallet_referral_bonus_granted':
            totalCreditsGranted += event.data.amount || 0;
            break;

          case 'wallet_referral_bonus_earned':
            if (!referrerStats[event.userId]) {
              referrerStats[event.userId] = { count: 0, amount: 0 };
            }
            referrerStats[event.userId].count++;
            referrerStats[event.userId].amount += event.data.amount || 0;
            break;
        }
      }

      // Get top 5 referrers
      const topReferrers = Object.entries(referrerStats)
        .sort(([,a], [,b]) => b.count - a.count)
        .slice(0, 5);

      // Get user names for top referrers
      for (const [userId, stats] of topReferrers) {
        try {
          const userDoc = await db.collection('users').doc(userId).get();
          if (userDoc.exists) {
            stats.name = userDoc.data()?.name || 'Unknown User';
          }
        } catch (error) {
          console.error(`Error getting user name for ${userId}:`, error);
        }
      }

      const report = {
        date: yesterday.toISOString().split('T')[0],
        totalCreditsGranted,
        referralCreditsUsed,
        topReferrers: topReferrers.map(([userId, stats]) => ({
          userId,
          name: stats.name,
          referralCount: stats.count,
          totalEarned: stats.amount
        })),
        generatedAt: admin.firestore.Timestamp.now()
      };

      // Save report
      await db.collection('dailyWalletReports').doc(report.date).set(report);

      // Track report generation
      await trackWalletEvent('daily_report_generated', 'system', report);

      console.log('Daily wallet report generated:', report);

      return { success: true, report };
    } catch (error) {
      console.error('Error generating daily wallet report:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  });

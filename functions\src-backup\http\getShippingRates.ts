import { Request, Response } from 'express';
import { getShippingRates } from '../services/shippingService';
import { PACKAGE_PRESETS } from '../services/shippingService';

interface GetShippingRatesRequest {
  fromAddress: {
    name: string;
    street1: string;
    street2?: string;
    city: string;
    state: string;
    zip: string;
    country: string;
    phone?: string;
  };
  toAddress: {
    name: string;
    street1: string;
    street2?: string;
    city: string;
    state: string;
    zip: string;
    country: string;
    phone?: string;
  };
  packageDetails: {
    useCustom: boolean;
    preset?: string;
    dimensions?: {
      length: number;
      width: number;
      height: number;
      unit: 'in' | 'cm';
    };
    weight?: {
      value: number;
      unit: 'oz' | 'lb';
    };
  };
}

export const getShippingRatesHandler = async (req: Request, res: Response) => {
  try {
    // Set comprehensive CORS headers
    res.set('Access-Control-Allow-Origin', '*');
    res.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
    res.set('Access-Control-Allow-Headers', 'Origin, Content-Type, Accept, Authorization, X-Requested-With');
    res.set('Access-Control-Max-Age', '3600');

    // Handle preflight OPTIONS request
    if (req.method === 'OPTIONS') {
      res.status(204).send('');
      return;
    }

    const { fromAddress, toAddress, packageDetails }: GetShippingRatesRequest = req.body;

    // Validate required fields
    if (!fromAddress || !toAddress || !packageDetails) {
      res.status(400).json({
        error: 'Missing required fields: fromAddress, toAddress, packageDetails'
      });
      return;
    }

    // Validate addresses
    const requiredAddressFields = ['name', 'street1', 'city', 'state', 'zip', 'country'];
    for (const field of requiredAddressFields) {
      if (!fromAddress[field as keyof typeof fromAddress] || !toAddress[field as keyof typeof toAddress]) {
        res.status(400).json({
          error: `Missing required address field: ${field}`
        });
        return;
      }
    }

    // Prepare package info for shipping service
    let packageInfo: any;

    if (packageDetails.useCustom) {
      // Custom package dimensions
      if (!packageDetails.dimensions || !packageDetails.weight) {
        res.status(400).json({
          error: 'Custom package requires dimensions and weight'
        });
        return;
      }

      packageInfo = {
        weight: packageDetails.weight.value.toString(),
        weightUnit: packageDetails.weight.unit,
        length: packageDetails.dimensions.length,
        width: packageDetails.dimensions.width,
        height: packageDetails.dimensions.height,
        dimensionUnit: packageDetails.dimensions.unit
      };
    } else {
      // Preset package
      if (!packageDetails.preset || !(packageDetails.preset in PACKAGE_PRESETS)) {
        res.status(400).json({
          error: 'Invalid package preset'
        });
        return;
      }

      const preset = PACKAGE_PRESETS[packageDetails.preset as keyof typeof PACKAGE_PRESETS];
      packageInfo = {
        weight: preset.weight.value.toString(),
        weightUnit: preset.weight.unit,
        length: preset.dimensions.length,
        width: preset.dimensions.width,
        height: preset.dimensions.height,
        dimensionUnit: preset.dimensions.unit,
        presetUsed: packageDetails.preset
      };
    }

    // Get shipping rates from Shippo
    const rates = await getShippingRates(fromAddress, toAddress, packageInfo);

    // Return the rates
    res.status(200).json({
      success: true,
      rates: rates.map(rate => ({
        carrier: rate.carrier,
        service: rate.service,
        amount: parseFloat(rate.amount),
        currency: rate.currency,
        estimatedDays: rate.estimated_days,
        rateId: rate.rate_id
      }))
    });

  } catch (error: any) {
    console.error('Error getting shipping rates:', error);
    
    res.status(500).json({
      error: 'Failed to get shipping rates',
      message: error.message || 'Unknown error'
    });
  }
};

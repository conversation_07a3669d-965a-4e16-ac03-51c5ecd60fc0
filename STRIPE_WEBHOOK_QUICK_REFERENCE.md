# 🚀 Stripe Webhook - Quick Reference Guide

## 📞 Webhook Configuration

### Stripe Dashboard Setup
```
Webhook URL: https://us-central1-h1c1-798a8.cloudfunctions.net/stripeWebhook
Webhook Secret: whsec_Ggd0wEt8z8NzRV5kyEyvXH2iB1xrWSZq
Events to Enable:
  - checkout.session.completed
  - payment_intent.succeeded  
  - account.updated
```

## 🧪 Test Cards
```
Success: ************** 4242
Declined: ************** 0002
Requires 3D Secure: ************** 3155
```

## 📋 Quick Commands

### Check Function Logs
```bash
firebase functions:log --only stripeWebhook
```

### Test Webhook Endpoint
```bash
curl -X GET https://us-central1-h1c1-798a8.cloudfunctions.net/stripeWebhook
# Expected: 405 Method Not Allowed
```

### Check Firebase Config
```bash
firebase functions:config:get stripe
```

## 🔍 Troubleshooting

### Common Issues
| Issue | Solution |
|-------|----------|
| Webhook signature verification failed | Check webhook secret matches |
| Order not found | Ensure order created before payment |
| Notifications not sent | Verify user IDs in order metadata |
| Function timeout | Check for infinite loops in processing |

### Debug Steps
1. Check webhook endpoint responds (405 for GET)
2. Verify webhook secret in Firebase config
3. Monitor function logs during payment
4. Check order creation in Firestore
5. Verify notification delivery

## 📊 Expected Behavior

### Successful Payment Flow
1. User initiates checkout → `createCheckoutSession` called
2. Stripe processes payment → Webhook receives `checkout.session.completed`
3. Order updated → Status changed to `payment_completed`
4. Notifications sent → Buyer, seller, admin notified
5. Escrow updated → Seller funds held for 7 days
6. Cashback processed → 2% added to buyer wallet

### Log Indicators
- ✅ Success: Green checkmarks and "processed successfully"
- ❌ Error: Red X marks and error descriptions
- 🔔 Info: Blue info icons for processing steps

## 🎯 Production Checklist
- [ ] Webhook URL configured in Stripe Dashboard
- [ ] Test payments completed successfully
- [ ] Order creation verified in Firestore
- [ ] Notifications working for all parties
- [ ] Escrow balances updating correctly
- [ ] Error logging functioning properly
- [ ] Admin panel receiving real-time notifications

## 🆘 Emergency Contacts
- Firebase Console: https://console.firebase.google.com/project/h1c1-798a8
- Stripe Dashboard: https://dashboard.stripe.com
- Function Logs: Firebase Console → Functions → Logs

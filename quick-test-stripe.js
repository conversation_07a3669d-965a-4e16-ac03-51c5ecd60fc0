#!/usr/bin/env node

// Quick test script to verify <PERSON><PERSON> fixes
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🧪 Quick Stripe Fixes Test\n');

// Test 1: Check if files exist and have correct content
function testFileChanges() {
  console.log('🔧 Testing file changes...');
  
  let passed = 0;
  let total = 0;
  
  // Check AuthContext.tsx
  total++;
  try {
    const authContextPath = path.join(__dirname, 'src', 'contexts', 'AuthContext.tsx');
    const content = fs.readFileSync(authContextPath, 'utf8');
    
    if (content.includes('export const useAuth') && content.includes('useContext')) {
      console.log('  ✅ AuthContext.tsx - useAuth export and useContext import found');
      passed++;
    } else {
      console.log('  ❌ AuthContext.tsx - Missing useAuth export or useContext import');
    }
  } catch (error) {
    console.log('  ❌ AuthContext.tsx - File not found or error reading');
  }
  
  // Check useStripeCheckout.ts
  total++;
  try {
    const hookPath = path.join(__dirname, 'src', 'hooks', 'useStripeCheckout.ts');
    const content = fs.readFileSync(hookPath, 'utf8');
    
    if (content.includes("import { useAuth } from '../contexts/AuthContext'")) {
      console.log('  ✅ useStripeCheckout.ts - Correct import path found');
      passed++;
    } else {
      console.log('  ❌ useStripeCheckout.ts - Incorrect import path');
    }
  } catch (error) {
    console.log('  ❌ useStripeCheckout.ts - File not found or error reading');
  }
  
  console.log(`  📊 File changes: ${passed}/${total} passed\n`);
  return passed === total;
}

// Test 2: Check if functions are accessible
async function testFunctionEndpoint() {
  console.log('🔧 Testing function endpoint...');
  
  try {
    const response = await fetch('https://us-central1-h1c1-798a8.cloudfunctions.net/stripeApi', {
      method: 'GET'
    });
    
    if (response.status === 404) {
      console.log('  ✅ Function endpoint accessible (404 expected for GET request)');
      return true;
    } else {
      console.log(`  ⚠️  Function endpoint returned: ${response.status}`);
      return true; // Still accessible
    }
  } catch (error) {
    console.log(`  ❌ Function endpoint not accessible: ${error.message}`);
    return false;
  }
}

// Test 3: Check Firebase configuration
function testFirebaseConfig() {
  console.log('🔧 Testing Firebase configuration...');
  
  try {
    const configPath = path.join(__dirname, 'src', 'firebase', 'config.ts');
    if (fs.existsSync(configPath)) {
      console.log('  ✅ Firebase config file exists');
      return true;
    } else {
      console.log('  ⚠️  Firebase config file not found at expected location');
      return false;
    }
  } catch (error) {
    console.log(`  ❌ Error checking Firebase config: ${error.message}`);
    return false;
  }
}

// Test 4: Validate backend function structure
function testBackendFunction() {
  console.log('🔧 Testing backend function structure...');
  
  try {
    const functionPath = path.join(__dirname, 'functions', 'src', 'index.ts');
    const content = fs.readFileSync(functionPath, 'utf8');
    
    let checks = 0;
    let total = 3;
    
    if (content.includes('handleCreateCheckoutSession')) {
      console.log('  ✅ handleCreateCheckoutSession function found');
      checks++;
    } else {
      console.log('  ❌ handleCreateCheckoutSession function not found');
    }
    
    if (content.includes('sellerId') && content.includes('listing.ownerId || listing.userId')) {
      console.log('  ✅ sellerId validation logic found');
      checks++;
    } else {
      console.log('  ❌ sellerId validation logic not found');
    }
    
    if (content.includes('webhookSecret')) {
      console.log('  ✅ Webhook secret handling found');
      checks++;
    } else {
      console.log('  ❌ Webhook secret handling not found');
    }
    
    console.log(`  📊 Backend checks: ${checks}/${total} passed\n`);
    return checks === total;
  } catch (error) {
    console.log(`  ❌ Error reading backend function: ${error.message}\n`);
    return false;
  }
}

// Main test runner
async function runTests() {
  console.log('🚀 Running comprehensive Stripe fixes test...\n');
  
  const results = {
    fileChanges: testFileChanges(),
    functionEndpoint: await testFunctionEndpoint(),
    firebaseConfig: testFirebaseConfig(),
    backendFunction: testBackendFunction()
  };
  
  console.log('📋 Test Results Summary:');
  console.log('========================');
  
  let totalPassed = 0;
  let totalTests = 0;
  
  Object.entries(results).forEach(([test, passed]) => {
    console.log(`${passed ? '✅' : '❌'} ${test}: ${passed ? 'PASSED' : 'FAILED'}`);
    if (passed) totalPassed++;
    totalTests++;
  });
  
  console.log(`\n🎯 Overall: ${totalPassed}/${totalTests} tests passed`);
  
  if (totalPassed === totalTests) {
    console.log('\n🎉 All tests passed! Your Stripe fixes are ready.');
    console.log('\n📋 Next steps:');
    console.log('1. Start your app: npm run dev');
    console.log('2. Open test file: test-frontend-fix.html');
    console.log('3. Test complete payment flow');
    console.log('4. Monitor logs: firebase functions:log');
  } else {
    console.log('\n⚠️  Some tests failed. Please check the issues above.');
    console.log('\n🔧 Common fixes:');
    console.log('- Ensure functions are deployed: firebase deploy --only functions');
    console.log('- Check file paths and content');
    console.log('- Verify Firebase configuration');
  }
  
  console.log('\n📖 For detailed testing instructions, see: STRIPE_TESTING_GUIDE.md');
}

// Run the tests
runTests().catch(console.error);

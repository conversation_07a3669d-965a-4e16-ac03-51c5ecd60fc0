/**
 * Wallet System Test Suite
 * 
 * This file contains test scenarios for the Hive Campus wallet credit system.
 * These tests should be run in a Firebase emulator environment.
 */

const { initializeTestEnvironment, assertSucceeds, assertFails } = require('@firebase/rules-unit-testing');
const { doc, getDoc, setDoc, updateDoc } = require('firebase/firestore');

describe('Wallet Credit System Tests', () => {
  let testEnv;
  let adminDb;
  let userDb;
  let testUserId = 'test-user-123';
  let adminUserId = 'admin-user-456';

  beforeAll(async () => {
    testEnv = await initializeTestEnvironment({
      projectId: 'wallet-test-project',
      firestore: {
        rules: `
          rules_version = '2';
          service cloud.firestore {
            match /databases/{database}/documents {
              function isAuthenticated() {
                return request.auth != null;
              }
              
              function isAdmin() {
                return isAuthenticated() && 
                  exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
                  get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
              }
              
              match /wallets/{userId} {
                allow read: if isAuthenticated() && request.auth.uid == userId;
                allow write: if isAdmin();
              }
              
              match /users/{userId} {
                allow read, write: if isAuthenticated();
              }
            }
          }
        `
      }
    });

    // Set up admin and user contexts
    adminDb = testEnv.authenticatedContext(adminUserId).firestore();
    userDb = testEnv.authenticatedContext(testUserId).firestore();

    // Create admin user
    await adminDb.collection('users').doc(adminUserId).set({
      role: 'admin',
      name: 'Test Admin'
    });

    // Create test user
    await userDb.collection('users').doc(testUserId).set({
      role: 'student',
      name: 'Test User'
    });
  });

  afterAll(async () => {
    await testEnv.cleanup();
  });

  describe('Wallet Security Rules', () => {
    test('User can read their own wallet', async () => {
      // Admin creates wallet for user
      await assertSucceeds(
        adminDb.collection('wallets').doc(testUserId).set({
          userId: testUserId,
          balance: 5.00,
          referralCode: 'TEST123',
          usedReferral: false,
          history: [],
          grantedBy: 'signup',
          createdAt: new Date(),
          lastUpdated: new Date()
        })
      );

      // User can read their own wallet
      await assertSucceeds(
        userDb.collection('wallets').doc(testUserId).get()
      );
    });

    test('User cannot write to their own wallet', async () => {
      // User cannot modify their wallet balance
      await assertFails(
        userDb.collection('wallets').doc(testUserId).update({
          balance: 1000.00
        })
      );
    });

    test('Admin can write to any wallet', async () => {
      // Admin can modify wallet
      await assertSucceeds(
        adminDb.collection('wallets').doc(testUserId).update({
          balance: 10.00,
          lastUpdated: new Date()
        })
      );
    });
  });

  describe('Wallet Initialization', () => {
    test('New user wallet should have signup bonus', async () => {
      const newUserId = 'new-user-789';
      
      // Simulate wallet initialization
      const walletData = {
        userId: newUserId,
        balance: 5.00, // $5 signup bonus
        referralCode: 'NEWUSER789',
        usedReferral: false,
        history: [{
          id: 'txn-1',
          type: 'credit',
          amount: 5.00,
          description: 'Welcome bonus for joining Hive Campus!',
          source: 'signup_bonus',
          createdAt: new Date()
        }],
        grantedBy: 'signup',
        createdAt: new Date(),
        lastUpdated: new Date()
      };

      await assertSucceeds(
        adminDb.collection('wallets').doc(newUserId).set(walletData)
      );

      const wallet = await adminDb.collection('wallets').doc(newUserId).get();
      const data = wallet.data();
      
      expect(data.balance).toBe(5.00);
      expect(data.history).toHaveLength(1);
      expect(data.history[0].source).toBe('signup_bonus');
    });
  });

  describe('Referral System', () => {
    test('Valid referral code should grant bonus to both users', async () => {
      const referrerId = 'referrer-123';
      const newUserId = 'referred-456';
      
      // Create referrer wallet
      await adminDb.collection('wallets').doc(referrerId).set({
        userId: referrerId,
        balance: 5.00,
        referralCode: 'REFERRER123',
        usedReferral: false,
        history: [],
        grantedBy: 'signup',
        createdAt: new Date(),
        lastUpdated: new Date()
      });

      // Create referral code document
      await adminDb.collection('referralCodes').doc('REFERRER123').set({
        code: 'REFERRER123',
        userId: referrerId,
        usedBy: [],
        totalRewards: 0,
        isActive: true,
        createdAt: new Date()
      });

      // Simulate referral processing
      const referralBonus = 5.00;
      
      // Update new user wallet
      await adminDb.collection('wallets').doc(newUserId).set({
        userId: newUserId,
        balance: 10.00, // $5 signup + $5 referral
        referralCode: 'NEWUSER456',
        usedReferral: true,
        history: [
          {
            id: 'txn-1',
            type: 'credit',
            amount: 5.00,
            description: 'Welcome bonus for joining Hive Campus!',
            source: 'signup_bonus',
            createdAt: new Date()
          },
          {
            id: 'txn-2',
            type: 'credit',
            amount: 5.00,
            description: 'Referral bonus from REFERRER123',
            source: 'referral_bonus',
            referralUserId: referrerId,
            createdAt: new Date()
          }
        ],
        grantedBy: 'referral',
        createdAt: new Date(),
        lastUpdated: new Date()
      });

      // Update referrer wallet
      await adminDb.collection('wallets').doc(referrerId).update({
        balance: 10.00, // $5 original + $5 referral reward
        history: [{
          id: 'txn-3',
          type: 'credit',
          amount: 5.00,
          description: 'Referral reward for inviting new user',
          source: 'referral_bonus',
          referralUserId: newUserId,
          createdAt: new Date()
        }],
        lastUpdated: new Date()
      });

      // Verify both wallets updated correctly
      const newUserWallet = await adminDb.collection('wallets').doc(newUserId).get();
      const referrerWallet = await adminDb.collection('wallets').doc(referrerId).get();

      expect(newUserWallet.data().balance).toBe(10.00);
      expect(newUserWallet.data().usedReferral).toBe(true);
      expect(referrerWallet.data().balance).toBe(10.00);
    });
  });

  describe('Admin Credit Grants', () => {
    test('Admin should be able to grant credits', async () => {
      const grantAmount = 15.00;
      const currentWallet = await adminDb.collection('wallets').doc(testUserId).get();
      const currentBalance = currentWallet.data().balance;

      // Simulate admin credit grant
      const transaction = {
        id: 'admin-grant-1',
        type: 'credit',
        amount: grantAmount,
        description: 'Admin credit grant',
        source: 'admin_grant',
        grantedBy: adminUserId,
        createdAt: new Date()
      };

      await assertSucceeds(
        adminDb.collection('wallets').doc(testUserId).update({
          balance: currentBalance + grantAmount,
          history: [...(currentWallet.data().history || []), transaction],
          lastUpdated: new Date()
        })
      );

      const updatedWallet = await adminDb.collection('wallets').doc(testUserId).get();
      expect(updatedWallet.data().balance).toBe(currentBalance + grantAmount);
    });
  });

  describe('Checkout Integration', () => {
    test('Wallet credit should be calculated correctly', () => {
      const scenarios = [
        {
          itemPrice: 20.00,
          walletBalance: 5.00,
          useWallet: true,
          expectedWalletCredit: 5.00,
          expectedStripeAmount: 15.00
        },
        {
          itemPrice: 3.00,
          walletBalance: 10.00,
          useWallet: true,
          expectedWalletCredit: 3.00,
          expectedStripeAmount: 0.00
        },
        {
          itemPrice: 25.00,
          walletBalance: 8.50,
          useWallet: false,
          expectedWalletCredit: 0.00,
          expectedStripeAmount: 25.00
        }
      ];

      scenarios.forEach(scenario => {
        const walletCreditToApply = scenario.useWallet 
          ? Math.min(scenario.walletBalance, scenario.itemPrice) 
          : 0;
        const stripeAmount = Math.max(0, scenario.itemPrice - walletCreditToApply);

        expect(walletCreditToApply).toBe(scenario.expectedWalletCredit);
        expect(stripeAmount).toBe(scenario.expectedStripeAmount);
      });
    });
  });

  describe('Transaction History', () => {
    test('All wallet operations should be logged', async () => {
      const wallet = await adminDb.collection('wallets').doc(testUserId).get();
      const history = wallet.data().history || [];

      // Verify transaction history structure
      if (history.length > 0) {
        const transaction = history[0];
        expect(transaction).toHaveProperty('id');
        expect(transaction).toHaveProperty('type');
        expect(transaction).toHaveProperty('amount');
        expect(transaction).toHaveProperty('description');
        expect(transaction).toHaveProperty('source');
        expect(transaction).toHaveProperty('createdAt');
      }
    });
  });
});

// Manual test scenarios for integration testing
console.log(`
=== MANUAL TESTING SCENARIOS ===

1. SIGNUP BONUS TEST:
   - Create new user account
   - Check wallet balance is $5.00
   - Verify transaction history shows signup bonus

2. REFERRAL TEST:
   - User A gets referral code from profile
   - User B signs up with referral code
   - Both users should have additional $5.00

3. CHECKOUT TEST:
   - User with $10 wallet balance
   - Purchase $25 item with wallet enabled
   - Verify $10 applied, $15 charged to Stripe
   - Check wallet deducted only after payment success

4. ADMIN GRANT TEST:
   - Admin logs into admin panel
   - Grants $20 to specific user
   - User sees updated balance immediately

5. EDGE CASE TESTS:
   - Try using own referral code (should fail)
   - Try using referral code twice (should fail)
   - Payment failure with wallet credit (wallet unchanged)
   - Concurrent wallet operations

=== END MANUAL TESTS ===
`);

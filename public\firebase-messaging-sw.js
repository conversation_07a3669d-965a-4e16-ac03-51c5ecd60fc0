// Firebase Cloud Messaging Service Worker
// This file must be in the public directory and served from the root

// Import Firebase scripts
importScripts('https://www.gstatic.com/firebasejs/11.9.1/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/11.9.1/firebase-messaging-compat.js');

// Firebase configuration - must match your app config
const firebaseConfig = {
  apiKey: "AIzaSyBnF-pVKlXNdwBQ-74SEfw7NHJLLmL5e6A",
  authDomain: "h1c1-798a8.firebaseapp.com",
  projectId: "h1c1-798a8",
  storageBucket: "h1c1-798a8.firebasestorage.app",
  messagingSenderId: "1096652648176",
  appId: "1:1096652648176:web:4caac283b87d55a4ba9c35",
  measurementId: "G-6WQNVK6V4K"
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);

// Initialize Firebase Cloud Messaging
const messaging = firebase.messaging();

// Handle background messages
messaging.onBackgroundMessage((payload) => {
  console.log('[firebase-messaging-sw.js] Received background message:', payload);

  const notificationTitle = payload.notification?.title || 'Hive Campus';
  const notificationOptions = {
    body: payload.notification?.body || 'You have a new notification',
    icon: payload.notification?.icon || '/icons/icon-192.png',
    badge: '/icons/icon-96.png',
    tag: payload.data?.type || 'general',
    data: {
      click_action: payload.notification?.click_action || payload.data?.click_action || '/',
      ...payload.data
    },
    actions: [
      {
        action: 'view',
        title: 'View',
        icon: '/icons/icon-96.png'
      },
      {
        action: 'dismiss',
        title: 'Dismiss'
      }
    ],
    requireInteraction: payload.data?.requireInteraction === 'true',
    silent: false,
    vibrate: [200, 100, 200]
  };

  // Show notification
  self.registration.showNotification(notificationTitle, notificationOptions);
});

// Handle notification click
self.addEventListener('notificationclick', (event) => {
  console.log('[firebase-messaging-sw.js] Notification click received:', event);

  event.notification.close();

  if (event.action === 'dismiss') {
    return;
  }

  // Get the click action URL
  const clickAction = event.notification.data?.click_action || '/';
  
  // Focus or open the app window
  event.waitUntil(
    clients.matchAll({ type: 'window', includeUncontrolled: true }).then((clientList) => {
      // Check if there's already a window/tab open with the target URL
      for (const client of clientList) {
        if (client.url.includes(clickAction) && 'focus' in client) {
          return client.focus();
        }
      }
      
      // If no window/tab is open, open a new one
      if (clients.openWindow) {
        const baseUrl = self.location.origin;
        const fullUrl = clickAction.startsWith('http') ? clickAction : baseUrl + clickAction;
        return clients.openWindow(fullUrl);
      }
    })
  );
});

// Handle push event (fallback)
self.addEventListener('push', (event) => {
  console.log('[firebase-messaging-sw.js] Push event received:', event);
  
  if (event.data) {
    try {
      const payload = event.data.json();
      console.log('[firebase-messaging-sw.js] Push payload:', payload);
      
      // This will be handled by onBackgroundMessage, but we include it as fallback
      const notificationTitle = payload.notification?.title || 'Hive Campus';
      const notificationOptions = {
        body: payload.notification?.body || 'You have a new notification',
        icon: '/icons/icon-192.png',
        badge: '/icons/icon-96.png',
        data: payload.data || {}
      };

      event.waitUntil(
        self.registration.showNotification(notificationTitle, notificationOptions)
      );
    } catch (error) {
      console.error('[firebase-messaging-sw.js] Error parsing push payload:', error);
    }
  }
});

console.log('[firebase-messaging-sw.js] Service worker loaded and ready');

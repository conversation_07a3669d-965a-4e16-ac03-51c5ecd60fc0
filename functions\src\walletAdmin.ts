// Wallet admin functions for Hive Campus
import * as functions from 'firebase-functions/v1';
import * as admin from 'firebase-admin';
import cors from 'cors';

// CORS configuration for development and production
const corsHandler = cors({
  origin: [
    'https://h1c1-798a8.web.app',
    'https://h1c1-798a8.firebaseapp.com',
    'https://hivecampus.app',
    'https://www.hivecampus.app',
    'http://localhost:5173',
    'http://localhost:5174',
    'http://localhost:3000',
    'http://localhost:5000'
  ],
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With']
});

// HTTP endpoint for granting wallet credits (for admin dashboard with CORS)
export const grantWalletCredits = functions
  .runWith({
    memory: '256MB',
    timeoutSeconds: 30,
  })
  .https.onRequest(async (req, res) => {
    // Handle CORS
    corsHandler(req, res, async () => {
      try {
        // Only allow POST requests
        if (req.method !== 'POST') {
          res.status(405).json({ error: 'Method not allowed' });
          return;
        }

        // Check authorization header
        const authHeader = req.headers.authorization;
        if (!authHeader || !authHeader.startsWith('Bearer ')) {
          res.status(401).json({ error: 'Unauthorized' });
          return;
        }

        const token = authHeader.split('Bearer ')[1];
        
        // Verify the token
        const decodedToken = await admin.auth().verifyIdToken(token);
        
        // Verify admin role
        const adminDoc = await admin.firestore().collection('users').doc(decodedToken.uid).get();
        if (!adminDoc.exists || adminDoc.data()?.role !== 'admin') {
          res.status(403).json({ error: 'Admin access required' });
          return;
        }

        const { userId, amount, description } = req.body;

        if (!userId || !amount || amount <= 0) {
          res.status(400).json({ error: 'Valid user ID and positive amount are required' });
          return;
        }

        // Check if target user exists
        const userDoc = await admin.firestore().collection('users').doc(userId).get();
        if (!userDoc.exists) {
          res.status(404).json({ error: 'User not found' });
          return;
        }

        console.log(`Admin ${decodedToken.uid} granting $${amount} to user ${userId}`);

        // Create transaction record
        const transaction = {
          id: admin.firestore().collection('temp').doc().id,
          userId: userId,
          type: 'admin_grant',
          amount: parseFloat(amount.toFixed(2)),
          description: description || `Admin credit grant`,
          timestamp: admin.firestore.Timestamp.now(),
          grantedBy: decodedToken.uid,
          source: 'admin_grant',
          createdAt: admin.firestore.Timestamp.now()
        };

        // Get or create wallet
        const walletRef = admin.firestore().collection('wallets').doc(userId);
        const walletDoc = await walletRef.get();

        if (!walletDoc.exists) {
          // Create new wallet
          const referralCode = `user${userId.substring(0, 6)}`;
          await walletRef.set({
            userId,
            balance: transaction.amount,
            referralCode,
            usedReferral: false,
            history: [transaction],
            grantedBy: 'admin',
            createdAt: admin.firestore.Timestamp.now(),
            lastUpdated: admin.firestore.Timestamp.now()
          });
        } else {
          // Update existing wallet
          await walletRef.update({
            balance: admin.firestore.FieldValue.increment(transaction.amount),
            history: admin.firestore.FieldValue.arrayUnion(transaction),
            lastUpdated: admin.firestore.Timestamp.now()
          });
        }

        res.status(200).json({
          success: true,
          message: `Successfully granted $${transaction.amount} to user`,
          transaction
        });

      } catch (error) {
        console.error('Error in grantWalletCredits:', error);
        const errorMessage = error instanceof Error ? error.message : 'Unknown error';
        res.status(500).json({ error: errorMessage });
      }
    });
  });

// Test function to verify deployment
export const testWalletAdmin = functions
  .runWith({
    memory: '128MB',
    timeoutSeconds: 10
  })
  .https.onRequest(async (_req, res) => {
    res.json({
      success: true,
      message: 'Wallet admin functions working',
      timestamp: new Date().toISOString()
    });
  });

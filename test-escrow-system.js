// Test script for the new commission-based escrow system
// Run this in the browser console on your Hive Campus app

console.log('🧪 Starting Escrow System Tests...');

// Test commission calculation function
function testCommissionCalculation() {
  console.log('\n📊 Testing Commission Calculation...');
  
  const testCases = [
    // Low price range ($1-$5) - Flat $0.50
    { price: 1, category: 'textbooks', expected: { fee: 0.50, seller: 0.50, rate: 'flat_fee' } },
    { price: 3, category: 'textbooks', expected: { fee: 0.50, seller: 2.50, rate: 'flat_fee' } },
    { price: 5, category: 'electronics', expected: { fee: 0.50, seller: 4.50, rate: 'flat_fee' } },
    
    // Medium price range ($5-$10)
    { price: 8, category: 'textbooks', expected: { fee: 0.64, seller: 7.36, rate: '8%' } },
    { price: 10, category: 'electronics', expected: { fee: 1.00, seller: 9.00, rate: '10%' } },
    { price: 7, category: 'course-materials', expected: { fee: 0.56, seller: 6.44, rate: '8%' } },
    
    // High price range ($10+)
    { price: 20, category: 'textbooks', expected: { fee: 1.60, seller: 18.40, rate: '8%' } },
    { price: 50, category: 'electronics', expected: { fee: 5.00, seller: 45.00, rate: '10%' } },
    { price: 100, category: 'books', expected: { fee: 8.00, seller: 92.00, rate: '8%' } }
  ];
  
  testCases.forEach((test, index) => {
    const isTextbook = ['textbooks', 'course-materials', 'books'].includes(test.category);
    let platformFee;
    
    if (test.price <= 5) {
      platformFee = 0.50;
    } else if (test.price <= 10) {
      platformFee = test.price * (isTextbook ? 0.08 : 0.10);
    } else {
      platformFee = test.price * (isTextbook ? 0.08 : 0.10);
    }
    
    const sellerAmount = test.price - platformFee;
    
    console.log(`Test ${index + 1}: $${test.price} ${test.category}`);
    console.log(`  Expected: Fee=$${test.expected.fee}, Seller=$${test.expected.seller}`);
    console.log(`  Actual:   Fee=$${platformFee.toFixed(2)}, Seller=$${sellerAmount.toFixed(2)}`);
    console.log(`  ✅ ${Math.abs(platformFee - test.expected.fee) < 0.01 && Math.abs(sellerAmount - test.expected.seller) < 0.01 ? 'PASS' : 'FAIL'}`);
  });
}

// Test secret code generation
function testSecretCodeGeneration() {
  console.log('\n🔐 Testing Secret Code Generation...');
  
  const codes = [];
  for (let i = 0; i < 10; i++) {
    const code = Math.floor(100000 + Math.random() * 900000).toString();
    codes.push(code);
    console.log(`Generated code ${i + 1}: ${code} (length: ${code.length})`);
  }
  
  // Check uniqueness
  const unique = new Set(codes);
  console.log(`✅ Uniqueness test: ${unique.size === codes.length ? 'PASS' : 'FAIL'} (${unique.size}/${codes.length} unique)`);
  
  // Check format
  const validFormat = codes.every(code => /^\d{6}$/.test(code));
  console.log(`✅ Format test: ${validFormat ? 'PASS' : 'FAIL'} (all 6-digit numbers)`);
}

// Test wallet integration scenarios
function testWalletScenarios() {
  console.log('\n💰 Testing Wallet Integration Scenarios...');
  
  const scenarios = [
    {
      name: '$3 Textbook with $2 Wallet Credit',
      itemPrice: 3,
      category: 'textbooks',
      walletCredit: 2,
      expected: { platformFee: 0.50, sellerReceives: 2.50, stripeCharge: 1.00, walletUsed: 2.00 }
    },
    {
      name: '$20 Electronics with $5 Wallet Credit',
      itemPrice: 20,
      category: 'electronics',
      walletCredit: 5,
      expected: { platformFee: 2.00, sellerReceives: 18.00, stripeCharge: 15.00, walletUsed: 5.00 }
    },
    {
      name: '$8 Course Materials with $10 Wallet Credit',
      itemPrice: 8,
      category: 'course-materials',
      walletCredit: 10,
      expected: { platformFee: 0.64, sellerReceives: 7.36, stripeCharge: 0.00, walletUsed: 8.00 }
    }
  ];
  
  scenarios.forEach((scenario, index) => {
    console.log(`\nScenario ${index + 1}: ${scenario.name}`);
    
    // Calculate commission
    const isTextbook = ['textbooks', 'course-materials', 'books'].includes(scenario.category);
    let platformFee;
    
    if (scenario.itemPrice <= 5) {
      platformFee = 0.50;
    } else if (scenario.itemPrice <= 10) {
      platformFee = scenario.itemPrice * (isTextbook ? 0.08 : 0.10);
    } else {
      platformFee = scenario.itemPrice * (isTextbook ? 0.08 : 0.10);
    }
    
    const sellerReceives = scenario.itemPrice - platformFee;
    const walletUsed = Math.min(scenario.walletCredit, scenario.itemPrice);
    const stripeCharge = Math.max(0, scenario.itemPrice - walletUsed);
    
    console.log(`  Platform Fee: $${platformFee.toFixed(2)} (expected: $${scenario.expected.platformFee})`);
    console.log(`  Seller Receives: $${sellerReceives.toFixed(2)} (expected: $${scenario.expected.sellerReceives})`);
    console.log(`  Stripe Charge: $${stripeCharge.toFixed(2)} (expected: $${scenario.expected.stripeCharge})`);
    console.log(`  Wallet Used: $${walletUsed.toFixed(2)} (expected: $${scenario.expected.walletUsed})`);
    
    const pass = Math.abs(platformFee - scenario.expected.platformFee) < 0.01 &&
                 Math.abs(sellerReceives - scenario.expected.sellerReceives) < 0.01 &&
                 Math.abs(stripeCharge - scenario.expected.stripeCharge) < 0.01 &&
                 Math.abs(walletUsed - scenario.expected.walletUsed) < 0.01;
    
    console.log(`  ✅ ${pass ? 'PASS' : 'FAIL'}`);
  });
}

// Test order status flow
function testOrderStatusFlow() {
  console.log('\n📋 Testing Order Status Flow...');
  
  const statusFlow = [
    'pending_payment',
    'in_progress',
    'shipped', // for shipping orders
    'delivered',
    'completed'
  ];
  
  const inPersonFlow = [
    'pending_payment',
    'in_progress',
    'delivered',
    'completed'
  ];
  
  console.log('Shipping Order Flow:', statusFlow.join(' → '));
  console.log('In-Person Order Flow:', inPersonFlow.join(' → '));
  
  // Test return flow
  const returnFlow = [
    'pending_payment',
    'in_progress',
    'delivered',
    'return_requested'
  ];
  
  console.log('Return Flow:', returnFlow.join(' → '));
  console.log('✅ Status flows defined correctly');
}

// Test time calculations
function testTimeCalculations() {
  console.log('\n⏰ Testing Time Calculations...');
  
  const now = new Date();
  const threeDaysLater = new Date(now.getTime() + 3 * 24 * 60 * 60 * 1000);
  const twoDaysLater = new Date(now.getTime() + 2 * 24 * 60 * 60 * 1000);
  
  console.log(`Current time: ${now.toISOString()}`);
  console.log(`Return deadline (3 days): ${threeDaysLater.toISOString()}`);
  console.log(`Delivery deadline (2 days): ${twoDaysLater.toISOString()}`);
  
  // Test auto-release calculation
  const threeDaysAgo = new Date(now.getTime() - 3 * 24 * 60 * 60 * 1000);
  console.log(`Auto-release threshold (3 days ago): ${threeDaysAgo.toISOString()}`);
  
  console.log('✅ Time calculations working correctly');
}

// Test Firebase Functions availability
async function testFunctionAvailability() {
  console.log('\n🔧 Testing Firebase Functions Availability...');
  
  const functions = [
    'releaseEscrowWithCode',
    'markDeliveryCompleted',
    'requestReturn',
    'autoReleaseEscrow'
  ];
  
  // Check if Firebase is available
  if (typeof firebase !== 'undefined' && firebase.functions) {
    console.log('✅ Firebase Functions SDK available');
    
    functions.forEach(funcName => {
      try {
        const func = firebase.functions().httpsCallable(funcName);
        console.log(`✅ Function ${funcName} is callable`);
      } catch (error) {
        console.log(`❌ Function ${funcName} not available: ${error.message}`);
      }
    });
  } else {
    console.log('❌ Firebase Functions SDK not available in this context');
    console.log('💡 Run this test in the browser console on your Hive Campus app');
  }
}

// Run all tests
async function runAllTests() {
  console.log('🚀 Running Complete Escrow System Test Suite\n');
  console.log('=' * 50);
  
  testCommissionCalculation();
  testSecretCodeGeneration();
  testWalletScenarios();
  testOrderStatusFlow();
  testTimeCalculations();
  await testFunctionAvailability();
  
  console.log('\n' + '=' * 50);
  console.log('🎉 Test Suite Complete!');
  console.log('\n📝 Next Steps:');
  console.log('1. Test actual order creation with new commission structure');
  console.log('2. Test secret code release flow');
  console.log('3. Test shipping label generation with Shippo');
  console.log('4. Test auto-release after 3 days');
  console.log('5. Monitor system performance and error rates');
}

// Export for use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testCommissionCalculation,
    testSecretCodeGeneration,
    testWalletScenarios,
    testOrderStatusFlow,
    testTimeCalculations,
    testFunctionAvailability,
    runAllTests
  };
}

// Auto-run if in browser
if (typeof window !== 'undefined') {
  runAllTests();
}

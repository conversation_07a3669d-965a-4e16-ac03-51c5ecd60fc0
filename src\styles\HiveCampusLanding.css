/* Hive Campus Landing Page Styles */
.hero-container {
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
}

.hero-canvas {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

.side-menu {
  position: fixed;
  left: 2rem;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
}

.menu-icon {
  display: flex;
  flex-direction: column;
  gap: 4px;
  cursor: pointer;
}

.menu-icon span {
  width: 20px;
  height: 2px;
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.menu-icon:hover span {
  background: #3b82f6;
}

.vertical-text {
  writing-mode: vertical-rl;
  text-orientation: mixed;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.875rem;
  font-weight: 600;
  letter-spacing: 0.2em;
  text-transform: uppercase;
}

.hero-content {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  z-index: 10;
  max-width: 90vw;
}

.hero-title {
  font-size: clamp(3rem, 8vw, 8rem);
  font-weight: 900;
  color: white;
  margin-bottom: 2rem;
  letter-spacing: 0.1em;
  text-shadow: 0 0 30px rgba(59, 130, 246, 0.5);
  background: linear-gradient(135deg, #ffffff 0%, #3b82f6 50%, #f59e0b 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-size: 200% 200%;
  animation: gradientShift 4s ease-in-out infinite;
}

@keyframes gradientShift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

.hero-subtitle {
  font-size: clamp(1.125rem, 2.5vw, 1.5rem);
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  max-width: 600px;
  margin: 0 auto;
}

.subtitle-line {
  margin-bottom: 0.5rem;
  opacity: 0.9;
}

.scroll-progress {
  position: fixed;
  right: 2rem;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.scroll-text {
  writing-mode: vertical-rl;
  text-orientation: mixed;
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.75rem;
  font-weight: 600;
  letter-spacing: 0.2em;
  text-transform: uppercase;
}

.progress-track {
  width: 2px;
  height: 100px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 1px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(to bottom, #3b82f6, #f59e0b);
  transition: width 0.3s ease;
  border-radius: 1px;
}

.section-counter {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.75rem;
  font-weight: 600;
  font-family: 'Courier New', monospace;
}

.scroll-sections {
  position: relative;
  z-index: 5;
  height: 300vh;
  pointer-events: none;
}

.content-section {
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 2rem;
}

.content-section .hero-title {
  font-size: clamp(2.5rem, 6vw, 6rem);
  margin-bottom: 1.5rem;
}

.content-section .hero-subtitle {
  font-size: clamp(1rem, 2vw, 1.25rem);
  max-width: 500px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .side-menu {
    left: 1rem;
  }
  
  .scroll-progress {
    right: 1rem;
  }
  
  .hero-content {
    max-width: 95vw;
    padding: 0 1rem;
  }
  
  .vertical-text {
    font-size: 0.75rem;
  }
  
  .scroll-text {
    font-size: 0.625rem;
  }
  
  .progress-track {
    height: 80px;
  }
}

@media (max-width: 480px) {
  .side-menu {
    display: none;
  }
  
  .scroll-progress {
    display: none;
  }
  
  .hero-title {
    font-size: clamp(2rem, 10vw, 4rem);
  }
  
  .hero-subtitle {
    font-size: clamp(0.875rem, 4vw, 1.125rem);
  }
}

/* Animation Classes */
.animate-fade-in {
  animation: fadeIn 1s ease-out;
}

.animate-slide-up {
  animation: slideUp 1s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Glassmorphism effects */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
}

/* Button hover effects */
.hero-content a:last-child {
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.hero-content a:last-child:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

# 🧪 Complete Stripe Testing Guide

## 🚀 Quick Test Options

### Option 1: Use Existing Test File (Recommended)
```bash
# Open the existing test file in your browser
start test-frontend-fix.html
# or double-click the file to open in browser
```

### Option 2: Test in Your React App
```bash
# Start your development server
npm run dev
# Navigate to http://localhost:5173 (or your dev port)
```

### Option 3: Test Backend Directly
```bash
# Test the API endpoint directly
node test-stripe-api.js
```

## 📋 Step-by-Step Testing Instructions

### 🔧 Test 1: Frontend Import Fixes

**What to test**: Verify useAuth import issues are resolved

**Steps**:
1. Start your React app: `npm run dev`
2. Open browser console (F12)
3. Navigate to any page that uses checkout
4. Look for these errors (should NOT appear):
   ```
   ❌ SyntaxError: The requested module '/src/contexts/AuthContext.tsx' does not provide an export named 'useAuth'
   ```

**Expected Result**: ✅ No import errors in console

### 🔧 Test 2: sellerId Validation

**What to test**: Backend properly validates seller information

**Method A - Using Test File**:
1. Open `test-frontend-fix.html` in browser
2. Click "Sign In Anonymously"
3. Click "Create Test Listing" 
4. Click "Test Stripe API Call"

**Expected Results**:
- ✅ Test listing created successfully
- ✅ Stripe API call succeeds
- ✅ Returns session ID and URL

**Method B - Test Invalid Listing**:
```bash
# Test with a listing that has no sellerId
curl -X POST https://us-central1-h1c1-798a8.cloudfunctions.net/stripeApi/create-checkout-session \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_ID_TOKEN" \
  -d '{"listingId": "invalid-listing-id"}'
```

**Expected Result**: ❌ 400 error: "Invalid listing data: missing seller information"

### 🔧 Test 3: Webhook Secret

**What to test**: Stripe webhooks process correctly with new secret

**Steps**:
1. Complete a test payment (see Test 4)
2. Check Stripe Dashboard → Webhooks
3. Look for successful webhook deliveries
4. Check Firebase Functions logs:
   ```bash
   firebase functions:log --only stripeWebhook
   ```

**Expected Results**:
- ✅ Webhook signature verification succeeds
- ✅ No "webhook signature verification failed" errors
- ✅ Order status updates in Firestore

### 🔧 Test 4: Complete Payment Flow

**What to test**: End-to-end payment process

**Steps**:
1. **Create Test Listing**:
   - Go to your app
   - Create a new listing with valid seller info
   - Note the listing ID

2. **Initiate Checkout**:
   - Navigate to the listing
   - Click "Buy Now" or checkout button
   - Should redirect to Stripe Checkout

3. **Complete Payment**:
   - Use Stripe test card: `4242 4242 4242 4242`
   - Any future expiry date
   - Any 3-digit CVC
   - Complete payment

4. **Verify Results**:
   - Check Firestore for order creation
   - Check for success page redirect
   - Check for notifications

**Expected Results**:
- ✅ Checkout session created
- ✅ Redirected to Stripe
- ✅ Payment completes successfully
- ✅ Webhook processes payment
- ✅ Order created in Firestore
- ✅ User redirected to success page

## 🔍 Monitoring & Debugging

### Check Function Logs
```bash
# Real-time logs for all functions
firebase functions:log

# Specific function logs
firebase functions:log --only stripeApi
firebase functions:log --only stripeWebhook

# Filter for errors
firebase functions:log | grep "ERROR\|❌"
```

### Check Firestore Data
1. Go to Firebase Console → Firestore
2. Check `orders` collection for new orders
3. Verify all fields are populated (especially `sellerId`)

### Check Stripe Dashboard
1. Go to Stripe Dashboard → Payments
2. Verify test payments appear
3. Go to Webhooks → Check delivery status
4. Look for any failed webhook deliveries

## 🚨 Common Issues & Solutions

### Issue: "useAuth is not exported"
**Solution**: ✅ Already fixed - useAuth now exported from AuthContext.tsx

### Issue: "sellerId is undefined"
**Solution**: ✅ Already fixed - Enhanced validation in backend

### Issue: "Webhook signature verification failed"
**Solution**: ✅ Already fixed - Updated webhook secret

### Issue: Function not found
**Check**: Ensure functions are deployed
```bash
firebase deploy --only functions:stripeApi
```

### Issue: Authentication errors
**Check**: User is signed in and ID token is valid
```javascript
const user = firebase.auth().currentUser;
const token = await user.getIdToken();
```

## 📊 Success Criteria

Your fixes are working if:

- ✅ **No import errors** in browser console
- ✅ **Checkout sessions create** successfully
- ✅ **sellerId validation** works (rejects invalid, accepts valid)
- ✅ **Webhook processing** succeeds
- ✅ **Orders created** in Firestore with all data
- ✅ **Payment flow** completes end-to-end
- ✅ **Error messages** are specific and helpful

## 🎯 Quick Verification Commands

```bash
# 1. Check if functions are deployed
firebase functions:list

# 2. Test API endpoint
curl -X GET https://us-central1-h1c1-798a8.cloudfunctions.net/stripeApi

# 3. Check configuration
firebase functions:config:get

# 4. Monitor logs in real-time
firebase functions:log --only stripeApi

# 5. Start your app
npm run dev
```

## 🎉 If Everything Works

You should see:
- ✅ Clean console (no import errors)
- ✅ Successful API calls
- ✅ Stripe checkout opens
- ✅ Payments process
- ✅ Orders created in database
- ✅ Users redirected to success page

**Your Stripe integration is now fully functional! 🚀**

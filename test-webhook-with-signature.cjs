// Test Stripe webhook with proper signature
const crypto = require('crypto');

const WEBHOOK_URL = 'https://us-central1-h1c1-798a8.cloudfunctions.net/stripeWebhook';
const WEBHOOK_SECRET = 'whsec_Ggd0wEt8z8NzRV5kyEyvXH2iB1xrWSZq';

// Mock Stripe event
const mockEvent = {
    id: 'evt_test_webhook_signature',
    object: 'event',
    api_version: '2025-05-28.basil',
    created: Math.floor(Date.now() / 1000),
    data: {
        object: {
            id: 'cs_test_signature_check',
            object: 'checkout.session',
            amount_total: 2599,
            currency: 'usd',
            metadata: {
                orderId: 'test-order-signature-123',
                listingId: 'test-listing-456',
                buyerId: 'test-buyer-789',
                sellerId: 'test-seller-101',
                cashbackAmount: '0.52'
            },
            payment_intent: 'pi_test_signature',
            payment_status: 'paid',
            status: 'complete'
        }
    },
    livemode: false,
    pending_webhooks: 1,
    type: 'checkout.session.completed'
};

function createStripeSignature(payload, secret) {
    const timestamp = Math.floor(Date.now() / 1000);
    const payloadString = JSON.stringify(payload);
    const signedPayload = `${timestamp}.${payloadString}`;
    const signature = crypto
        .createHmac('sha256', secret)
        .update(signedPayload, 'utf8')
        .digest('hex');
    
    return `t=${timestamp},v1=${signature}`;
}

async function testWebhookWithSignature() {
    try {
        console.log('🔐 Testing webhook with proper Stripe signature...');
        
        const payload = JSON.stringify(mockEvent);
        const signature = createStripeSignature(mockEvent, WEBHOOK_SECRET);
        
        console.log(`📝 Event ID: ${mockEvent.id}`);
        console.log(`📋 Event Type: ${mockEvent.type}`);
        console.log(`🔐 Signature: ${signature.substring(0, 50)}...`);
        
        const response = await fetch(WEBHOOK_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Stripe-Signature': signature,
                'User-Agent': 'Stripe/1.0 (+https://stripe.com/docs/webhooks)'
            },
            body: payload
        });
        
        const responseText = await response.text();
        
        console.log(`📊 Response Status: ${response.status}`);
        console.log(`📄 Response Body: ${responseText}`);
        
        if (response.status === 200) {
            console.log('✅ Webhook test with signature PASSED!');
            console.log('🎉 The webhook is correctly processing Stripe events!');
            
            // Parse response if it's JSON
            try {
                const responseJson = JSON.parse(responseText);
                if (responseJson.received && responseJson.processed) {
                    console.log('✅ Event was received and processed successfully');
                }
            } catch (e) {
                // Response might not be JSON, that's okay
            }
            
        } else {
            console.log('❌ Webhook test with signature FAILED!');
            console.log('🔍 Check the webhook implementation and logs');
        }
        
    } catch (error) {
        console.error('❌ Error testing webhook with signature:', error.message);
    }
}

async function testInvalidSignature() {
    try {
        console.log('\n🚫 Testing webhook with invalid signature...');
        
        const payload = JSON.stringify(mockEvent);
        const invalidSignature = 't=1234567890,v1=invalid_signature_here';
        
        const response = await fetch(WEBHOOK_URL, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Stripe-Signature': invalidSignature
            },
            body: payload
        });
        
        console.log(`📊 Invalid signature response: ${response.status}`);
        
        if (response.status === 400) {
            console.log('✅ Invalid signature correctly rejected');
        } else {
            console.log('❌ Invalid signature not properly handled');
        }
        
    } catch (error) {
        console.error('❌ Error testing invalid signature:', error.message);
    }
}

async function runSignatureTests() {
    console.log('🚀 Starting Stripe webhook signature tests...');
    console.log('='.repeat(60));
    
    await testWebhookWithSignature();
    await testInvalidSignature();
    
    console.log('\n' + '='.repeat(60));
    console.log('🏁 Signature tests completed!');
    console.log('\n💡 Next steps:');
    console.log('1. Configure this webhook URL in Stripe Dashboard');
    console.log('2. Test with real Stripe events');
    console.log('3. Monitor Firebase Functions logs');
    console.log('4. Verify order creation in Firestore');
}

runSignatureTests();

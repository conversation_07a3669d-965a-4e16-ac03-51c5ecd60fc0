import React, { useState } from 'react';
import { Bell, Plus, Trash2, RefreshCw } from 'lucide-react';
import { AdminNotificationService } from '../../services/adminNotificationService';
import { AdminNotificationType, NOTIFICATION_CONFIG } from '../../types/admin-notifications';
import { seedAdminNotifications } from '../../utils/seedAdminNotifications';
import { isFeatureEnabled } from '../../config/features';

const AdminNotificationTest: React.FC = () => {
  // Check if notifications are enabled via feature flag
  const NOTIFICATIONS_ENABLED = isFeatureEnabled('ADMIN_NOTIFICATIONS');

  const [isCreating, setIsCreating] = useState(false);
  const [isSeeding, setIsSeeding] = useState(false);
  const [selectedType, setSelectedType] = useState<AdminNotificationType>('user_signup');
  const [title, setTitle] = useState('');
  const [message, setMessage] = useState('');
  const [userId, setUserId] = useState('');
  const [username, setUsername] = useState('');
  const [amount, setAmount] = useState('');

  // Show setup message if notifications are not enabled
  if (!NOTIFICATIONS_ENABLED) {
    return (
      <div className="space-y-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-8 text-center">
          <Bell className="h-16 w-16 mx-auto mb-4 text-gray-400" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            Admin Notification Test - Setup Required
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            The admin notification system needs to be deployed before you can test notifications.
          </p>
          <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 text-left">
            <h3 className="font-medium text-blue-800 dark:text-blue-200 mb-2">
              Deployment Steps:
            </h3>
            <ol className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
              <li>1. Deploy Firebase Functions: <code className="bg-blue-100 dark:bg-blue-800 px-1 rounded">firebase deploy --only functions</code></li>
              <li>2. Deploy Firestore Rules: <code className="bg-blue-100 dark:bg-blue-800 px-1 rounded">firebase deploy --only firestore:rules</code></li>
              <li>3. Enable notifications in the code</li>
              <li>4. Refresh this page</li>
            </ol>
          </div>
          <div className="mt-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
            <h4 className="font-medium text-yellow-800 dark:text-yellow-200 mb-2">
              Quick Deployment
            </h4>
            <p className="text-sm text-yellow-700 dark:text-yellow-300 mb-2">
              Use the automated deployment script:
            </p>
            <code className="block bg-yellow-100 dark:bg-yellow-800 px-3 py-2 rounded text-sm">
              npm run deploy:admin-notifications
            </code>
          </div>
        </div>
      </div>
    );
  }

  const handleCreateNotification = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!title.trim() || !message.trim()) {
      alert('Please fill in title and message');
      return;
    }

    setIsCreating(true);
    
    try {
      await AdminNotificationService.createNotification(
        selectedType,
        title,
        message,
        {
          userId: userId || undefined,
          username: username || undefined,
          amount: amount ? parseFloat(amount) : undefined,
          metadata: {
            testNotification: true,
            createdAt: new Date().toISOString()
          }
        }
      );

      // Reset form
      setTitle('');
      setMessage('');
      setUserId('');
      setUsername('');
      setAmount('');
      
      alert('Notification created successfully!');
    } catch (error) {
      console.error('Error creating notification:', error);
      alert('Failed to create notification');
    } finally {
      setIsCreating(false);
    }
  };

  const handleSeedNotifications = async () => {
    setIsSeeding(true);
    
    try {
      await seedAdminNotifications();
      alert('Sample notifications created successfully!');
    } catch (error) {
      console.error('Error seeding notifications:', error);
      alert('Failed to create sample notifications');
    } finally {
      setIsSeeding(false);
    }
  };

  const handleTypeChange = (type: AdminNotificationType) => {
    setSelectedType(type);
    
    // Set default values based on type
    const config = NOTIFICATION_CONFIG[type];
    switch (type) {
      case 'user_signup':
        setTitle('New User Signup');
        setMessage('A new user has joined the platform');
        setUsername('Test User');
        break;
      case 'listing_created':
        setTitle('New Listing Created');
        setMessage('A new listing has been created');
        setUsername('Seller Name');
        setAmount('50');
        break;
      case 'payment_completed':
        setTitle('Payment Completed');
        setMessage('Payment has been processed successfully');
        setUsername('Buyer Name');
        setAmount('75');
        break;
      case 'payment_failed':
        setTitle('Payment Failed');
        setMessage('Payment processing failed');
        setUsername('Buyer Name');
        setAmount('100');
        break;
      default:
        setTitle(`Test ${type.replace(/_/g, ' ')}`);
        setMessage(`This is a test notification for ${type.replace(/_/g, ' ')}`);
        setUsername('Test User');
        break;
    }
  };

  return (
    <div className="space-y-6">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center space-x-2 mb-6">
          <Bell className="h-6 w-6 text-blue-600" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Admin Notification Test
          </h2>
        </div>

        {/* Quick Actions */}
        <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
          <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
            Quick Actions
          </h3>
          <div className="flex flex-wrap gap-2">
            <button
              onClick={handleSeedNotifications}
              disabled={isSeeding}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              {isSeeding ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <Plus className="h-4 w-4" />
              )}
              <span>{isSeeding ? 'Creating...' : 'Create Sample Notifications'}</span>
            </button>
          </div>
        </div>

        {/* Create Custom Notification */}
        <form onSubmit={handleCreateNotification} className="space-y-4">
          <h3 className="text-lg font-medium text-gray-900 dark:text-white">
            Create Custom Notification
          </h3>

          {/* Notification Type */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Notification Type
            </label>
            <select
              value={selectedType}
              onChange={(e) => handleTypeChange(e.target.value as AdminNotificationType)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
            >
              {Object.keys(NOTIFICATION_CONFIG).map((type) => (
                <option key={type} value={type}>
                  {NOTIFICATION_CONFIG[type as AdminNotificationType].icon} {type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                </option>
              ))}
            </select>
          </div>

          {/* Title */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Title
            </label>
            <input
              type="text"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
              placeholder="Notification title"
              required
            />
          </div>

          {/* Message */}
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Message
            </label>
            <textarea
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
              placeholder="Notification message"
              required
            />
          </div>

          {/* Optional Fields */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                User ID (optional)
              </label>
              <input
                type="text"
                value={userId}
                onChange={(e) => setUserId(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
                placeholder="user123"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Username (optional)
              </label>
              <input
                type="text"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
                placeholder="John Doe"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Amount (optional)
              </label>
              <input
                type="number"
                step="0.01"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-2 focus:ring-blue-500"
                placeholder="0.00"
              />
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end">
            <button
              type="submit"
              disabled={isCreating}
              className="flex items-center space-x-2 px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
            >
              {isCreating ? (
                <RefreshCw className="h-4 w-4 animate-spin" />
              ) : (
                <Plus className="h-4 w-4" />
              )}
              <span>{isCreating ? 'Creating...' : 'Create Notification'}</span>
            </button>
          </div>
        </form>

        {/* Instructions */}
        <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <h4 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
            Testing Instructions
          </h4>
          <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
            <li>• Use "Create Sample Notifications" to generate test data</li>
            <li>• Create custom notifications to test specific scenarios</li>
            <li>• Check the bell icon in the header for real-time updates</li>
            <li>• Visit the full notifications page to see all notifications</li>
            <li>• Test marking notifications as read/unread</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default AdminNotificationTest;

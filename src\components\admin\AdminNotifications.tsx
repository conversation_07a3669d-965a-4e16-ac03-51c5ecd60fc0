import React from 'react';
import { <PERSON> } from 'react-router-dom';
import { Bell } from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import { isFeatureEnabled } from '../../config/features';

interface AdminNotificationsProps {
  className?: string;
}

const AdminNotifications: React.FC<AdminNotificationsProps> = ({ className = '' }) => {
  const { isAdmin } = useAuth();

  // Don't show notifications if not admin
  if (!isAdmin) {
    return null;
  }

  // Check if notifications are enabled via feature flag
  const NOTIFICATIONS_ENABLED = isFeatureEnabled('ADMIN_NOTIFICATIONS');

  return (
    <div className={className}>
      {/* Bell Icon Button - Direct Link */}
      <Link
        to="/admin/notifications"
        className="relative p-1 text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors block"
        title={NOTIFICATIONS_ENABLED ? 'View notifications' : 'Notifications (Setup Required)'}
      >
        <span className="sr-only">View notifications</span>
        <Bell className="h-6 w-6" />

        {/* Setup indicator - small orange dot to indicate setup is needed */}
        {!NOTIFICATIONS_ENABLED && (
          <span className="absolute -top-1 -right-1 flex h-3 w-3 items-center justify-center rounded-full bg-orange-500 ring-2 ring-white dark:ring-gray-800">
          </span>
        )}
      </Link>
    </div>
  );
};

export default AdminNotifications;

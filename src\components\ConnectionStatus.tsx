import React, { useState, useEffect } from 'react';
import { Wifi, WifiOff, Al<PERSON><PERSON>riangle, CheckCircle } from 'lucide-react';
import { firebaseErrorHandler } from '../utils/firebaseErrorHandler';

interface ConnectionStatusProps {
  className?: string;
}

const ConnectionStatus: React.FC<ConnectionStatusProps> = ({ className = '' }) => {
  const [isOnline, setIsOnline] = useState(navigator.onLine);
  const [connectionState, setConnectionState] = useState(firebaseErrorHandler.getConnectionState());
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    const handleOnline = () => {
      setIsOnline(true);
      setConnectionState(firebaseErrorHandler.getConnectionState());
    };

    const handleOffline = () => {
      setIsOnline(false);
      setConnectionState(firebaseErrorHandler.getConnectionState());
    };

    // Update connection state periodically
    const interval = setInterval(() => {
      setConnectionState(firebaseErrorHandler.getConnectionState());
    }, 5000);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      clearInterval(interval);
    };
  }, []);

  const getStatusIcon = () => {
    if (!isOnline) {
      return <WifiOff className="w-4 h-4 text-red-500" />;
    }
    
    if (connectionState.retryCount > 0) {
      return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
    }
    
    return <CheckCircle className="w-4 h-4 text-green-500" />;
  };

  const getStatusText = () => {
    if (!isOnline) {
      return 'Offline';
    }
    
    if (connectionState.retryCount > 0) {
      return `Reconnecting... (${connectionState.retryCount}/${connectionState.maxRetries})`;
    }
    
    return 'Connected';
  };

  const getStatusColor = () => {
    if (!isOnline) {
      return 'text-red-600 dark:text-red-400';
    }
    
    if (connectionState.retryCount > 0) {
      return 'text-yellow-600 dark:text-yellow-400';
    }
    
    return 'text-green-600 dark:text-green-400';
  };

  // Don't show if everything is working fine
  if (isOnline && connectionState.retryCount === 0) {
    return null;
  }

  return (
    <div className={`fixed top-4 right-4 z-50 ${className}`}>
      <div 
        className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-3 cursor-pointer transition-all duration-200 hover:shadow-xl"
        onClick={() => setShowDetails(!showDetails)}
      >
        <div className="flex items-center space-x-2">
          {getStatusIcon()}
          <span className={`text-sm font-medium ${getStatusColor()}`}>
            {getStatusText()}
          </span>
        </div>
        
        {showDetails && (
          <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
            <div className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
              <div>Network: {isOnline ? 'Online' : 'Offline'}</div>
              {connectionState.lastConnected && (
                <div>
                  Last connected: {connectionState.lastConnected.toLocaleTimeString()}
                </div>
              )}
              {connectionState.retryCount > 0 && (
                <div>
                  Retry attempts: {connectionState.retryCount}/{connectionState.maxRetries}
                </div>
              )}
            </div>
            
            {!isOnline && (
              <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                <p>• Check your internet connection</p>
                <p>• Try refreshing the page</p>
                <p>• Disable ad blockers if needed</p>
              </div>
            )}
            
            {connectionState.retryCount > 0 && (
              <div className="mt-2 text-xs text-gray-500 dark:text-gray-400">
                <p>• Connection issues detected</p>
                <p>• Automatically retrying...</p>
                <p>• Some features may be limited</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ConnectionStatus;

import React, { useEffect, useState } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { CheckCircle, AlertCircle, Mail } from 'lucide-react';
import { auth } from '../firebase/config';
import { applyActionCode, sendEmailVerification } from 'firebase/auth';
import HiveCampusLoader from '../components/HiveCampusLoader';

const EmailVerification: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const [status, setStatus] = useState<'loading' | 'success' | 'error' | 'resend'>('loading');
  const [message, setMessage] = useState('');
  const [isResending, setIsResending] = useState(false);

  useEffect(() => {
    const verifyEmail = async () => {
      const actionCode = searchParams.get('oobCode');
      const mode = searchParams.get('mode');

      if (mode === 'verifyEmail' && actionCode) {
        try {
          // Apply the email verification code
          await applyActionCode(auth, actionCode);
          
          // Reload the user to get updated emailVerified status
          if (auth.currentUser) {
            await auth.currentUser.reload();
          }
          
          setStatus('success');
          setMessage('Your email has been successfully verified! You can now access Hive Campus.');
          
          // Redirect to home after 3 seconds
          setTimeout(() => {
            navigate('/home', { replace: true });
          }, 3000);
          
        } catch (error: any) {
          console.error('Email verification error:', error);
          setStatus('error');
          
          if (error.code === 'auth/invalid-action-code') {
            setMessage('This verification link is invalid or has expired. Please request a new one.');
          } else if (error.code === 'auth/expired-action-code') {
            setMessage('This verification link has expired. Please request a new one.');
          } else {
            setMessage('Failed to verify email. Please try again or request a new verification link.');
          }
        }
      } else {
        // No action code provided, show resend option
        setStatus('resend');
        setMessage('Please check your email for a verification link, or request a new one below.');
      }
    };

    verifyEmail();
  }, [searchParams, navigate]);

  const handleResendVerification = async () => {
    if (!auth.currentUser) {
      setMessage('Please log in first to resend verification email.');
      return;
    }

    setIsResending(true);
    try {
      await sendEmailVerification(auth.currentUser, {
        url: `${window.location.origin}/verify-email`,
        handleCodeInApp: false
      });
      setMessage('Verification email sent! Please check your inbox.');
      setStatus('resend');
    } catch (error: any) {
      console.error('Error resending verification:', error);
      setMessage('Failed to send verification email. Please try again.');
    } finally {
      setIsResending(false);
    }
  };

  const renderContent = () => {
    switch (status) {
      case 'loading':
        return (
          <div className="text-center">
            <HiveCampusLoader size="large" className="mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
              Verifying Your Email
            </h2>
            <p className="text-gray-600 dark:text-gray-400">
              Please wait while we verify your email address...
            </p>
          </div>
        );

      case 'success':
        return (
          <div className="text-center">
            <div className="w-20 h-20 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-6">
              <CheckCircle className="w-10 h-10 text-green-600 dark:text-green-400" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
              Email Verified Successfully!
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              {message}
            </p>
            <p className="text-sm text-gray-500 dark:text-gray-500">
              Redirecting you to Hive Campus in a few seconds...
            </p>
          </div>
        );

      case 'error':
        return (
          <div className="text-center">
            <div className="w-20 h-20 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-6">
              <AlertCircle className="w-10 h-10 text-red-600 dark:text-red-400" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
              Verification Failed
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              {message}
            </p>
            <button
              onClick={handleResendVerification}
              disabled={isResending}
              className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isResending ? (
                <>
                  <Loader className="w-4 h-4 animate-spin inline mr-2" />
                  Sending...
                </>
              ) : (
                'Resend Verification Email'
              )}
            </button>
          </div>
        );

      case 'resend':
        return (
          <div className="text-center">
            <div className="w-20 h-20 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mx-auto mb-6">
              <Mail className="w-10 h-10 text-blue-600 dark:text-blue-400" />
            </div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
              Verify Your Email
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              {message}
            </p>
            <button
              onClick={handleResendVerification}
              disabled={isResending}
              className="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-lg font-medium transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isResending ? (
                <>
                  <Loader className="w-4 h-4 animate-spin inline mr-2" />
                  Sending...
                </>
              ) : (
                'Resend Verification Email'
              )}
            </button>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-accent-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Logo */}
        <div className="text-center mb-8">
          <img
            src="/hive-campus-logo.svg"
            alt="Hive Campus Logo"
            className="w-16 h-16 mx-auto mb-4"
          />
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Hive Campus</h1>
        </div>

        {/* Content Card */}
        <div className="bg-white dark:bg-gray-800 rounded-3xl shadow-xl p-8">
          {renderContent()}
        </div>

        {/* Footer */}
        <div className="text-center mt-6">
          <p className="text-sm text-gray-500 dark:text-gray-400">
            Need help? Contact{' '}
            <a href="mailto:<EMAIL>" className="text-primary-600 hover:text-primary-700">
              <EMAIL>
            </a>
          </p>
        </div>
      </div>
    </div>
  );
};

export default EmailVerification;

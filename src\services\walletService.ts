import { doc, getDoc, setDoc } from 'firebase/firestore';
import { db } from '../firebase/config';
import { WalletTransaction } from '../firebase/types';

export interface LocalWalletData {
  userId: string;
  balance: number;
  referralCode: string;
  usedReferral: boolean;
  history: WalletTransaction[];
  grantedBy: 'admin' | 'referral' | 'signup' | 'cashback' | 'system';
  lastUpdated: Date;
  createdAt: Date;
}

/**
 * Local wallet service that works without Firebase Functions
 * This is a fallback until the functions are deployed
 */
export class LocalWalletService {

  /**
   * Safely parse date from various timestamp formats
   */
  private static safeParseDate(timestamp: any): Date {
    if (!timestamp) return new Date();

    try {
      // If it's a Firestore Timestamp with toDate method
      if (timestamp.toDate && typeof timestamp.toDate === 'function') {
        return timestamp.toDate();
      }
      // If it's a serialized timestamp with seconds property
      if (timestamp.seconds && typeof timestamp.seconds === 'number') {
        return new Date(timestamp.seconds * 1000);
      }
      // If it's a serialized timestamp with _seconds property
      if (timestamp._seconds && typeof timestamp._seconds === 'number') {
        return new Date(timestamp._seconds * 1000);
      }
      // If it's already a Date object
      if (timestamp instanceof Date) {
        return timestamp;
      }
      // Try to parse as string or number
      const parsed = new Date(timestamp);
      if (isNaN(parsed.getTime())) {
        console.warn('Invalid timestamp, using current date:', timestamp);
        return new Date();
      }
      return parsed;
    } catch (error) {
      console.warn('Error parsing timestamp, using current date:', error, timestamp);
      return new Date();
    }
  }

  /**
   * Get or create wallet data for a user
   */
  static async getWalletData(userId: string): Promise<LocalWalletData> {
    try {
      // Try to get existing wallet
      const walletRef = doc(db, 'wallets', userId);
      const walletDoc = await getDoc(walletRef);
      
      if (walletDoc.exists()) {
        const data = walletDoc.data();
        return {
          userId: data.userId,
          balance: data.balance || 0,
          referralCode: data.referralCode || this.generateReferralCode(userId),
          usedReferral: data.usedReferral || false,
          history: data.history || [],
          grantedBy: data.grantedBy || 'system',
          lastUpdated: this.safeParseDate(data.lastUpdated),
          createdAt: this.safeParseDate(data.createdAt)
        };
      } else {
        // Create new wallet
        const newWallet = await this.createWallet(userId);
        return newWallet;
      }
    } catch (error) {
      console.error('Error getting wallet data:', error);
      // Return default wallet data if there's an error
      return this.getDefaultWallet(userId);
    }
  }
  
  /**
   * Create a new wallet for a user
   */
  static async createWallet(userId: string): Promise<LocalWalletData> {
    try {
      const referralCode = this.generateReferralCode(userId);
      const now = new Date();
      
      const walletData: LocalWalletData = {
        userId,
        balance: 0, // Start with $0 - admin can configure bonuses later
        referralCode,
        usedReferral: false,
        history: [],
        grantedBy: 'system',
        createdAt: now,
        lastUpdated: now
      };
      
      // Save to Firestore
      const walletRef = doc(db, 'wallets', userId);
      await setDoc(walletRef, {
        ...walletData,
        createdAt: now,
        lastUpdated: now
      });
      
      // Also create referral code document
      const referralRef = doc(db, 'referralCodes', referralCode);
      await setDoc(referralRef, {
        code: referralCode,
        userId,
        usedBy: [],
        totalRewards: 0,
        isActive: true,
        createdAt: now
      });
      
      return walletData;
    } catch (error) {
      console.error('Error creating wallet:', error);
      return this.getDefaultWallet(userId);
    }
  }
  
  /**
   * Generate a unique referral code
   */
  static generateReferralCode(userId: string): string {
    const prefix = userId.substring(0, 6).toUpperCase();
    const suffix = Math.random().toString(36).substring(2, 5).toUpperCase();
    return `${prefix}${suffix}`;
  }
  
  /**
   * Get default wallet data (fallback)
   */
  static getDefaultWallet(userId: string): LocalWalletData {
    return {
      userId,
      balance: 0,
      referralCode: this.generateReferralCode(userId),
      usedReferral: false,
      history: [],
      grantedBy: 'system',
      createdAt: new Date(),
      lastUpdated: new Date()
    };
  }
  
  /**
   * Validate a referral code
   */
  static async validateReferralCode(code: string, userId: string): Promise<{
    valid: boolean;
    message: string;
    referrerName?: string;
    bonusAmount?: number;
  }> {
    try {
      if (!code.trim()) {
        return { valid: false, message: 'Referral code is required' };
      }
      
      // Check if referral code exists
      const referralRef = doc(db, 'referralCodes', code);
      const referralDoc = await getDoc(referralRef);
      
      if (!referralDoc.exists()) {
        return { valid: false, message: 'Invalid referral code' };
      }
      
      const referralData = referralDoc.data();
      
      if (!referralData.isActive) {
        return { valid: false, message: 'This referral code is no longer active' };
      }
      
      if (referralData.userId === userId) {
        return { valid: false, message: 'You cannot use your own referral code' };
      }
      
      if (referralData.usedBy?.includes(userId)) {
        return { valid: false, message: 'You have already used this referral code' };
      }
      
      // Get referrer info
      const userRef = doc(db, 'users', referralData.userId);
      const userDoc = await getDoc(userRef);
      const referrerName = userDoc.exists() ? userDoc.data()?.name : 'Unknown User';
      
      // Check admin settings for bonus amount
      const settingsRef = doc(db, 'adminSettings', 'walletConfig');
      const settingsDoc = await getDoc(settingsRef);
      const settings = settingsDoc.exists() ? settingsDoc.data() : null;
      
      const bonusEnabled = settings?.enableReferralBonus || false;
      const bonusAmount = settings?.referralBonus || 0;
      
      if (!bonusEnabled || bonusAmount <= 0) {
        return { valid: false, message: 'Referral bonuses are currently disabled' };
      }
      
      return {
        valid: true,
        message: `Valid referral code from ${referrerName}`,
        referrerName,
        bonusAmount
      };
    } catch (error) {
      console.error('Error validating referral code:', error);
      return { valid: false, message: 'Error validating referral code' };
    }
  }
  
  /**
   * Check if wallet functions are available
   */
  static async checkFunctionsAvailable(): Promise<boolean> {
    try {
      // Try to make a simple request to see if functions are deployed
      const response = await fetch('https://us-central1-h1c1-798a8.cloudfunctions.net/getWalletData', {
        method: 'OPTIONS', // Preflight request
      });
      return response.ok;
    } catch (_error) {
      return false;
    }
  }
}

import React, { useState } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { <PERSON>ert<PERSON>ircle, Loader, GraduationCap, ShoppingBag } from 'lucide-react';
import { signInWithEmail, getUserProfile, signInWithMicrosoft } from '../../firebase/auth';
import '../../pages/LoginForm.css';

const LoginScreen: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const from = (location.state as { from?: { pathname: string } })?.from?.pathname || '/home';
  
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email || !password) {
      setError('Please enter both email and password');
      return;
    }
    
    setIsLoading(true);
    setError(null);
    
    try {
      await signInWithEmail(email, password);

      // Get user profile to determine role-based redirect
      try {
        const profileResult = await getUserProfile();
        if (profileResult && profileResult.success && profileResult.data) {
          const userData = profileResult.data as any;
          const userRole = userData.role;

          // Role-based redirection
          if (userRole === 'admin') {
            navigate('/admin/dashboard', { replace: true });
          } else if (userRole === 'merchant') {
            navigate('/merchant/dashboard', { replace: true });
          } else {
            navigate('/home', { replace: true });
          }
        } else {
          // Fallback to default redirect if profile fetch fails
          navigate(from, { replace: true });
        }
      } catch (profileError) {
        console.error('Error fetching user profile:', profileError);
        // Fallback to default redirect
        navigate(from, { replace: true });
      }
    } catch (err: unknown) {
      console.error('Login error:', err);
      const errorMessage = err instanceof Error ? err.message : 'Invalid email or password. Please try again.';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleMicrosoftLogin = async () => {
    setIsLoading(true);
    setError(null);

    try {
      await signInWithMicrosoft();
      navigate('/', { replace: true });
    } catch (err: unknown) {
      console.error('Microsoft login error:', err);
      setError(err instanceof Error ? err.message : 'Failed to sign in with Microsoft');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col bg-gradient-to-br from-primary-50 via-white to-accent-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Decorative Elements */}
      <div className="absolute top-0 left-0 w-full h-full overflow-hidden pointer-events-none">
        <div className="absolute top-[10%] left-[15%] w-64 h-64 bg-primary-400/20 dark:bg-primary-600/10 rounded-full blur-3xl"></div>
        <div className="absolute bottom-[20%] right-[10%] w-72 h-72 bg-accent-400/20 dark:bg-accent-600/10 rounded-full blur-3xl"></div>
      </div>
      
      {/* Header */}
      <header className="relative z-10 pt-6 px-6">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-2">
            <img
              src="/hive-campus-logo.svg"
              alt="Hive Campus Logo"
              className="w-10 h-10"
            />
            <span className="text-xl font-bold text-gray-900 dark:text-white">Hive Campus</span>
          </div>
          <Link 
            to="/signup" 
            className="text-sm font-medium text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 transition-colors"
          >
            Create Account
          </Link>
        </div>
      </header>
      
      {/* Main Content */}
      <main className="flex-1 flex flex-col justify-center px-6 py-12 sm:px-12 relative z-10">
        <div className="max-w-md w-full mx-auto">
          <form onSubmit={handleLogin} className="form">
            <div className="title">
              Welcome Back <span>Sign in to continue to your marketplace</span>
            </div>

            {error && (
              <div className="error-message">
                <AlertCircle className="w-4 h-4 inline mr-2" />
                {error}
              </div>
            )}
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="input"
              placeholder="<EMAIL>"
              required
              disabled={isLoading}
            />

            <input
              type={showPassword ? "text" : "password"}
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="input"
              placeholder="Enter your password"
              required
              disabled={isLoading}
            />

            <div className="login-with">
              <button
                type="button"
                onClick={handleMicrosoftLogin}
                className="button-log"
                disabled={isLoading}
              >
                <svg className="microsoft-icon" viewBox="0 0 23 23">
                  <path fill="#f3f3f3" d="M0 0h23v23H0z"/>
                  <path fill="#f35325" d="M1 1h10v10H1z"/>
                  <path fill="#81bc06" d="M12 1h10v10H12z"/>
                  <path fill="#05a6f0" d="M1 12h10v10H1z"/>
                  <path fill="#ffba08" d="M12 12h10v10H12z"/>
                </svg>
              </button>
            </div>

            <button
              type="submit"
              className="button-confirm"
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader className="w-4 h-4 animate-spin inline mr-2" />
                  Signing in...
                </>
              ) : (
                'Sign In'
              )}
            </button>
          </form>

          {/* Sign Up Link */}
          <div className="mt-6 text-center">
            <p className="text-gray-600 dark:text-gray-400">
              Don't have an account?{' '}
              <Link to="/signup" className="text-primary-600 dark:text-primary-400 font-semibold hover:underline">
                Sign up
              </Link>
            </p>
          </div>
          
          {/* Login Options */}
          <div className="mt-8">
            <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
              <Link 
                to="/merchant-login" 
                className="flex-1 bg-white/70 dark:bg-gray-800/70 backdrop-blur-md border border-white/20 dark:border-white/10 text-gray-900 dark:text-white py-3 px-6 rounded-xl font-medium transition-all duration-200 flex items-center justify-center space-x-2 shadow-sm hover:shadow-md hover:bg-white/80 dark:hover:bg-gray-800/80"
              >
                <ShoppingBag className="w-5 h-5 text-accent-500" />
                <span>Merchant Login</span>
              </Link>
              
              <Link 
                to="/universities" 
                className="flex-1 bg-white/70 dark:bg-gray-800/70 backdrop-blur-md border border-white/20 dark:border-white/10 text-gray-900 dark:text-white py-3 px-6 rounded-xl font-medium transition-all duration-200 flex items-center justify-center space-x-2 shadow-sm hover:shadow-md hover:bg-white/80 dark:hover:bg-gray-800/80"
              >
                <GraduationCap className="w-5 h-5 text-primary-500" />
                <span>Browse Universities</span>
              </Link>
            </div>
            

          </div>
        </div>
      </main>
      
      {/* Footer */}
      <footer className="relative z-10 py-6 px-6 text-center">
        <p className="text-xs text-gray-600 dark:text-gray-400">
          &copy; {new Date().getFullYear()} Hive Campus. All rights reserved.
        </p>
        <div className="flex justify-center space-x-4 mt-2">
          <Link to="/terms" className="text-xs text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors">
            Terms
          </Link>
          <Link to="/privacy" className="text-xs text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors">
            Privacy
          </Link>
          <Link to="/help" className="text-xs text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors">
            Help
          </Link>
        </div>
      </footer>
    </div>
  );
};

export default LoginScreen;
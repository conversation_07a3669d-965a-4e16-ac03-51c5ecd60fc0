import React, { useState, useEffect } from 'react';
import { AlertTriangle, X, ExternalLink, Shield, Wifi } from 'lucide-react';

interface FirebaseBlockedNoticeProps {
  error?: Error | null;
  onDismiss?: () => void;
}

const FirebaseBlockedNotice: React.FC<FirebaseBlockedNoticeProps> = ({ 
  error, 
  onDismiss 
}) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isDismissed, setIsDismissed] = useState(false);

  useEffect(() => {
    if (error && !isDismissed) {
      const errorMessage = error.message.toLowerCase();
      const isBlocked = errorMessage.includes('blocked') || 
                       errorMessage.includes('err_blocked_by_client') ||
                       errorMessage.includes('failed to fetch') ||
                       errorMessage.includes('ad blocker') ||
                       errorMessage.includes('browser blocking');
      
      setIsVisible(isBlocked);
    } else {
      setIsVisible(false);
    }
  }, [error, isDismissed]);

  const handleDismiss = () => {
    setIsVisible(false);
    setIsDismissed(true);
    onDismiss?.();
  };

  if (!isVisible) return null;

  return (
    <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 max-w-md w-full mx-4">
      <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg shadow-lg p-4">
        <div className="flex items-start">
          <div className="flex-shrink-0">
            <AlertTriangle className="h-5 w-5 text-yellow-600 dark:text-yellow-400" />
          </div>
          
          <div className="ml-3 flex-1">
            <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
              Connection Blocked
            </h3>
            
            <div className="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
              <p className="mb-2">
                Your browser or an extension is blocking connections to our servers.
              </p>
              
              <div className="space-y-2">
                <div className="flex items-center space-x-2">
                  <Shield className="h-4 w-4" />
                  <span>Try disabling ad blockers for this site</span>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Wifi className="h-4 w-4" />
                  <span>Use incognito/private browsing mode</span>
                </div>
                
                <div className="flex items-center space-x-2">
                  <ExternalLink className="h-4 w-4" />
                  <span>Check browser extensions</span>
                </div>
              </div>
            </div>
            
            <div className="mt-3">
              <details className="text-xs text-yellow-600 dark:text-yellow-400">
                <summary className="cursor-pointer hover:text-yellow-800 dark:hover:text-yellow-200">
                  Technical Details
                </summary>
                <div className="mt-2 p-2 bg-yellow-100 dark:bg-yellow-900/40 rounded text-yellow-800 dark:text-yellow-200 font-mono text-xs break-all">
                  {error?.message}
                </div>
              </details>
            </div>
            
            <div className="mt-4 flex space-x-2">
              <button
                onClick={() => window.location.reload()}
                className="text-xs bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded transition-colors"
              >
                Refresh Page
              </button>
              
              <button
                onClick={handleDismiss}
                className="text-xs text-yellow-600 dark:text-yellow-400 hover:text-yellow-800 dark:hover:text-yellow-200 px-3 py-1 rounded border border-yellow-300 dark:border-yellow-600 hover:border-yellow-400 dark:hover:border-yellow-500 transition-colors"
              >
                Dismiss
              </button>
            </div>
          </div>
          
          <div className="flex-shrink-0 ml-4">
            <button
              onClick={handleDismiss}
              className="text-yellow-400 hover:text-yellow-600 dark:text-yellow-500 dark:hover:text-yellow-300"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FirebaseBlockedNotice;

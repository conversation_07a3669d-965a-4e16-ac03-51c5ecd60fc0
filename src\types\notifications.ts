import { Timestamp } from 'firebase/firestore';

// Notification types that can be triggered
export type NotificationType = 
  | 'listing_sold'
  | 'order_confirmed'
  | 'order_delivered'
  | 'wallet_credited'
  | 'wallet_debited'
  | 'new_chat_message'
  | '48_hour_shipping_reminder'
  | 'platform_announcement'
  | 'auction_update'
  | 'payment_failed'
  | 'user_warning'
  | 'delivery_confirmation'
  | 'admin_warning'
  | 'admin_broadcast'
  | 'payment_success';

// Notification delivery channels
export type NotificationChannel = 'in_app' | 'push' | 'email';

// Notification priority levels
export type NotificationPriority = 'low' | 'normal' | 'high' | 'urgent';

// User notification document structure
export interface UserNotification {
  id: string;
  type: NotificationType;
  title: string;
  message: string;
  icon?: string;
  createdAt: Timestamp;
  read: boolean;
  link?: string;
  priority?: NotificationPriority;
  actionRequired?: boolean;
  expiresAt?: Timestamp;
  metadata?: Record<string, any>;
  
  // Additional fields for specific notification types
  orderId?: string;
  listingId?: string;
  chatId?: string;
  senderId?: string;
  senderName?: string;
  amount?: number;
  secretCode?: string;
}

// Notification preferences structure
export interface NotificationPreferences {
  enable_push: boolean;
  enable_email: boolean;
  pause_all: boolean;
  muted_categories: NotificationType[];
  
  // Channel-specific preferences
  channels: {
    [key in NotificationChannel]: {
      enabled: boolean;
      types: NotificationType[];
    };
  };
  
  // Quiet hours (optional)
  quiet_hours?: {
    enabled: boolean;
    start: string; // HH:MM format
    end: string;   // HH:MM format
    timezone: string;
  };
}

// Default notification preferences
export const DEFAULT_NOTIFICATION_PREFERENCES: NotificationPreferences = {
  enable_push: true,
  enable_email: true,
  pause_all: false,
  muted_categories: [],
  channels: {
    in_app: {
      enabled: true,
      types: [
        'listing_sold',
        'order_confirmed',
        'order_delivered',
        'wallet_credited',
        'wallet_debited',
        'new_chat_message',
        '48_hour_shipping_reminder',
        'platform_announcement',
        'auction_update',
        'payment_failed',
        'user_warning',
        'delivery_confirmation',
        'admin_warning',
        'admin_broadcast',
        'payment_success'
      ]
    },
    push: {
      enabled: true,
      types: [
        'listing_sold',
        'order_confirmed',
        'order_delivered',
        'wallet_credited',
        'new_chat_message',
        '48_hour_shipping_reminder',
        'payment_failed',
        'user_warning',
        'delivery_confirmation',
        'admin_warning'
      ]
    },
    email: {
      enabled: true,
      types: [
        'wallet_credited',
        'order_confirmed',
        '48_hour_shipping_reminder',
        'user_warning',
        'payment_failed',
        'payment_success'
      ]
    }
  }
};

// FCM token storage structure
export interface FCMTokenData {
  token: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  platform: 'web' | 'android' | 'ios';
  userAgent?: string;
  active: boolean;
}

// Notification sending request structure
export interface NotificationSendRequest {
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  channels?: NotificationChannel[];
  priority?: NotificationPriority;
  link?: string;
  icon?: string;
  actionRequired?: boolean;
  expiresAt?: Date;
  metadata?: Record<string, any>;
  
  // Specific data for different notification types
  orderId?: string;
  listingId?: string;
  chatId?: string;
  senderId?: string;
  amount?: number;
  secretCode?: string;
}

// Push notification payload structure
export interface PushNotificationPayload {
  notification: {
    title: string;
    body: string;
    icon?: string;
    click_action?: string;
  };
  data?: {
    type: NotificationType;
    link?: string;
    orderId?: string;
    listingId?: string;
    chatId?: string;
    requireInteraction?: string;
    [key: string]: string | undefined;
  };
  webpush?: {
    headers?: {
      TTL?: string;
      Urgency?: 'very-low' | 'low' | 'normal' | 'high';
    };
    notification?: {
      actions?: Array<{
        action: string;
        title: string;
        icon?: string;
      }>;
      badge?: string;
      tag?: string;
      requireInteraction?: boolean;
      silent?: boolean;
      vibrate?: number[];
    };
  };
}

// Email notification template data
export interface EmailNotificationData {
  to: string;
  subject: string;
  template: string;
  data: Record<string, any>;
}

// Notification statistics
export interface NotificationStats {
  total: number;
  unread: number;
  byType: Record<NotificationType, number>;
  byChannel: Record<NotificationChannel, number>;
  lastRead?: Timestamp;
}

// Notification filter options
export interface NotificationFilter {
  types?: NotificationType[];
  read?: boolean;
  priority?: NotificationPriority[];
  dateRange?: {
    start: Date;
    end: Date;
  };
  limit?: number;
  offset?: number;
}

// Notification action result
export interface NotificationActionResult {
  success: boolean;
  error?: string;
  data?: any;
}

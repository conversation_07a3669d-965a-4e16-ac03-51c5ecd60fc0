const admin = require('firebase-admin');
const path = require('path');

// Initialize Firebase Admin
const serviceAccountPath = path.join(__dirname, '../functions/service-account-key.json');

if (!admin.apps.length) {
  try {
    const serviceAccount = require(serviceAccountPath);
    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount),
      projectId: 'h1c1-798a8'
    });
    console.log('✅ Firebase Admin initialized successfully');
  } catch (error) {
    console.error('❌ Error initializing Firebase Admin:', error.message);
    console.log('📝 Make sure service-account-key.json exists in the functions directory');
    process.exit(1);
  }
}

async function verifyAndFixAdminUser() {
  const adminEmail = '<EMAIL>';
  
  try {
    console.log(`🔍 Checking admin user: ${adminEmail}`);
    
    // Step 1: Get user by email
    let userRecord;
    try {
      userRecord = await admin.auth().getUserByEmail(adminEmail);
      console.log(`✅ Found user record for ${adminEmail}`);
      console.log(`   UID: ${userRecord.uid}`);
      console.log(`   Email Verified: ${userRecord.emailVerified}`);
    } catch (error) {
      console.error(`❌ User not found: ${adminEmail}`);
      console.log('📝 Please create the admin user account first');
      return;
    }
    
    // Step 2: Check custom claims
    console.log('\n🔍 Checking custom claims...');
    const customClaims = userRecord.customClaims || {};
    console.log('Current custom claims:', JSON.stringify(customClaims, null, 2));
    
    if (!customClaims.admin || !customClaims.role) {
      console.log('🔧 Setting custom claims...');
      await admin.auth().setCustomUserClaims(userRecord.uid, {
        admin: true,
        role: 'admin'
      });
      console.log('✅ Custom claims set successfully');
    } else {
      console.log('✅ Custom claims already set correctly');
    }
    
    // Step 3: Check Firestore document
    console.log('\n🔍 Checking Firestore document...');
    const userDoc = await admin.firestore().collection('users').doc(userRecord.uid).get();
    
    if (!userDoc.exists) {
      console.log('🔧 Creating Firestore document...');
      await admin.firestore().collection('users').doc(userRecord.uid).set({
        uid: userRecord.uid,
        name: userRecord.displayName || 'Admin User',
        email: userRecord.email,
        role: 'admin',
        university: 'Hive Campus Admin',
        createdAt: admin.firestore.Timestamp.now(),
        updatedAt: admin.firestore.Timestamp.now(),
        emailVerified: true,
        status: 'active',
        adminLevel: 'super',
        permissions: ['user_management', 'content_moderation', 'analytics', 'system_settings', 'super_admin']
      });
      console.log('✅ Firestore document created');
    } else {
      const userData = userDoc.data();
      console.log('Current Firestore data:', JSON.stringify(userData, null, 2));
      
      if (userData.role !== 'admin') {
        console.log('🔧 Updating role in Firestore...');
        await admin.firestore().collection('users').doc(userRecord.uid).update({
          role: 'admin',
          university: 'Hive Campus Admin',
          updatedAt: admin.firestore.Timestamp.now(),
          adminLevel: 'super',
          permissions: ['user_management', 'content_moderation', 'analytics', 'system_settings', 'super_admin']
        });
        console.log('✅ Firestore document updated');
      } else {
        console.log('✅ Firestore document already correct');
      }
    }
    
    // Step 4: Create missing collections
    console.log('\n🔧 Ensuring required collections exist...');
    const collections = ['reports', 'shippingLabels', 'walletReports', 'universityAnalytics', 'systemMetrics', 'adminLogs'];
    
    for (const collectionName of collections) {
      const defaultDoc = await admin.firestore().collection(collectionName).doc('default').get();
      if (!defaultDoc.exists) {
        await admin.firestore().collection(collectionName).doc('default').set({
          created: admin.firestore.Timestamp.now(),
          type: 'system',
          description: `Default document for ${collectionName} collection`
        });
        console.log(`✅ Created ${collectionName} collection`);
      }
    }
    
    console.log('\n🎉 Admin user verification and fix completed successfully!');
    console.log('\n📋 Summary:');
    console.log(`   Email: ${adminEmail}`);
    console.log(`   UID: ${userRecord.uid}`);
    console.log(`   Custom Claims: admin=true, role=admin`);
    console.log(`   Firestore Role: admin`);
    console.log(`   Status: Ready for admin access`);
    
    console.log('\n🔄 Next steps:');
    console.log('1. Clear browser cache and cookies');
    console.log('2. Login with the admin credentials');
    console.log('3. You should be redirected to /admin/dashboard');
    
  } catch (error) {
    console.error('❌ Error during admin verification:', error);
  }
}

// Run the verification
verifyAndFixAdminUser()
  .then(() => {
    console.log('\n✅ Script completed');
    process.exit(0);
  })
  .catch((error) => {
    console.error('❌ Script failed:', error);
    process.exit(1);
  });

import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { Search, Filter, Star, ChevronDown, Grid, List, Grid3X3, ShoppingCart, MessageCircle, Eye, Heart, DollarSign, BarChart3, X, ShoppingBag } from 'lucide-react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useListings } from '../hooks/useListings';
import { useAuth } from '../hooks/useAuth';
import { collection, getDocs, query, where, orderBy, limit } from 'firebase/firestore';
import { firestore } from '../firebase/config';
import { Listing } from '../firebase/types';
import HiveCampusLoader from '../components/HiveCampusLoader';


const Home: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const searchInputRef = useRef<HTMLInputElement>(null);
  const { currentUser } = useAuth();
  const { fetchListings, listings, isLoading: listingsLoading, search: _search } = useListings();

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedSort, setSelectedSort] = useState('relevance');
  const [selectedCondition, setSelectedCondition] = useState('all');
  const [selectedPriceRange, setSelectedPriceRange] = useState('all');
  const [selectedListingType, setSelectedListingType] = useState('all');
  const [layoutMode, setLayoutMode] = useState<'grid' | 'list' | 'compact'>('grid');
  const [showCategoryDropdown, setShowCategoryDropdown] = useState(false);
  const [showSortDropdown, setShowSortDropdown] = useState(false);
  const [showFilterDropdown, setShowFilterDropdown] = useState(false);
  const [showListingTypeDropdown, setShowListingTypeDropdown] = useState(false);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [selectedSuggestionIndex, setSelectedSuggestionIndex] = useState(-1);
  const [currentPage, setCurrentPage] = useState(1);

  // Fallback state for direct Firestore access
  const [fallbackListings, setFallbackListings] = useState<Listing[]>([]);
  const [isFallbackLoading, setIsFallbackLoading] = useState(false);
  const [useFallback, setUseFallback] = useState(false);

  // Optimized fallback function with memoization
  const fetchListingsDirectly = useCallback(async () => {
    try {
      setIsFallbackLoading(true);

      const listingsRef = collection(firestore, 'listings');
      const q = query(
        listingsRef,
        where('status', '==', 'active'),
        orderBy('createdAt', 'desc'),
        limit(50) // Increased limit for better user experience
      );

      const snapshot = await getDocs(q);
      const directListings: Listing[] = [];

      snapshot.forEach((doc) => {
        directListings.push({ id: doc.id, ...doc.data() } as Listing);
      });

      console.log('Direct fetch result:', directListings);
      setFallbackListings(directListings);
      setUseFallback(true);
      setIsFallbackLoading(false);
    } catch (error) {
      console.error('Error fetching listings directly:', error);
      setIsFallbackLoading(false);
    }
  }, []);

  // Fetch listings when component mounts
  useEffect(() => {
    if (currentUser) {
      fetchListings()
        .then((result) => {
          // If the hook fetch fails or returns no data, try direct fetch
          if (!result || !(result as any).success || !(result as any).data?.listings?.length) {
            fetchListingsDirectly();
          }
        })
        .catch((error) => {
          console.error('Error fetching listings via hook:', error);
          fetchListingsDirectly();
        });
    }
  }, [currentUser]); // eslint-disable-line react-hooks/exhaustive-deps

  // Utility function to truncate text to word limit
  const _truncateText = (text: string, wordLimit: number = 50): string => {
    const words = text.split(' ');
    if (words.length <= wordLimit) return text;
    return words.slice(0, wordLimit).join(' ') + '...';
  };

  // Memoized listings data
  const currentListings = useMemo(() =>
    useFallback ? fallbackListings : listings,
    [useFallback, fallbackListings, listings]
  );

  // Memoized filtered and sorted listings
  const filteredListings = useMemo(() => {
    let filtered = [...currentListings];

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(listing =>
        listing.title.toLowerCase().includes(query) ||
        listing.description.toLowerCase().includes(query) ||
        listing.category.toLowerCase().includes(query)
      );
    }

    // Apply category filter
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(listing => listing.category === selectedCategory);
    }

    // Apply condition filter
    if (selectedCondition !== 'all') {
      filtered = filtered.filter(listing => listing.condition === selectedCondition);
    }

    // Apply price range filter
    if (selectedPriceRange !== 'all') {
      const [min, max] = selectedPriceRange.split('-').map(Number);
      filtered = filtered.filter(listing => {
        const price = listing.price;
        if (max) return price >= min && price <= max;
        return price >= min;
      });
    }

    // Apply listing type filter
    if (selectedListingType !== 'all') {
      filtered = filtered.filter(listing => listing.listingType === selectedListingType);
    }

    // Apply sorting
    switch (selectedSort) {
      case 'price-low':
        filtered.sort((a, b) => a.price - b.price);
        break;
      case 'price-high':
        filtered.sort((a, b) => b.price - a.price);
        break;
      case 'newest':
        filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
        break;
      case 'oldest':
        filtered.sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
        break;
      default: // relevance
        // Keep original order for relevance
        break;
    }

    return filtered;
  }, [currentListings, searchQuery, selectedCategory, selectedCondition, selectedPriceRange, selectedListingType, selectedSort]);

  // Optimized dropdown handlers
  const closeAllDropdowns = useCallback(() => {
    setShowCategoryDropdown(false);
    setShowSortDropdown(false);
    setShowFilterDropdown(false);
    setShowListingTypeDropdown(false);
  }, []);

  // Add scroll event listener to close dropdowns
  useEffect(() => {
    const handleScroll = () => {
      closeAllDropdowns();
    };

    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('.dropdown-container')) {
        closeAllDropdowns();
      }
    };

    window.addEventListener('scroll', handleScroll);
    document.addEventListener('click', handleClickOutside);

    return () => {
      window.removeEventListener('scroll', handleScroll);
      document.removeEventListener('click', handleClickOutside);
    };
  }, [closeAllDropdowns]);

  // Handle search focus from navigation
  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    if (urlParams.get('focus') === 'search' && searchInputRef.current) {
      // Small delay to ensure the component is fully rendered
      setTimeout(() => {
        searchInputRef.current?.focus();
        searchInputRef.current?.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }, 100);
    }
  }, [location.search]);

  const quickStats = [
    {
      icon: Eye,
      label: 'Profile Views',
      value: '2.4K',
      change: '+12%',
      color: 'text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/20'
    },
    {
      icon: Heart,
      label: 'Total Likes',
      value: '892',
      change: '+8%',
      color: 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/20'
    },
    {
      icon: MessageCircle,
      label: 'Messages',
      value: '234',
      change: '+15%',
      color: 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/20'
    },
    {
      icon: DollarSign,
      label: 'Earnings',
      value: '$1.2K',
      change: '+22%',
      color: 'text-purple-600 dark:text-purple-400 bg-purple-100 dark:bg-purple-900/20'
    }
  ];



  const categories = [
    { id: 'all', name: 'All Categories' },
    { id: 'electronics', name: 'Electronics & Tech' },
    { id: 'phones', name: 'Mobile Phones' },
    { id: 'computers', name: 'Computers & Laptops' },
    { id: 'gaming', name: 'Gaming & Consoles' },
    { id: 'audio', name: 'Audio & Headphones' },
    { id: 'cameras', name: 'Cameras & Photography' },
    { id: 'textbooks', name: 'Textbooks & Education' },
    { id: 'clothing', name: 'Clothing & Fashion' },
    { id: 'shoes', name: 'Shoes & Sneakers' },
    { id: 'accessories', name: 'Accessories & Jewelry' },
    { id: 'bags', name: 'Bags & Backpacks' },
    { id: 'furniture', name: 'Furniture & Decor' },
    { id: 'appliances', name: 'Home Appliances' },
    { id: 'kitchen', name: 'Kitchen & Dining' },
    { id: 'sports', name: 'Sports & Fitness' },
    { id: 'outdoor', name: 'Outdoor & Recreation' },
    { id: 'bikes', name: 'Bikes & Scooters' },
    { id: 'beauty', name: 'Beauty & Personal Care' },
    { id: 'health', name: 'Health & Wellness' },
    { id: 'art', name: 'Art & Crafts' },
    { id: 'music', name: 'Musical Instruments' },
    { id: 'collectibles', name: 'Collectibles & Antiques' },
    { id: 'tickets', name: 'Tickets & Events' },
    { id: 'services', name: 'Services & Tutoring' },
    { id: 'other', name: 'Other' }
  ];

  const listingTypes = [
    { id: 'all', name: 'All Types', icon: '🏪' },
    { id: 'sell', name: 'For Sale', icon: '💰' },
    { id: 'rent', name: 'For Rent', icon: '📅' },
    { id: 'auction', name: 'Auction', icon: '⚡' }
  ];

  const sortOptions = [
    { id: 'relevance', name: 'Best Match' },
    { id: 'price_low', name: 'Price: Low to High' },
    { id: 'price_high', name: 'Price: High to Low' },
    { id: 'newest', name: 'Newest First' },
    { id: 'oldest', name: 'Oldest First' },
    { id: 'distance', name: 'Distance: Nearest' },
    { id: 'popular', name: 'Most Popular' },
    { id: 'ending_soon', name: 'Ending Soon' }
  ];

  const conditionOptions = [
    { id: 'all', name: 'All Conditions' },
    { id: 'new', name: 'Brand New' },
    { id: 'like_new', name: 'Like New' },
    { id: 'very_good', name: 'Very Good' },
    { id: 'good', name: 'Good' },
    { id: 'fair', name: 'Fair' },
    { id: 'poor', name: 'For Parts/Repair' }
  ];

  const priceRanges = [
    { id: 'all', name: 'All Prices' },
    { id: 'under_25', name: 'Under $25' },
    { id: '25_50', name: '$25 - $50' },
    { id: '50_100', name: '$50 - $100' },
    { id: '100_250', name: '$100 - $250' },
    { id: '250_500', name: '$250 - $500' },
    { id: '500_1000', name: '$500 - $1,000' },
    { id: 'over_1000', name: 'Over $1,000' }
  ];





  const handleBuyNow = (listingId: string) => {
    // Navigate to checkout with the listing ID in the URL
    navigate(`/checkout/${listingId}`, { state: { listingId } });
  };

  const handleChatSeller = (sellerId: string, listingId: string) => {
    // Navigate to messages with the seller
    navigate('/messages', { state: { sellerId, listingId } });
  };

  // Generate suggestions based on search query
  const generateSuggestions = (query: string) => {
    if (!query.trim()) {
      setSuggestions([]);
      setShowSuggestions(false);
      return;
    }

    const queryLower = query.toLowerCase();
    const suggestionMap = new Map<string, number>();

    // Common search terms
    const commonTerms = [
      'textbook', 'electronics', 'furniture', 'laptop', 'phone', 'books', 
      'calculator', 'desk', 'chair', 'backpack', 'tablet', 'headphones',
      'speaker', 'monitor', 'keyboard', 'mouse', 'iphone', 'macbook'
    ];

    // Add matching common terms
    commonTerms.forEach(term => {
      if (term.toLowerCase().startsWith(queryLower)) {
        suggestionMap.set(term, 100); // High priority
      }
    });

    // Get suggestions from listing data
    currentListings.forEach(listing => {
      // Exact title matches
      if (listing.title.toLowerCase().includes(queryLower)) {
        suggestionMap.set(listing.title, 90);
      }
      
      // Individual words from titles that start with the query
      const words = listing.title.split(' ');
      words.forEach(word => {
        const cleanWord = word.toLowerCase().replace(/[^a-z0-9]/g, '');
        if (cleanWord.startsWith(queryLower) && cleanWord.length > queryLower.length) {
          suggestionMap.set(cleanWord, 70);
        }
      });
      
      // Sellers that match
      if (listing.ownerName?.toLowerCase().includes(queryLower)) {
        suggestionMap.set(`by ${listing.ownerName}`, 60);
      }

      // Universities that match
      if (listing.university?.toLowerCase().includes(queryLower)) {
        suggestionMap.set(`in ${listing.university}`, 50);
      }
    });

    // Sort by priority and limit to 6 suggestions
    const suggestionsArray = Array.from(suggestionMap.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 6)
      .map(([suggestion]) => suggestion);

    setSuggestions(suggestionsArray);
    setShowSuggestions(suggestionsArray.length > 0);
  };

  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);
    setSelectedSuggestionIndex(-1);
    generateSuggestions(value);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (!showSuggestions || suggestions.length === 0) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedSuggestionIndex(prev => 
          prev < suggestions.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();  
        setSelectedSuggestionIndex(prev => 
          prev > 0 ? prev - 1 : suggestions.length - 1
        );
        break;
      case 'Enter':
        if (selectedSuggestionIndex >= 0) {
          e.preventDefault();
          handleSuggestionClick(suggestions[selectedSuggestionIndex]);
        }
        break;
      case 'Escape':
        setShowSuggestions(false);
        setSelectedSuggestionIndex(-1);
        break;
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    setSearchQuery(suggestion);
    setShowSuggestions(false);
  };

  const clearSearch = () => {
    setSearchQuery('');
    setSuggestions([]);
    setShowSuggestions(false);
    setSelectedSuggestionIndex(-1);
  };

  // Search and filter functionality
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setShowSuggestions(false);
    if (searchQuery.trim()) {
      // Filter listings based on search query - this is now handled by the memoized filteredListings
      console.log('Search submitted:', searchQuery);
    }
  };

  const sortedListings = [...filteredListings].sort((a, b) => {
    switch (selectedSort) {
      case 'price_low':
        return a.price - b.price;
      case 'price_high':
        return b.price - a.price;
      case 'newest':
        return b.createdAt.seconds - a.createdAt.seconds;
      case 'oldest':
        return a.createdAt.seconds - b.createdAt.seconds;
      case 'popular':
        return 0; // TODO: Add popularity metric to listings
      default:
        return 0;
    }
  });

  // Pagination logic
  const getItemsPerPage = () => {
    if (layoutMode === 'grid') return 10; // 1-column layout: 10 items per page
    if (layoutMode === 'compact') return 20; // 2-column layout: 20 items per page
    return 10; // list layout: 10 items per page
  };

  const itemsPerPage = getItemsPerPage();
  const totalPages = Math.ceil(sortedListings.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedListings = sortedListings.slice(startIndex, endIndex);

  // Reset to page 1 when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery, selectedCategory, selectedCondition, selectedPriceRange, selectedListingType, selectedSort, layoutMode]);

  const renderListingCard = (listing: any) => {
    if (layoutMode === 'list') {
      return (
        <div key={listing.id} className="bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 p-4 group">
          <div className="flex items-center space-x-4">
            <Link to={`/listing/${listing.id}`} className="flex-shrink-0">
              <div className="relative w-24 h-24 rounded-lg overflow-hidden">
                <img
                  src={listing.imageURLs?.[0] || 'https://images.pexels.com/photos/699122/pexels-photo-699122.jpeg?auto=compress&cs=tinysrgb&w=400'}
                  alt={listing.title}
                  className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                />
                <div className="absolute top-1 right-1">
                  <div className="bg-primary-500 text-white p-1 rounded-full">
                    <Star className="w-2 h-2 fill-current" />
                  </div>
                </div>
              </div>
            </Link>
            <div className="flex-1 min-w-0">
              <Link to={`/listing/${listing.id}`}>
                <h3 className="font-semibold text-gray-900 dark:text-white mb-1 truncate hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                  {listing.title}
                </h3>
              </Link>
              <div className="flex items-center space-x-2 mb-2">
                <span className="text-xl font-bold text-primary-600 dark:text-primary-400">
                  ${listing.price}
                </span>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
                  <span>{listing.condition}</span>
                  <span>•</span>
                  <span>{listing.university}</span>
                </div>
                <span className="text-xs bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400 px-2 py-1 rounded-full">
                  {listing.status}
                </span>
              </div>
            </div>
            <div className="flex flex-col space-y-2">
              <button
                onClick={() => handleBuyNow(listing.id)}
                disabled={listing.status !== 'active'}
                className="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg font-medium text-sm transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
              >
                <ShoppingCart className="w-4 h-4" />
                <span>Buy</span>
              </button>
              <button
                onClick={() => handleChatSeller(listing.ownerId, listing.id)}
                className="border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 px-4 py-2 rounded-lg font-medium text-sm transition-colors flex items-center space-x-2"
              >
                <MessageCircle className="w-4 h-4" />
                <span>Chat</span>
              </button>
            </div>
          </div>
        </div>
      );
    }

    if (layoutMode === 'compact') {
      return (
        <div key={listing.id} className="bg-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-md transition-all duration-300 overflow-hidden group">
          <Link to={`/listing/${listing.id}`}>
            <div className="relative aspect-square overflow-hidden">
              <img
                src={listing.imageURLs?.[0] || 'https://images.pexels.com/photos/699122/pexels-photo-699122.jpeg?auto=compress&cs=tinysrgb&w=400'}
                alt={listing.title}
                className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
              />
              <div className="absolute top-2 left-2">
                <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                  listing.status === 'active'
                    ? 'bg-success-100 text-success-700'
                    : 'bg-gray-100 text-gray-700'
                }`}>
                  {listing.status}
                </span>
              </div>
              <div className="absolute top-2 right-2">
                <div className="bg-primary-500 text-white p-1 rounded-full">
                  <Star className="w-3 h-3 fill-current" />
                </div>
              </div>
            </div>
          </Link>
          <div className="p-3">
            <Link to={`/listing/${listing.id}`}>
              <h3 className="font-semibold text-sm text-gray-900 dark:text-white mb-1 line-clamp-2 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
                {listing.title}
              </h3>
            </Link>
            <div className="flex items-center space-x-1 mb-2">
              <span className="text-lg font-bold text-primary-600 dark:text-primary-400">
                ${listing.price}
              </span>
            </div>
            <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400 mb-3">
              <span>{listing.condition}</span>
              <span>{listing.university}</span>
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => handleBuyNow(listing.id)}
                disabled={listing.status !== 'active'}
                className="flex-1 bg-primary-600 hover:bg-primary-700 text-white py-2 rounded-lg font-medium text-xs transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-1"
              >
                <ShoppingCart className="w-3 h-3" />
                <span>Buy</span>
              </button>
              <button
                onClick={() => handleChatSeller(listing.ownerId, listing.id)}
                className="flex-1 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 py-2 rounded-lg font-medium text-xs transition-colors flex items-center justify-center space-x-1"
              >
                <MessageCircle className="w-3 h-3" />
                <span>Chat</span>
              </button>
            </div>
          </div>
        </div>
      );
    }

    // Default grid layout
    return (
      <div key={listing.id} className="bg-white dark:bg-gray-800 rounded-2xl shadow-md hover:shadow-xl transition-all duration-300 transform hover:scale-[1.02] overflow-hidden group">
        <Link to={`/listing/${listing.id}`}>
          <div className="relative overflow-hidden">
            <img
              src={listing.imageURLs?.[0] || '/placeholder-image.jpg'}
              alt={listing.title}
              className="w-full h-48 object-cover group-hover:scale-110 transition-transform duration-300"
            />
            <div className="absolute top-4 left-4">
              <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                listing.status === 'active'
                  ? 'bg-success-500 text-white'
                  : 'bg-gray-500 text-white'
              }`}>
                {listing.status}
              </span>
            </div>
            <div className="absolute top-4 right-4">
              <div className="bg-primary-500 text-white p-1 rounded-full">
                <Star className="w-4 h-4 fill-current" />
              </div>
            </div>
            <div className="absolute bottom-4 right-4 bg-blue-500 text-white px-2 py-1 rounded-full text-xs font-medium">
              {listing.university}
            </div>
          </div>
        </Link>
        <div className="p-3 sm:p-4 md:p-6">
          <Link to={`/listing/${listing.id}`}>
            <h3 className="font-bold text-lg text-gray-900 dark:text-white mb-2 line-clamp-2 hover:text-primary-600 dark:hover:text-primary-400 transition-colors">
              {listing.title}
            </h3>
          </Link>
          <div className="flex items-center space-x-2 mb-3">
            <span className="text-2xl font-bold text-primary-600 dark:text-primary-400">
              ${listing.price}
            </span>
          </div>
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-2 min-w-0 flex-1">
              <div className="w-8 h-8 bg-gray-300 rounded-full flex-shrink-0"></div>
              <div className="min-w-0 flex-1">
                <p className="text-sm font-medium text-gray-900 dark:text-white truncate">{listing.ownerName}</p>
                <div className="flex items-center">
                  <Star className="w-3 h-3 text-yellow-400 fill-current flex-shrink-0" />
                  <span className="text-xs text-gray-500 dark:text-gray-400 ml-1">4.9</span>
                </div>
              </div>
            </div>
            <span className="text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 px-2 py-1 rounded-full flex-shrink-0 ml-2">
              {listing.condition}
            </span>
          </div>
          <div className="flex space-x-2 sm:space-x-3">
            <button
              onClick={() => handleBuyNow(listing.id)}
              disabled={listing.status !== 'active'}
              className="flex-1 bg-primary-600 hover:bg-primary-700 text-white py-2.5 sm:py-3 rounded-lg sm:rounded-xl font-semibold transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-1 sm:space-x-2 text-sm sm:text-base"
            >
              <ShoppingCart className="w-3 h-3 sm:w-4 sm:h-4" />
              <span className="hidden xs:inline sm:inline">Buy Now</span>
              <span className="xs:hidden">Buy</span>
            </button>
            <button
              onClick={() => handleChatSeller(listing.ownerId, listing.id)}
              className="flex-1 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300 py-2.5 sm:py-3 rounded-lg sm:rounded-xl font-semibold transition-colors flex items-center justify-center space-x-1 sm:space-x-2 text-sm sm:text-base"
            >
              <MessageCircle className="w-3 h-3 sm:w-4 sm:h-4" />
              <span className="hidden xs:inline sm:inline">Chat</span>
              <span className="xs:hidden">💬</span>
            </button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 md:ml-64">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-primary-600 via-primary-700 to-accent-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-4 animate-fade-in">
              Student Marketplace
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-blue-100 animate-slide-up">
              Buy, sell, and trade with verified students
            </p>
            
            {/* Search Bar */}
            <form onSubmit={handleSearchSubmit} className="max-w-2xl mx-auto relative animate-scale-in">
              <Search className="absolute left-4 top-4 w-6 h-6 text-gray-400" />
              <input
                ref={searchInputRef}
                type="text"
                value={searchQuery}
                onChange={handleSearchInputChange}
                onKeyDown={handleKeyDown}
                onFocus={() => searchQuery && setShowSuggestions(true)}
                onBlur={() => setTimeout(() => setShowSuggestions(false), 150)}
                placeholder="Search for textbooks, electronics, furniture..."
                className="w-full pl-12 pr-20 py-4 text-lg rounded-2xl border-0 focus:ring-4 focus:ring-white/25 text-gray-900 dark:text-white dark:bg-gray-800"
              />
              {searchQuery && (
                <button
                  type="button"
                  onClick={clearSearch}
                  className="absolute right-16 top-3 bottom-3 px-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors flex items-center justify-center"
                >
                  <X className="w-5 h-5" />
                </button>
              )}
              <button
                type="submit"
                className="absolute right-2 top-2 bottom-2 px-4 bg-primary-600 hover:bg-primary-700 text-white rounded-xl transition-colors flex items-center justify-center"
              >
                <Search className="w-5 h-5" />
              </button>
              
              {/* Auto-suggestions Dropdown */}
              {showSuggestions && suggestions.length > 0 && (
                <div className="absolute top-full left-0 right-0 mt-2 bg-white dark:bg-gray-800 rounded-xl shadow-xl border border-gray-200 dark:border-gray-700 z-50 max-h-64 overflow-y-auto backdrop-blur-sm bg-white/95 dark:bg-gray-800/95">
                  <div className="p-2">
                    <div className="text-xs text-gray-500 dark:text-gray-400 px-3 py-2 font-medium">
                      Suggestions
                    </div>
                    {suggestions.map((suggestion, index) => (
                      <button
                        key={index}
                        type="button"
                        onClick={() => handleSuggestionClick(suggestion)}
                        className={`w-full text-left px-3 py-2.5 transition-all duration-200 rounded-lg text-gray-900 dark:text-white group ${
                          selectedSuggestionIndex === index 
                            ? 'bg-primary-100 dark:bg-primary-900/30' 
                            : 'hover:bg-primary-50 dark:hover:bg-primary-900/20'
                        }`}
                      >
                        <div className="flex items-center space-x-3">
                          <div className="w-8 h-8 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center group-hover:bg-primary-100 dark:group-hover:bg-primary-900/40 transition-colors">
                            <Search className="w-4 h-4 text-gray-500 dark:text-gray-400 group-hover:text-primary-600 dark:group-hover:text-primary-400" />
                          </div>
                          <span className="font-medium group-hover:text-primary-600 dark:group-hover:text-primary-400 transition-colors">
                            {suggestion}
                          </span>
                        </div>
                      </button>
                    ))}
                  </div>
                </div>
              )}
            </form>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-2 sm:px-3 md:px-4 lg:px-6 xl:px-8 py-4 sm:py-6 md:py-8 overflow-x-hidden">
        {/* Analytics Dashboard - Hide when searching */}
        {!searchQuery.trim() && (
        <div className="mb-8">
          <div className="flex items-center space-x-2 mb-6">
            <BarChart3 className="w-6 h-6 text-primary-500" />
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Your Analytics</h2>
          </div>
          
          {/* Quick Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            {quickStats.map((stat, index) => (
              <div key={index} className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className={`w-8 h-8 rounded-lg flex items-center justify-center ${stat.color}`}>
                    <stat.icon className="w-4 h-4" />
                  </div>
                  <span className="text-xs text-success-600 dark:text-success-400 font-medium">
                    {stat.change}
                  </span>
                </div>
                <div className="text-xl font-bold text-gray-900 dark:text-white mb-1">
                  {stat.value}
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400">{stat.label}</p>
              </div>
            ))}
          </div>


        </div>
        )}

        {/* Search Results Header - Show when searching */}
        {searchQuery.trim() && (
          <div className="mb-6">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                  Search Results for "{searchQuery}"
                </h2>
                <p className="text-gray-600 dark:text-gray-400 mt-1">
                  {sortedListings.length} {sortedListings.length === 1 ? 'item' : 'items'} found
                </p>
              </div>
              <button
                onClick={clearSearch}
                className="flex items-center space-x-2 px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white transition-colors"
              >
                <X className="w-4 h-4" />
                <span>Clear Search</span>
              </button>
            </div>
          </div>
        )}

        {/* Filters and Layout Controls */}
        <div className="mb-8">
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
            {/* Filter Dropdowns */}
            <div className="flex flex-wrap gap-3">
              {/* Category Dropdown */}
              <div className="relative dropdown-container">
                <button
                  onClick={() => {
                    closeAllDropdowns();
                    setShowCategoryDropdown(!showCategoryDropdown);
                  }}
                  className="flex items-center space-x-2 px-4 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-all"
                >
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {categories.find(c => c.id === selectedCategory)?.name || 'Category'}
                  </span>
                  <ChevronDown className="w-4 h-4 text-gray-500" />
                </button>
                {showCategoryDropdown && (
                  <div className="absolute top-full left-0 mt-2 w-64 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-lg z-50 max-h-80 overflow-y-auto">
                    {categories.map((category) => (
                      <button
                        key={category.id}
                        onClick={() => {
                          setSelectedCategory(category.id);
                          closeAllDropdowns();
                        }}
                        className="w-full text-left px-4 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                      >
                        {category.name}
                      </button>
                    ))}
                  </div>
                )}
              </div>

              {/* Listing Type Dropdown */}
              <div className="relative dropdown-container">
                <button
                  onClick={() => {
                    closeAllDropdowns();
                    setShowListingTypeDropdown(!showListingTypeDropdown);
                  }}
                  className="flex items-center space-x-2 px-4 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-all"
                >
                  <span className="text-lg">
                    {listingTypes.find(t => t.id === selectedListingType)?.icon || '🏪'}
                  </span>
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {listingTypes.find(t => t.id === selectedListingType)?.name || 'Type'}
                  </span>
                  <ChevronDown className="w-4 h-4 text-gray-500" />
                </button>
                {showListingTypeDropdown && (
                  <div className="absolute top-full left-0 mt-2 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-lg z-50">
                    {listingTypes.map((type) => (
                      <button
                        key={type.id}
                        onClick={() => {
                          setSelectedListingType(type.id);
                          closeAllDropdowns();
                        }}
                        className="w-full text-left px-4 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors flex items-center space-x-3"
                      >
                        <span className="text-lg">{type.icon}</span>
                        <span>{type.name}</span>
                      </button>
                    ))}
                  </div>
                )}
              </div>

              {/* Sort Dropdown */}
              <div className="relative dropdown-container">
                <button
                  onClick={() => {
                    closeAllDropdowns();
                    setShowSortDropdown(!showSortDropdown);
                  }}
                  className="flex items-center space-x-2 px-4 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-all"
                >
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {sortOptions.find(s => s.id === selectedSort)?.name || 'Sort'}
                  </span>
                  <ChevronDown className="w-4 h-4 text-gray-500" />
                </button>
                {showSortDropdown && (
                  <div className="absolute top-full left-0 mt-2 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-lg z-50">
                    {sortOptions.map((option) => (
                      <button
                        key={option.id}
                        onClick={() => {
                          setSelectedSort(option.id);
                          closeAllDropdowns();
                        }}
                        className="w-full text-left px-4 py-3 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
                      >
                        {option.name}
                      </button>
                    ))}
                  </div>
                )}
              </div>

              {/* More Filters Dropdown */}
              <div className="relative dropdown-container">
                <button
                  onClick={() => {
                    closeAllDropdowns();
                    setShowFilterDropdown(!showFilterDropdown);
                  }}
                  className="flex items-center space-x-2 px-4 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-xl hover:bg-gray-50 dark:hover:bg-gray-700 transition-all"
                >
                  <Filter className="w-4 h-4 text-gray-500" />
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">More Filters</span>
                  <ChevronDown className="w-4 h-4 text-gray-500" />
                </button>
                {showFilterDropdown && (
                  <div className="absolute top-full left-0 mt-2 w-80 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-lg z-50 p-6">
                    <div className="space-y-6">
                      {/* Condition Filter */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Condition
                        </label>
                        <select
                          value={selectedCondition}
                          onChange={(e) => setSelectedCondition(e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                        >
                          {conditionOptions.map((option) => (
                            <option key={option.id} value={option.id}>
                              {option.name}
                            </option>
                          ))}
                        </select>
                      </div>

                      {/* Price Range Filter */}
                      <div>
                        <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                          Price Range
                        </label>
                        <select
                          value={selectedPriceRange}
                          onChange={(e) => setSelectedPriceRange(e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white"
                        >
                          {priceRanges.map((range) => (
                            <option key={range.id} value={range.id}>
                              {range.name}
                            </option>
                          ))}
                        </select>
                      </div>

                      <div className="flex space-x-3">
                        <button
                          onClick={() => {
                            setSelectedCondition('all');
                            setSelectedPriceRange('all');
                          }}
                          className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                        >
                          Clear
                        </button>
                        <button
                          onClick={() => setShowFilterDropdown(false)}
                          className="flex-1 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors"
                        >
                          Apply
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Layout Controls */}
            <div className="flex items-center space-x-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-xl p-1">
              <button
                onClick={() => setLayoutMode('grid')}
                className={`p-2 rounded-lg transition-all ${
                  layoutMode === 'grid' 
                    ? 'bg-primary-100 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400' 
                    : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'
                }`}
              >
                <Grid className="w-5 h-5" />
              </button>
              <button
                onClick={() => setLayoutMode('compact')}
                className={`p-2 rounded-lg transition-all ${
                  layoutMode === 'compact' 
                    ? 'bg-primary-100 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400' 
                    : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'
                }`}
              >
                <Grid3X3 className="w-5 h-5" />
              </button>
              <button
                onClick={() => setLayoutMode('list')}
                className={`p-2 rounded-lg transition-all ${
                  layoutMode === 'list' 
                    ? 'bg-primary-100 dark:bg-primary-900/20 text-primary-600 dark:text-primary-400' 
                    : 'text-gray-400 hover:text-gray-600 dark:hover:text-gray-300'
                }`}
              >
                <List className="w-5 h-5" />
              </button>
            </div>
          </div>

        {/* Active Filters */}
          <div className="flex flex-wrap gap-2">
            {selectedCategory !== 'all' && (
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-400">
                {categories.find(c => c.id === selectedCategory)?.name}
                <button
                  onClick={() => setSelectedCategory('all')}
                  className="ml-2 text-primary-500 hover:text-primary-700"
                >
                  ×
                </button>
              </span>
            )}
            {selectedListingType !== 'all' && (
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-400">
                <span className="mr-1">{listingTypes.find(t => t.id === selectedListingType)?.icon}</span>
                {listingTypes.find(t => t.id === selectedListingType)?.name}
                <button
                  onClick={() => setSelectedListingType('all')}
                  className="ml-2 text-blue-500 hover:text-blue-700"
                >
                  ×
                </button>
              </span>
            )}
            {selectedCondition !== 'all' && (
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-400">
                {conditionOptions.find(c => c.id === selectedCondition)?.name}
                <button
                  onClick={() => setSelectedCondition('all')}
                  className="ml-2 text-primary-500 hover:text-primary-700"
                >
                  ×
                </button>
              </span>
            )}
            {selectedPriceRange !== 'all' && (
              <span className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-primary-100 dark:bg-primary-900/20 text-primary-700 dark:text-primary-400">
                {priceRanges.find(p => p.id === selectedPriceRange)?.name}
                <button
                  onClick={() => setSelectedPriceRange('all')}
                  className="ml-2 text-primary-500 hover:text-primary-700"
                >
                  ×
                </button>
              </span>
            )}
          </div>
        </div>



        {/* Featured Listings */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-2">
              <Star className="w-6 h-6 text-accent-500" />
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Featured Listings</h2>
            </div>
            <span className="text-sm text-gray-500 dark:text-gray-400">
              {sortedListings.length} items found
            </span>
          </div>
          
          {(listingsLoading || isFallbackLoading) ? (
            <div className="text-center py-12">
              <HiveCampusLoader size="medium" className="mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Loading listings...</h3>
              <p className="text-gray-500 dark:text-gray-400">
                Please wait while we fetch the latest items.
              </p>
            </div>
          ) : sortedListings.length === 0 ? (
            <div className="text-center py-12">
              <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                {searchQuery || selectedCategory !== 'all' || selectedCondition !== 'all' ? (
                  <Search className="w-8 h-8 text-gray-400" />
                ) : (
                  <ShoppingBag className="w-8 h-8 text-gray-400" />
                )}
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                {searchQuery || selectedCategory !== 'all' || selectedCondition !== 'all'
                  ? 'No items found'
                  : 'No listings available at the moment'
                }
              </h3>
              <p className="text-gray-500 dark:text-gray-400 mb-4">
                {searchQuery || selectedCategory !== 'all' || selectedCondition !== 'all'
                  ? 'Try adjusting your search or filters to find what you\'re looking for.'
                  : 'Be the first to list an item! Come back later to see what others are selling.'
                }
              </p>
              {!searchQuery && selectedCategory === 'all' && selectedCondition === 'all' && (
                <div className="text-center">
                  <p className="text-sm text-gray-400 mb-6">Thank you for using Hive Campus! 🎓</p>
                  <Link
                    to="/add-listing"
                    className="inline-flex items-center px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white font-medium rounded-lg transition-colors"
                  >
                    <ShoppingBag className="w-5 h-5 mr-2" />
                    List Your First Item
                  </Link>
                </div>
              )}
            </div>
          ) : (
            <div className={`grid gap-3 sm:gap-4 md:gap-6 ${
              layoutMode === 'grid'
                ? 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3'
                : layoutMode === 'compact'
                ? 'grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6'
                : 'grid-cols-1'
            }`}>
              {paginatedListings.map(renderListingCard)}
            </div>
          )}

          {/* Pagination Controls */}
          {totalPages > 1 && (
            <div className="flex justify-center items-center space-x-2 mt-8">
              <button
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className="px-3 py-2 rounded-lg bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Previous
              </button>

              {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
                <button
                  key={page}
                  onClick={() => setCurrentPage(page)}
                  className={`px-3 py-2 rounded-lg transition-colors ${
                    currentPage === page
                      ? 'bg-primary-600 text-white'
                      : 'bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
                  }`}
                >
                  {page}
                </button>
              ))}

              <button
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages}
                className="px-3 py-2 rounded-lg bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Next
              </button>
            </div>
          )}
        </div>


      </div>
    </div>
  );
};

export default Home;
import {
  collection,
  doc,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  updateDoc,
  Timestamp
} from 'firebase/firestore';
import { firestore } from '../firebase/config';
import { httpsCallable } from 'firebase/functions';
import { functions } from '../firebase/config';

export interface ShippingLabel {
  id: string;
  orderId: string;
  trackingNumber: string;
  labelUrl: string;
  carrier: string;
  status: 'created' | 'shipped' | 'in_transit' | 'delivered' | 'exception' | 'returned';
  createdAt: Date;
  shippedAt?: Date;
  deliveredAt?: Date;
  estimatedDeliveryDate?: Date;
  cost?: number;
  weight?: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
}

export interface TrackingEvent {
  status: string;
  statusDetail?: string;
  statusDate: Date;
  location?: {
    city?: string;
    state?: string;
    zip?: string;
    country?: string;
  };
  message?: string;
}

export interface ShippingMetrics {
  totalLabels: number;
  activeShipments: number;
  deliveredShipments: number;
  pendingShipments: number;
  exceptionShipments: number;
  averageDeliveryTime: number;
  totalShippingCost: number;
}

/**
 * Admin Shippo Service for shipping label and tracking management
 */
export class AdminShippoService {
  
  /**
   * Get shipping metrics for admin dashboard
   */
  static async getShippingMetrics(): Promise<ShippingMetrics> {
    try {
      const labelsSnapshot = await getDocs(collection(firestore, 'shippingLabels'));
      const labels = labelsSnapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as any[];

      let activeShipments = 0;
      let deliveredShipments = 0;
      let pendingShipments = 0;
      let exceptionShipments = 0;
      let totalShippingCost = 0;
      let totalDeliveryTime = 0;
      let deliveredCount = 0;

      labels.forEach((label: any) => {
        // Count by status
        switch (label.status) {
          case 'created':
          case 'shipped':
            pendingShipments++;
            break;
          case 'in_transit':
            activeShipments++;
            break;
          case 'delivered':
            deliveredShipments++;
            if (label.createdAt && label.deliveredAt) {
              const deliveryTime = label.deliveredAt.toDate().getTime() - label.createdAt.toDate().getTime();
              totalDeliveryTime += deliveryTime;
              deliveredCount++;
            }
            break;
          case 'exception':
          case 'returned':
            exceptionShipments++;
            break;
        }

        // Sum shipping costs
        if (label.cost) {
          totalShippingCost += label.cost;
        }
      });

      const averageDeliveryTime = deliveredCount > 0 ? totalDeliveryTime / deliveredCount / (1000 * 60 * 60 * 24) : 0; // in days

      return {
        totalLabels: labels.length,
        activeShipments,
        deliveredShipments,
        pendingShipments,
        exceptionShipments,
        averageDeliveryTime,
        totalShippingCost
      };
    } catch (error: any) {
      console.error('Error fetching shipping metrics:', error);

      // Provide better error messages for common issues
      if (error.code === 'permission-denied') {
        console.warn('Admin permissions not properly set for shipping data. Please check Firebase security rules and user role.');
      }

      // Return empty metrics instead of throwing
      return {
        totalLabels: 0,
        activeShipments: 0,
        deliveredShipments: 0,
        pendingShipments: 0,
        exceptionShipments: 0,
        averageDeliveryTime: 0,
        totalShippingCost: 0
      };
    }
  }

  /**
   * Get all shipping labels with pagination
   */
  static async getShippingLabels(limitCount: number = 50): Promise<ShippingLabel[]> {
    try {
      const labelsQuery = query(
        collection(firestore, 'shippingLabels'),
        orderBy('createdAt', 'desc'),
        limit(limitCount)
      );
      
      const snapshot = await getDocs(labelsQuery);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate(),
        shippedAt: doc.data().shippedAt?.toDate(),
        deliveredAt: doc.data().deliveredAt?.toDate(),
        estimatedDeliveryDate: doc.data().estimatedDeliveryDate?.toDate()
      })) as ShippingLabel[];
    } catch (error: any) {
      console.error('Error fetching shipping labels:', error);

      // Provide better error messages for common issues
      if (error.code === 'permission-denied') {
        console.warn('Admin permissions not properly set for shipping labels. Please check Firebase security rules and user role.');
      }

      return []; // Return empty array instead of throwing
    }
  }

  /**
   * Get shipping labels by status
   */
  static async getShippingLabelsByStatus(status: string): Promise<ShippingLabel[]> {
    try {
      const labelsQuery = query(
        collection(firestore, 'shippingLabels'),
        where('status', '==', status),
        orderBy('createdAt', 'desc')
      );
      
      const snapshot = await getDocs(labelsQuery);
      return snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        createdAt: doc.data().createdAt?.toDate(),
        shippedAt: doc.data().shippedAt?.toDate(),
        deliveredAt: doc.data().deliveredAt?.toDate(),
        estimatedDeliveryDate: doc.data().estimatedDeliveryDate?.toDate()
      })) as ShippingLabel[];
    } catch (error) {
      console.error('Error fetching shipping labels by status:', error);
      return []; // Return empty array instead of throwing
    }
  }

  /**
   * Get tracking information for a shipment
   */
  static async getTrackingInfo(trackingNumber: string): Promise<TrackingEvent[]> {
    try {
      const getTrackingInfo = httpsCallable(functions, 'getTrackingInfo');
      const result = await getTrackingInfo({ trackingNumber });
      return result.data as TrackingEvent[];
    } catch (error) {
      console.error('Error fetching tracking info:', error);
      throw error;
    }
  }

  /**
   * Update tracking status for a shipment
   */
  static async updateTrackingStatus(labelId: string, status: string, location?: any): Promise<void> {
    try {
      const labelRef = doc(firestore, 'shippingLabels', labelId);
      const updateData: any = {
        status,
        updatedAt: Timestamp.now()
      };

      if (status === 'delivered') {
        updateData.deliveredAt = Timestamp.now();
      } else if (status === 'shipped') {
        updateData.shippedAt = Timestamp.now();
      }

      if (location) {
        updateData.lastLocation = location;
      }

      await updateDoc(labelRef, updateData);
    } catch (error) {
      console.error('Error updating tracking status:', error);
      throw error;
    }
  }

  /**
   * Regenerate shipping label
   */
  static async regenerateLabel(orderId: string): Promise<ShippingLabel> {
    try {
      const generateLabel = httpsCallable(functions, 'generateShippingLabel');
      const result = await generateLabel({ orderId });
      return result.data as ShippingLabel;
    } catch (error) {
      console.error('Error regenerating shipping label:', error);
      throw error;
    }
  }

  /**
   * Cancel shipping label
   */
  static async cancelLabel(labelId: string): Promise<void> {
    try {
      const cancelLabel = httpsCallable(functions, 'cancelShippingLabel');
      await cancelLabel({ labelId });
    } catch (error) {
      console.error('Error canceling shipping label:', error);
      throw error;
    }
  }

  /**
   * Get shipping rates for an address
   */
  static async getShippingRates(
    fromAddress: any,
    toAddress: any,
    parcel: any
  ): Promise<any[]> {
    try {
      const getShippingRates = httpsCallable(functions, 'getShippingRates');
      const result = await getShippingRates({
        fromAddress,
        toAddress,
        parcel
      });
      return result.data as any[];
    } catch (error) {
      console.error('Error fetching shipping rates:', error);
      throw error;
    }
  }

  /**
   * Bulk update tracking statuses
   */
  static async bulkUpdateTracking(): Promise<{ updated: number; errors: number }> {
    try {
      const bulkUpdateTracking = httpsCallable(functions, 'bulkUpdateTracking');
      const result = await bulkUpdateTracking({});
      return result.data as { updated: number; errors: number };
    } catch (error) {
      console.error('Error bulk updating tracking:', error);
      throw error;
    }
  }

  /**
   * Get delivery performance analytics
   */
  static async getDeliveryAnalytics(
    startDate: Date,
    endDate: Date
  ): Promise<{
    onTimeDeliveries: number;
    lateDeliveries: number;
    averageDeliveryTime: number;
    carrierPerformance: Array<{
      carrier: string;
      deliveries: number;
      onTime: number;
      avgTime: number;
    }>;
  }> {
    try {
      const labelsQuery = query(
        collection(firestore, 'shippingLabels'),
        where('createdAt', '>=', Timestamp.fromDate(startDate)),
        where('createdAt', '<=', Timestamp.fromDate(endDate)),
        where('status', '==', 'delivered')
      );
      
      const snapshot = await getDocs(labelsQuery);
      const labels = snapshot.docs.map(doc => ({ id: doc.id, ...doc.data() })) as any[];

      let onTimeDeliveries = 0;
      let lateDeliveries = 0;
      let totalDeliveryTime = 0;
      const carrierStats = new Map<string, { deliveries: number; onTime: number; totalTime: number }>();

      labels.forEach((label: any) => {
        if (label.createdAt && label.deliveredAt) {
          const deliveryTime = label.deliveredAt.toDate().getTime() - label.createdAt.toDate().getTime();
          const deliveryDays = deliveryTime / (1000 * 60 * 60 * 24);
          
          totalDeliveryTime += deliveryDays;

          // Check if on time (assuming 3 days is standard)
          const isOnTime = deliveryDays <= 3;
          if (isOnTime) {
            onTimeDeliveries++;
          } else {
            lateDeliveries++;
          }

          // Track carrier performance
          const carrier = label.carrier || 'Unknown';
          if (!carrierStats.has(carrier)) {
            carrierStats.set(carrier, { deliveries: 0, onTime: 0, totalTime: 0 });
          }
          
          const stats = carrierStats.get(carrier)!;
          stats.deliveries++;
          stats.totalTime += deliveryDays;
          if (isOnTime) {
            stats.onTime++;
          }
        }
      });

      const averageDeliveryTime = labels.length > 0 ? totalDeliveryTime / labels.length : 0;

      const carrierPerformance = Array.from(carrierStats.entries()).map(([carrier, stats]) => ({
        carrier,
        deliveries: stats.deliveries,
        onTime: stats.onTime,
        avgTime: stats.totalTime / stats.deliveries
      }));

      return {
        onTimeDeliveries,
        lateDeliveries,
        averageDeliveryTime,
        carrierPerformance
      };
    } catch (error) {
      console.error('Error fetching delivery analytics:', error);
      throw error;
    }
  }
}

import { vi } from 'vitest'
// File removed for production deployment
export const setupSentryMocks = () => {
  // Mock Sentry React
  vi.mock('@sentry/react', () => ({
    init: vi.fn(),
    captureException: vi.fn(),
    captureMessage: vi.fn(),
    configureScope: vi.fn(),
    setUser: vi.fn(),
    setTag: vi.fn(),
    setContext: vi.fn(),
    addBreadcrumb: vi.fn(),
    ErrorBoundary: ({ children }: { children: React.ReactNode }) => children,
    withErrorBoundary: (component: React.ComponentType) => component,
    browserTracingIntegration: vi.fn(),
    replayIntegration: vi.fn(),
  }))
// File removed for production deployment
  // Mock Sentry Tracing
  vi.mock('@sentry/tracing', () => ({
    BrowserTracing: vi.fn(),
  }))
}

export const mockSentryMethods = {
  captureException: vi.fn(),
  captureMessage: vi.fn(),
  configureScope: vi.fn(),
  setUser: vi.fn(),
  setTag: vi.fn(),
  setContext: vi.fn(),
  addBreadcrumb: vi.fn(),
}
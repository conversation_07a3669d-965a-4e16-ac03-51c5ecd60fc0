#!/usr/bin/env node

/**
 * Deployment script for Admin Notification System
 * This script helps deploy the necessary Firebase components for the admin notification system
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('🚀 Admin Notification System Deployment Script');
console.log('================================================\n');

// Check if we're in the right directory
const packageJsonPath = path.join(process.cwd(), 'package.json');
if (!fs.existsSync(packageJsonPath)) {
  console.error('❌ Error: package.json not found. Please run this script from the project root.');
  process.exit(1);
}

// Check if Firebase CLI is installed
try {
  execSync('firebase --version', { stdio: 'ignore' });
  console.log('✅ Firebase CLI is installed');
} catch (error) {
  console.error('❌ Error: Firebase CLI not found. Please install it with: npm install -g firebase-tools');
  process.exit(1);
}

// Check if user is logged in to Firebase
try {
  execSync('firebase projects:list', { stdio: 'ignore' });
  console.log('✅ Firebase authentication verified');
} catch (error) {
  console.error('❌ Error: Not logged in to Firebase. Please run: firebase login');
  process.exit(1);
}

// Function to run command with output
function runCommand(command, description) {
  console.log(`\n📦 ${description}...`);
  console.log(`Running: ${command}`);
  
  try {
    const output = execSync(command, { 
      stdio: 'inherit',
      cwd: process.cwd()
    });
    console.log(`✅ ${description} completed successfully`);
    return true;
  } catch (error) {
    console.error(`❌ ${description} failed:`, error.message);
    return false;
  }
}

// Main deployment function
async function deployAdminNotifications() {
  console.log('Starting deployment of Admin Notification System components...\n');

  let success = true;

  // Step 1: Build and deploy Firebase Functions
  console.log('🔧 Step 1: Firebase Functions');
  if (!runCommand('cd functions && npm install', 'Installing Functions dependencies')) {
    success = false;
  }
  
  if (!runCommand('cd functions && npm run build', 'Building Functions')) {
    success = false;
  }
  
  if (!runCommand('firebase deploy --only functions', 'Deploying Functions')) {
    success = false;
  }

  // Step 2: Deploy Firestore Rules
  console.log('\n🔒 Step 2: Firestore Security Rules');
  if (!runCommand('firebase deploy --only firestore:rules', 'Deploying Firestore Rules')) {
    success = false;
  }

  // Step 3: Verify deployment
  console.log('\n🔍 Step 3: Verification');
  console.log('Checking deployed functions...');
  
  try {
    execSync('firebase functions:list', { stdio: 'inherit' });
    console.log('✅ Functions deployment verified');
  } catch (error) {
    console.error('⚠️ Warning: Could not verify functions deployment');
    success = false;
  }

  // Summary
  console.log('\n' + '='.repeat(50));
  if (success) {
    console.log('🎉 Admin Notification System deployed successfully!');
    console.log('\nNext steps:');
    console.log('1. Refresh your admin dashboard');
    console.log('2. Check the bell icon in the admin header');
    console.log('3. Visit /admin/notifications to see the full page');
    console.log('4. Test with /admin/notification-test (development only)');
    
    console.log('\n📋 Deployed Components:');
    console.log('• Firebase Functions with notification triggers');
    console.log('• Firestore security rules for admin_notifications collection');
    console.log('• Real-time notification system');
    console.log('• Admin notification management interface');
  } else {
    console.log('❌ Deployment completed with errors');
    console.log('\nPlease check the error messages above and try again.');
    console.log('You can also deploy components manually:');
    console.log('• Functions: firebase deploy --only functions');
    console.log('• Rules: firebase deploy --only firestore:rules');
  }
  
  console.log('\n📚 Documentation: docs/admin-notification-system.md');
  console.log('🚀 Deployment Guide: ADMIN_NOTIFICATION_DEPLOYMENT.md');
}

// Run the deployment
deployAdminNotifications().catch((error) => {
  console.error('❌ Deployment script failed:', error);
  process.exit(1);
});

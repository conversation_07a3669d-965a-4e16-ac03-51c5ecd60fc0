import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { Mail, Lock, User, Eye, EyeOff, Calendar, Phone, GraduationCap, CheckCircle, Gift } from 'lucide-react';
import { signUpWithEmail } from '../firebase/auth';
import { UniversityService, University } from '../services/universityService';
import { httpsCallable } from 'firebase/functions';
import { functions } from '../firebase/config';

interface SignupProps {
  onLogin?: () => void;
}

const Signup: React.FC<SignupProps> = ({ onLogin: _onLogin }) => {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    university: '',
    graduationYear: '',
    dateOfBirth: '',
    phone: '',
    password: '',
    confirmPassword: '',
    referralCode: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [verificationSent, setVerificationSent] = useState(false);

  // Referral code validation
  const [referralValidation, setReferralValidation] = useState<{
    isValid: boolean;
    message: string;
    referrerName?: string;
    bonusAmount?: number;
  } | null>(null);
  const [isValidatingReferral, setIsValidatingReferral] = useState(false);

  // Real universities data from Firebase
  const [universities, setUniversities] = useState<University[]>([]);
  const [universitiesLoading, setUniversitiesLoading] = useState(true);

  // Load universities from real data
  useEffect(() => {
    const loadUniversities = async () => {
      try {
        setUniversitiesLoading(true);
        const data = await UniversityService.getUniversities();
        // Filter only active universities and sort by name
        const activeUniversities = data
          .filter(uni => uni.isActive)
          .sort((a, b) => a.name.localeCompare(b.name));
        setUniversities(activeUniversities);
      } catch (err) {
        console.error('Error loading universities:', err);
        // Fallback to a comprehensive set if loading fails
        setUniversities([
          {
            id: 'harvard',
            name: 'Harvard University',
            domain: 'harvard.edu',
            shortName: 'Harvard',
            isActive: true,
            userCount: 0,
            listingCount: 0,
            allowedEmailDomains: ['harvard.edu'],
            requiresVerification: true,
            createdAt: new Date() as any,
            settings: {
              allowPublicListings: true,
              allowCrossUniversityChat: false,
              moderationLevel: 'medium' as const
            }
          },
          {
            id: 'stanford',
            name: 'Stanford University',
            domain: 'stanford.edu',
            shortName: 'Stanford',
            isActive: true,
            userCount: 0,
            listingCount: 0,
            allowedEmailDomains: ['stanford.edu'],
            requiresVerification: true,
            createdAt: new Date() as any,
            settings: {
              allowPublicListings: true,
              allowCrossUniversityChat: false,
              moderationLevel: 'medium' as const
            }
          },
          {
            id: 'mit',
            name: 'Massachusetts Institute of Technology',
            domain: 'mit.edu',
            shortName: 'MIT',
            isActive: true,
            userCount: 0,
            listingCount: 0,
            allowedEmailDomains: ['mit.edu'],
            requiresVerification: true,
            createdAt: new Date() as any,
            settings: {
              allowPublicListings: true,
              allowCrossUniversityChat: false,
              moderationLevel: 'medium' as const
            }
          },
          {
            id: 'berkeley',
            name: 'University of California, Berkeley',
            domain: 'berkeley.edu',
            shortName: 'UC Berkeley',
            isActive: true,
            userCount: 0,
            listingCount: 0,
            allowedEmailDomains: ['berkeley.edu'],
            requiresVerification: true,
            createdAt: new Date() as any,
            settings: {
              allowPublicListings: true,
              allowCrossUniversityChat: false,
              moderationLevel: 'medium' as const
            }
          },
          {
            id: 'msstate',
            name: 'Mississippi State University',
            domain: 'msstate.edu',
            isActive: true,
            userCount: 0,
            listingCount: 0,
            allowedEmailDomains: ['msstate.edu'],
            requiresVerification: true,
            createdAt: new Date() as any
          }
        ]);
      } finally {
        setUniversitiesLoading(false);
      }
    };

    loadUniversities();
  }, []);

  // Validate referral code
  const validateReferralCode = async (code: string) => {
    if (!code.trim()) {
      setReferralValidation(null);
      return;
    }

    setIsValidatingReferral(true);
    try {
      const validateReferral = httpsCallable(functions, 'validateReferralCode');
      const result = await validateReferral({ referralCode: code.trim() });
      const data = result.data as any;

      setReferralValidation({
        isValid: data.valid,
        message: data.message,
        referrerName: data.referrerName,
        bonusAmount: data.bonusAmount
      });
    } catch (error) {
      console.error('Error validating referral code:', error);
      setReferralValidation({
        isValid: false,
        message: 'Error validating referral code'
      });
    } finally {
      setIsValidatingReferral(false);
    }
  };

  // Handle referral code input with debouncing
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (formData.referralCode) {
        validateReferralCode(formData.referralCode);
      }
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [formData.referralCode]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      // Validation
      if (formData.password !== formData.confirmPassword) {
        throw new Error('Passwords do not match');
      }

      if (!formData.email.endsWith('.edu')) {
        throw new Error('Please use a valid university .edu email address');
      }

      // Validate email domain matches selected university
      const selectedUniversity = universities.find(uni => uni.name === formData.university);
      if (selectedUniversity) {
        const emailDomain = formData.email.split('@')[1];
        if (!selectedUniversity.allowedEmailDomains.includes(emailDomain)) {
          throw new Error(`Email domain must be one of: ${selectedUniversity.allowedEmailDomains.map(d => '@' + d).join(', ')} for ${selectedUniversity.name}`);
        }
      }

      // Calculate age
      const today = new Date();
      const birthDate = new Date(formData.dateOfBirth);
      let age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();

      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age = age - 1;
      }

      if (age < 18) {
        throw new Error('You must be at least 18 years old to create an account');
      }

      // Create user account (email verification is sent automatically)
      const fullName = `${formData.firstName} ${formData.lastName}`.trim();
      await signUpWithEmail(formData.email, formData.password, fullName);

      // Wait a moment for user creation to complete
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Save complete signup data to Firestore
      try {
        const saveSignupData = httpsCallable(functions, 'saveSignupData');
        await saveSignupData({
          firstName: formData.firstName,
          lastName: formData.lastName,
          university: formData.university,
          graduationYear: formData.graduationYear ? parseInt(formData.graduationYear) : null,
          dateOfBirth: formData.dateOfBirth,
          phone: formData.phone
        });
        console.log('Signup data saved successfully');
      } catch (signupDataError) {
        console.error('Error saving signup data:', signupDataError);
        // Don't fail signup if data saving fails - user can complete profile later
      }

      // Wait a moment for wallet initialization to complete
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Process referral code if provided and valid
      if (formData.referralCode && referralValidation?.isValid) {
        try {
          const processReferral = httpsCallable(functions, 'processReferralCode');
          await processReferral({ referralCode: formData.referralCode.trim() });
          console.log('Referral code processed successfully');
        } catch (referralError) {
          console.error('Error processing referral code:', referralError);
          // Don't fail signup if referral processing fails
        }
      }

      // Show verification message
      setVerificationSent(true);

    } catch (error: unknown) {
      console.error('Signup error:', error);
      setError(error instanceof Error ? error.message : 'Failed to create account');
    } finally {
      setIsLoading(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary-50 via-white to-accent-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 flex items-center justify-center p-2 sm:p-4 overflow-x-hidden">
      <div className="w-full max-w-2xl">
        {/* Logo */}
        <div className="text-center mb-8">
          <img
            src="/hive-campus-logo.svg"
            alt="Hive Campus Logo"
            className="w-16 h-16 mx-auto mb-4"
          />
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Join Hive Campus</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">Create your student marketplace account</p>
        </div>

        {/* Signup Form */}
        <div className="bg-white dark:bg-gray-800 rounded-3xl shadow-xl p-8 animate-scale-in">
          {verificationSent ? (
            /* Verification Sent State */
            <div className="text-center py-6">
              <div className="w-20 h-20 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-6">
                <CheckCircle className="w-10 h-10 text-green-600 dark:text-green-400" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">Verification Email Sent</h2>
              <p className="text-gray-600 dark:text-gray-400 mb-6">
                A verification email has been sent to <strong>{formData.email}</strong>. Please check your inbox and click the verification link to complete your registration and access Hive Campus.
              </p>
              <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mb-6">
                <p className="text-sm text-yellow-800 dark:text-yellow-200">
                  <strong>Important:</strong> You must verify your email before you can access Hive Campus. The verification link will redirect you back to the app once verified.
                </p>
              </div>
              <p className="text-sm text-gray-500 dark:text-gray-500 mb-6">
                Don't see the email? Check your spam folder or visit the{' '}
                <Link
                  to="/verify-email"
                  className="text-primary-600 hover:text-primary-700 underline"
                >
                  verification page
                </Link>
                {' '}to resend.
              </p>
              <Link
                to="/login"
                className="inline-block bg-primary-600 hover:bg-primary-700 text-white px-6 py-3 rounded-xl font-medium transition-colors"
              >
                Go to Sign In
              </Link>
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Error Message */}
              {error && (
                <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl p-4">
                  <p className="text-red-700 dark:text-red-300 text-sm">{error}</p>
                </div>
              )}
            {/* Name Fields */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">First Name</label>
                <div className="relative">
                  <User className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                  <input
                    type="text"
                    name="firstName"
                    value={formData.firstName}
                    onChange={handleChange}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all"
                    placeholder="John"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Last Name</label>
                <div className="relative">
                  <User className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                  <input
                    type="text"
                    name="lastName"
                    value={formData.lastName}
                    onChange={handleChange}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all"
                    placeholder="Doe"
                    required
                  />
                </div>
              </div>
            </div>

            {/* University and Graduation Year */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">University</label>
                <div className="relative">
                  <GraduationCap className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                  <select
                    name="university"
                    value={formData.university}
                    onChange={handleChange}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all appearance-none"
                    required
                  >
                    <option value="">
                      {universitiesLoading ? 'Loading universities...' : 'Select your university'}
                    </option>
                    {universities.map((university) => (
                      <option key={university.id} value={university.name}>
                        {university.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Expected Graduation Year</label>
                <div className="relative">
                  <Calendar className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                  <select
                    name="graduationYear"
                    value={formData.graduationYear}
                    onChange={handleChange}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all appearance-none"
                  >
                    <option value="">Select graduation year</option>
                    {Array.from({ length: 10 }, (_, i) => {
                      const year = new Date().getFullYear() + i;
                      return (
                        <option key={year} value={year}>
                          {year}
                        </option>
                      );
                    })}
                  </select>
                </div>
              </div>
            </div>

            {/* Email Input */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">University Email</label>
              <div className="relative">
                <Mail className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all"
                  placeholder="<EMAIL>"
                  pattern=".*\.edu$"
                  title="Please use your university .edu email"
                  required
                />
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400">Must be a valid .edu email address</p>
            </div>

            {/* Date of Birth and Phone */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Date of Birth</label>
                <div className="relative">
                  <Calendar className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                  <input
                    type="date"
                    name="dateOfBirth"
                    value={formData.dateOfBirth}
                    onChange={handleChange}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all"
                    required
                  />
                </div>
                <p className="text-xs text-gray-500 dark:text-gray-400">You must be 18 or older</p>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Phone Number (Optional)</label>
                <div className="relative">
                  <Phone className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                  <input
                    type="tel"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all"
                    placeholder="(*************"
                  />
                </div>
              </div>
            </div>

            {/* Password Fields */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Password</label>
                <div className="relative">
                  <Lock className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                  <input
                    type={showPassword ? 'text' : 'password'}
                    name="password"
                    value={formData.password}
                    onChange={handleChange}
                    className="w-full pl-10 pr-12 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all"
                    placeholder="Create a strong password"
                    minLength={8}
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
                  >
                    {showPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                  </button>
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Confirm Password</label>
                <div className="relative">
                  <Lock className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                  <input
                    type={showConfirmPassword ? 'text' : 'password'}
                    name="confirmPassword"
                    value={formData.confirmPassword}
                    onChange={handleChange}
                    className="w-full pl-10 pr-12 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all"
                    placeholder="Confirm your password"
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute right-3 top-3 text-gray-400 hover:text-gray-600"
                  >
                    {showConfirmPassword ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                  </button>
                </div>
              </div>
            </div>

            {/* Referral Code (Optional) */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Referral Code (Optional)
              </label>
              <div className="relative">
                <Gift className="absolute left-3 top-3 w-5 h-5 text-gray-400" />
                <input
                  type="text"
                  name="referralCode"
                  value={formData.referralCode}
                  onChange={handleChange}
                  className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-primary-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all"
                  placeholder="Enter referral code to earn $5 bonus"
                />
                {isValidatingReferral && (
                  <div className="absolute right-3 top-3">
                    <div className="w-5 h-5 border-2 border-gray-300 border-t-primary-500 rounded-full animate-spin"></div>
                  </div>
                )}
              </div>

              {/* Referral Validation Message */}
              {referralValidation && (
                <div className={`text-sm p-2 rounded-lg ${
                  referralValidation.isValid
                    ? 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-800'
                    : 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border border-red-200 dark:border-red-800'
                }`}>
                  {referralValidation.isValid && referralValidation.referrerName && (
                    <div className="flex items-center space-x-1">
                      <CheckCircle className="w-4 h-4" />
                      <span>
                        Valid! You and {referralValidation.referrerName} will each earn ${referralValidation.bonusAmount} 🎉
                      </span>
                    </div>
                  )}
                  {!referralValidation.isValid && (
                    <span>{referralValidation.message}</span>
                  )}
                </div>
              )}
            </div>

            {/* Password Requirements */}
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-4">
              <h4 className="text-sm font-medium text-blue-900 dark:text-blue-200 mb-2">Password Requirements:</h4>
              <ul className="text-xs text-blue-800 dark:text-blue-300 space-y-1">
                <li>• At least 8 characters long</li>
                <li>• Include uppercase and lowercase letters</li>
                <li>• Include at least one number</li>
                <li>• Include at least one special character</li>
              </ul>
            </div>

            {/* Submit Button */}
            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-gradient-to-r from-primary-600 to-primary-700 text-white py-3 rounded-xl font-semibold hover:from-primary-700 hover:to-primary-800 transform hover:scale-[1.02] transition-all duration-200 shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
            >
              {isLoading ? (
                <div className="flex items-center justify-center space-x-2">
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  <span>Creating Account...</span>
                </div>
              ) : (
                'Create Account'
              )}
            </button>

            {/* Terms */}
            <p className="text-xs text-gray-500 dark:text-gray-400 text-center">
              By creating an account, you agree to our{' '}
              <Link to="/terms" className="text-primary-600 dark:text-primary-400 hover:underline">
                Terms of Service
              </Link>{' '}
              and{' '}
              <Link to="/privacy" className="text-primary-600 dark:text-primary-400 hover:underline">
                Privacy Policy
              </Link>
            </p>
          </form>
          )}

          {/* Sign In Link */}
          <div className="mt-6 text-center">
            <p className="text-gray-600 dark:text-gray-400">
              Already have an account?{' '}
              <Link to="/login" className="text-primary-600 dark:text-primary-400 font-semibold hover:underline">
                Sign in
              </Link>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Signup;
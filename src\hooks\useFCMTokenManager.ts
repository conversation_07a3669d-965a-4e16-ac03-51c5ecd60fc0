import { useState, useEffect, useCallback } from 'react';
import { getToken, onMessage, MessagePayload, isSupported } from 'firebase/messaging';
import { doc, setDoc, getDoc, serverTimestamp, onSnapshot } from 'firebase/firestore';
import { messaging, firestore } from '../firebase/config';
import { FCMTokenData } from '../types/notifications';
import { useAuth } from './useAuth';
import toast from 'react-hot-toast';

interface FCMTokenManagerState {
  token: string | null;
  isSupported: boolean;
  isLoading: boolean;
  error: string | null;
  permission: NotificationPermission;
  isInitialized: boolean;
  retryCount: number;
}

interface FCMTokenManagerActions {
  requestPermission: () => Promise<boolean>;
  refreshToken: () => Promise<string | null>;
  clearToken: () => Promise<void>;
  onForegroundMessage: (callback: (payload: MessagePayload) => void) => (() => void) | null;
  retryPermission: () => Promise<boolean>;
  checkSupport: () => Promise<boolean>;
}

export const useFCM = (): FCMTokenManagerState & FCMTokenManagerActions => {
  const { user } = useAuth();
  const [state, setState] = useState<FCMTokenManagerState>({
    token: null,
    isSupported: false,
    isLoading: true,
    error: null,
    permission: typeof window !== 'undefined' ? Notification.permission : 'default',
    isInitialized: false,
    retryCount: 0
  });

  // Check FCM support and initialize
  const checkSupport = useCallback(async (): Promise<boolean> => {
    try {
      // Check if we're in a browser environment
      if (typeof window === 'undefined') {
        return false;
      }

      // Check for required APIs
      const hasRequiredAPIs = !!(
        'serviceWorker' in navigator &&
        'PushManager' in window &&
        'Notification' in window
      );

      if (!hasRequiredAPIs) {
        console.log('FCM: Required APIs not available');
        return false;
      }

      // Check if Firebase Messaging is supported
      const messagingSupported = await isSupported();
      if (!messagingSupported) {
        console.log('FCM: Firebase Messaging not supported');
        return false;
      }

      // Check if messaging instance is available
      if (!messaging) {
        console.log('FCM: Messaging instance not available');
        return false;
      }

      console.log('FCM: All checks passed, FCM is supported');
      return true;
    } catch (error) {
      console.error('FCM: Error checking support:', error);
      return false;
    }
  }, []);

  // Initialize FCM when user is available
  useEffect(() => {
    const initializeFCM = async () => {
      if (!user) {
        setState(prev => ({
          ...prev,
          isLoading: false,
          isInitialized: false,
          token: null
        }));
        return;
      }

      setState(prev => ({ ...prev, isLoading: true, error: null }));

      try {
        const supported = await checkSupport();

        setState(prev => ({
          ...prev,
          isSupported: supported,
          permission: Notification.permission,
          isLoading: false,
          isInitialized: true
        }));

        if (supported) {
          // Try to load existing token
          await loadExistingToken();

          // Set up token refresh listener
          setupTokenRefreshListener();
        }
      } catch (error) {
        console.error('FCM: Error during initialization:', error);
        setState(prev => ({
          ...prev,
          isSupported: false,
          isLoading: false,
          isInitialized: true,
          error: error instanceof Error ? error.message : 'Initialization failed'
        }));
      }
    };

    initializeFCM();
  }, [user, checkSupport]);

  // Load existing token from Firestore
  const loadExistingToken = useCallback(async () => {
    if (!user || !messaging) return;

    try {
      const tokenDoc = await getDoc(doc(firestore, `users/${user.uid}`));
      if (tokenDoc.exists()) {
        const userData = tokenDoc.data();
        const fcmToken = userData?.fcmToken;
        if (fcmToken && typeof fcmToken === 'string') {
          setState(prev => ({ ...prev, token: fcmToken }));
          console.log('FCM: Loaded existing token from Firestore');
        }
      }
    } catch (error) {
      console.error('FCM: Error loading existing token:', error);
    }
  }, [user]);

  // Set up token refresh listener
  const setupTokenRefreshListener = useCallback(() => {
    if (!user) return;

    // Listen for changes to the user's FCM token in Firestore
    const unsubscribe = onSnapshot(
      doc(firestore, `users/${user.uid}`),
      (doc) => {
        if (doc.exists()) {
          const userData = doc.data();
          const fcmToken = userData?.fcmToken;
          if (fcmToken && typeof fcmToken === 'string') {
            setState(prev => {
              if (prev.token !== fcmToken) {
                console.log('FCM: Token updated from Firestore');
                return { ...prev, token: fcmToken };
              }
              return prev;
            });
          }
        }
      },
      (error) => {
        console.error('FCM: Error listening to token changes:', error);
      }
    );

    return unsubscribe;
  }, [user]);

  // Request notification permission and get FCM token
  const requestPermission = useCallback(async (): Promise<boolean> => {
    if (!messaging || !user) {
      console.warn('FCM: Not available or user not authenticated');
      toast.error('Push notifications not available');
      return false;
    }

    if (!state.isSupported) {
      console.warn('FCM: Not supported on this device/browser');
      toast.error('Push notifications not supported on this device');
      return false;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      console.log('FCM: Requesting notification permission...');

      // Request notification permission
      const permission = await Notification.requestPermission();
      console.log('FCM: Permission result:', permission);

      setState(prev => ({ ...prev, permission }));

      if (permission !== 'granted') {
        const errorMsg = permission === 'denied'
          ? 'Notification permission denied. You can enable it in your browser settings.'
          : 'Notification permission not granted';

        setState(prev => ({
          ...prev,
          isLoading: false,
          error: errorMsg,
          retryCount: prev.retryCount + 1
        }));

        toast.error(errorMsg);
        return false;
      }

      console.log('FCM: Getting token with VAPID key...');

      // Get FCM token with VAPID key
      const vapidKey = import.meta.env.VITE_FIREBASE_VAPID_KEY;
      if (!vapidKey) {
        throw new Error('VAPID key not configured');
      }

      const token = await getToken(messaging, { vapidKey });
      console.log('FCM: Token received:', token ? 'Yes' : 'No');

      if (token) {
        // Save token to Firestore
        await saveTokenToFirestore(token);
        setState(prev => ({ ...prev, token, isLoading: false, error: null }));

        toast.success('Push notifications enabled!');
        console.log('FCM: Token saved successfully');
        return true;
      } else {
        const errorMsg = 'Failed to get FCM token. Please try again.';
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: errorMsg
        }));

        toast.error(errorMsg);
        return false;
      }
    } catch (error) {
      console.error('FCM: Error requesting permission:', error);
      const errorMsg = error instanceof Error ? error.message : 'Unknown error occurred';

      setState(prev => ({
        ...prev,
        isLoading: false,
        error: errorMsg,
        retryCount: prev.retryCount + 1
      }));

      toast.error(`Failed to enable push notifications: ${errorMsg}`);
      return false;
    }
  }, [messaging, user, state.isSupported]);

  // Save FCM token to Firestore
  const saveTokenToFirestore = useCallback(async (token: string) => {
    if (!user) return;

    try {
      // Save token directly to user document for easier access
      await setDoc(
        doc(firestore, `users/${user.uid}`),
        {
          fcmToken: token,
          fcmTokenUpdatedAt: serverTimestamp(),
          fcmTokenPlatform: 'web',
          fcmTokenUserAgent: navigator.userAgent
        },
        { merge: true }
      );

      console.log('FCM: Token saved to Firestore');
    } catch (error) {
      console.error('FCM: Error saving token to Firestore:', error);
      throw error;
    }
  }, [user]);

  // Refresh FCM token
  const refreshToken = useCallback(async (): Promise<string | null> => {
    if (!messaging || !user) return null;

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const token = await getToken(messaging, {
        vapidKey: import.meta.env.VITE_FIREBASE_VAPID_KEY
      });

      if (token) {
        await saveTokenToFirestore(token);
        setState(prev => ({ ...prev, token, isLoading: false }));
        return token;
      } else {
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: 'Failed to refresh FCM token'
        }));
        return null;
      }
    } catch (error) {
      console.error('Error refreshing FCM token:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }));
      return null;
    }
  }, [messaging, user]);

  // Retry permission request
  const retryPermission = useCallback(async (): Promise<boolean> => {
    if (state.retryCount >= 3) {
      toast.error('Too many retry attempts. Please enable notifications manually in your browser settings.');
      return false;
    }

    return requestPermission();
  }, [requestPermission, state.retryCount]);

  // Clear FCM token
  const clearToken = useCallback(async (): Promise<void> => {
    if (!user) return;

    try {
      // Remove token from user document
      await setDoc(
        doc(firestore, `users/${user.uid}`),
        {
          fcmToken: null,
          fcmTokenUpdatedAt: serverTimestamp()
        },
        { merge: true }
      );

      setState(prev => ({ ...prev, token: null }));
      toast.success('Push notifications disabled');
      console.log('FCM: Token cleared');
    } catch (error) {
      console.error('FCM: Error clearing token:', error);
      toast.error('Failed to disable push notifications');
    }
  }, [user]);

  // Listen for foreground messages
  const onForegroundMessage = useCallback((callback: (payload: MessagePayload) => void) => {
    if (!messaging) {
      console.warn('FCM: Messaging not available for foreground listener');
      return null;
    }

    try {
      console.log('FCM: Setting up foreground message listener');
      const unsubscribe = onMessage(messaging, (payload) => {
        console.log('FCM: Foreground message received:', payload);

        // Show in-app notification
        if (payload.notification) {
          toast.success(
            `${payload.notification.title}: ${payload.notification.body}`,
            {
              duration: 5000,
              icon: '🔔'
            }
          );
        }

        // Call the provided callback
        callback(payload);
      });

      return unsubscribe;
    } catch (error) {
      console.error('FCM: Error setting up foreground message listener:', error);
      return null;
    }
  }, [messaging]);

  return {
    ...state,
    requestPermission,
    refreshToken,
    clearToken,
    onForegroundMessage,
    retryPermission,
    checkSupport
  };
};

// Export both the new name and the old name for backward compatibility
export const useFCMTokenManager = useFCM;

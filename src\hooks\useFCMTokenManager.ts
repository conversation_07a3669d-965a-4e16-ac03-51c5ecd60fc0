import { useState, useEffect, useCallback } from 'react';
import { getToken, onMessage, MessagePayload } from 'firebase/messaging';
import { doc, setDoc, getDoc, serverTimestamp } from 'firebase/firestore';
import { messaging, firestore, auth } from '../firebase/config';
import { FCMTokenData } from '../types/notifications';
import { useAuth } from './useAuth';

interface FCMTokenManagerState {
  token: string | null;
  isSupported: boolean;
  isLoading: boolean;
  error: string | null;
  permission: NotificationPermission;
}

interface FCMTokenManagerActions {
  requestPermission: () => Promise<boolean>;
  refreshToken: () => Promise<string | null>;
  clearToken: () => Promise<void>;
  onForegroundMessage: (callback: (payload: MessagePayload) => void) => (() => void) | null;
}

export const useFCMTokenManager = (): FCMTokenManagerState & FCMTokenManagerActions => {
  const { user } = useAuth();
  const [state, setState] = useState<FCMTokenManagerState>({
    token: null,
    isSupported: false,
    isLoading: true,
    error: null,
    permission: 'default'
  });

  // Check if FCM is supported
  useEffect(() => {
    const checkSupport = async () => {
      try {
        const isSupported = !!(
          messaging &&
          'serviceWorker' in navigator &&
          'PushManager' in window &&
          'Notification' in window
        );

        setState(prev => ({
          ...prev,
          isSupported,
          permission: Notification.permission,
          isLoading: false
        }));

        if (isSupported && user) {
          // Try to get existing token
          await loadExistingToken();
        }
      } catch (error) {
        console.error('Error checking FCM support:', error);
        setState(prev => ({
          ...prev,
          isSupported: false,
          isLoading: false,
          error: 'FCM not supported'
        }));
      }
    };

    checkSupport();
  }, [user]);

  // Load existing token from Firestore
  const loadExistingToken = useCallback(async () => {
    if (!user || !messaging) return;

    try {
      const tokenDoc = await getDoc(doc(firestore, `users/${user.uid}/fcmTokens/web`));
      if (tokenDoc.exists()) {
        const tokenData = tokenDoc.data() as FCMTokenData;
        if (tokenData.active) {
          setState(prev => ({ ...prev, token: tokenData.token }));
        }
      }
    } catch (error) {
      console.error('Error loading existing FCM token:', error);
    }
  }, [user]);

  // Request notification permission and get FCM token
  const requestPermission = useCallback(async (): Promise<boolean> => {
    if (!messaging || !user) {
      console.warn('FCM not available or user not authenticated');
      return false;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      // Request notification permission
      const permission = await Notification.requestPermission();
      setState(prev => ({ ...prev, permission }));

      if (permission !== 'granted') {
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: 'Notification permission denied'
        }));
        return false;
      }

      // Get FCM token
      const token = await getToken(messaging, {
        vapidKey: import.meta.env.VITE_FIREBASE_VAPID_KEY
      });

      if (token) {
        // Save token to Firestore
        await saveTokenToFirestore(token);
        setState(prev => ({ ...prev, token, isLoading: false }));
        return true;
      } else {
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: 'Failed to get FCM token'
        }));
        return false;
      }
    } catch (error) {
      console.error('Error requesting FCM permission:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }));
      return false;
    }
  }, [messaging, user]);

  // Save FCM token to Firestore
  const saveTokenToFirestore = useCallback(async (token: string) => {
    if (!user) return;

    try {
      const tokenData: Omit<FCMTokenData, 'createdAt' | 'updatedAt'> = {
        token,
        platform: 'web',
        userAgent: navigator.userAgent,
        active: true
      };

      await setDoc(
        doc(firestore, `users/${user.uid}/fcmTokens/web`),
        {
          ...tokenData,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        },
        { merge: true }
      );

      console.log('FCM token saved to Firestore');
    } catch (error) {
      console.error('Error saving FCM token to Firestore:', error);
    }
  }, [user]);

  // Refresh FCM token
  const refreshToken = useCallback(async (): Promise<string | null> => {
    if (!messaging || !user) return null;

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const token = await getToken(messaging, {
        vapidKey: import.meta.env.VITE_FIREBASE_VAPID_KEY
      });

      if (token) {
        await saveTokenToFirestore(token);
        setState(prev => ({ ...prev, token, isLoading: false }));
        return token;
      } else {
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: 'Failed to refresh FCM token'
        }));
        return null;
      }
    } catch (error) {
      console.error('Error refreshing FCM token:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      }));
      return null;
    }
  }, [messaging, user]);

  // Clear FCM token
  const clearToken = useCallback(async (): Promise<void> => {
    if (!user) return;

    try {
      // Mark token as inactive in Firestore
      await setDoc(
        doc(firestore, `users/${user.uid}/fcmTokens/web`),
        {
          active: false,
          updatedAt: serverTimestamp()
        },
        { merge: true }
      );

      setState(prev => ({ ...prev, token: null }));
      console.log('FCM token cleared');
    } catch (error) {
      console.error('Error clearing FCM token:', error);
    }
  }, [user]);

  // Listen for foreground messages
  const onForegroundMessage = useCallback((callback: (payload: MessagePayload) => void) => {
    if (!messaging) return null;

    try {
      const unsubscribe = onMessage(messaging, callback);
      return unsubscribe;
    } catch (error) {
      console.error('Error setting up foreground message listener:', error);
      return null;
    }
  }, [messaging]);

  return {
    ...state,
    requestPermission,
    refreshToken,
    clearToken,
    onForegroundMessage
  };
};

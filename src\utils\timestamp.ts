import { Timestamp as _Timestamp } from 'firebase/firestore';

/**
 * Safely formats Firestore timestamps that may be serialized or native
 * @param timestamp - Firestore Timestamp, serialized timestamp, Date, or string
 * @param options - Formatting options
 * @returns Formatted date string
 */
export const formatTimestamp = (
  timestamp: any,
  options: Intl.DateTimeFormatOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }
): string => {
  if (!timestamp) return 'Recently';

  try {
    let date: Date;

    // If it's a Firestore Timestamp object with toDate method
    if (timestamp.toDate && typeof timestamp.toDate === 'function') {
      date = timestamp.toDate();
    }
    // If it's a serialized timestamp with seconds property (from Cloud Functions)
    else if (timestamp.seconds && typeof timestamp.seconds === 'number') {
      date = new Date(timestamp.seconds * 1000);
    }
    // If it's a serialized timestamp with _seconds property (Firebase client serialization)
    else if (timestamp._seconds && typeof timestamp._seconds === 'number') {
      date = new Date(timestamp._seconds * 1000);
    }
    // If it's already a Date object
    else if (timestamp instanceof Date) {
      date = timestamp;
    }
    // If it's a date string or number
    else {
      date = new Date(timestamp);
    }

    // Validate the date
    if (isNaN(date.getTime())) {
      console.warn('Invalid timestamp:', timestamp);
      return 'Recently';
    }

    return date.toLocaleDateString('en-US', options);
  } catch (error) {
    console.warn('Error formatting timestamp:', error, timestamp);
    return 'Recently';
  }
};

/**
 * Formats timestamp for relative time (e.g., "2 days ago")
 * @param timestamp - Firestore Timestamp, serialized timestamp, Date, or string
 * @returns Relative time string
 */
export const formatRelativeTime = (timestamp: any): string => {
  if (!timestamp) return 'Recently';

  try {
    let date: Date;

    if (timestamp.toDate && typeof timestamp.toDate === 'function') {
      date = timestamp.toDate();
    } else if (timestamp.seconds && typeof timestamp.seconds === 'number') {
      date = new Date(timestamp.seconds * 1000);
    } else if (timestamp._seconds && typeof timestamp._seconds === 'number') {
      date = new Date(timestamp._seconds * 1000);
    } else if (timestamp instanceof Date) {
      date = timestamp;
    } else {
      date = new Date(timestamp);
    }

    if (isNaN(date.getTime())) {
      return 'Recently';
    }

    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor(diffMs / (1000 * 60));

    if (diffDays > 7) {
      return formatTimestamp(timestamp);
    } else if (diffDays > 0) {
      return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    } else if (diffHours > 0) {
      return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    } else if (diffMinutes > 0) {
      return `${diffMinutes} minute${diffMinutes > 1 ? 's' : ''} ago`;
    } else {
      return 'Just now';
    }
  } catch (error) {
    console.warn('Error formatting relative time:', error, timestamp);
    return 'Recently';
  }
};

/**
 * Formats timestamp for full date and time display
 * @param timestamp - Firestore Timestamp, serialized timestamp, Date, or string
 * @returns Full date and time string
 */
export const formatFullDateTime = (timestamp: any): string => {
  return formatTimestamp(timestamp, {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

import { Timestamp } from 'firebase/firestore';

export interface AdminNotification {
  id: string;
  type: AdminNotificationType;
  title: string;
  message: string;
  icon: string; // emoji or Lucide icon key
  userId?: string; // User who triggered the action
  username?: string;
  orderId?: string;
  listingId?: string;
  amount?: number;
  metadata?: Record<string, any>; // Additional context data
  createdAt: Timestamp;
  read: boolean;
  actionUrl?: string; // Link to relevant admin page
}

export type AdminNotificationType = 
  | 'user_signup'
  | 'listing_created'
  | 'listing_sold'
  | 'listing_updated'
  | 'order_created'
  | 'payment_completed'
  | 'payment_failed'
  | 'feedback_submitted'
  | 'referral_bonus'
  | 'dispute_created'
  | 'wallet_used'
  | 'wallet_added'
  | 'shipping_label_created'
  | 'secret_code_verified'
  | 'escrow_released'
  | 'user_issue'
  | 'system_error';

export interface AdminNotificationCounts {
  total: number;
  unread: number;
  byType: Record<AdminNotificationType, number>;
}

export interface AdminNotificationFilters {
  type?: AdminNotificationType;
  read?: boolean;
  dateRange?: {
    start: Date;
    end: Date;
  };
  userId?: string;
}

// Notification configuration for different types
export const NOTIFICATION_CONFIG: Record<AdminNotificationType, {
  icon: string;
  color: string;
  priority: 'low' | 'medium' | 'high';
}> = {
  user_signup: { icon: '👋', color: 'blue', priority: 'low' },
  listing_created: { icon: '📦', color: 'green', priority: 'low' },
  listing_sold: { icon: '💰', color: 'green', priority: 'medium' },
  listing_updated: { icon: '✏️', color: 'yellow', priority: 'low' },
  order_created: { icon: '🛒', color: 'blue', priority: 'medium' },
  payment_completed: { icon: '✅', color: 'green', priority: 'medium' },
  payment_failed: { icon: '❌', color: 'red', priority: 'high' },
  feedback_submitted: { icon: '💬', color: 'blue', priority: 'low' },
  referral_bonus: { icon: '🎁', color: 'purple', priority: 'low' },
  dispute_created: { icon: '⚠️', color: 'red', priority: 'high' },
  wallet_used: { icon: '💳', color: 'blue', priority: 'low' },
  wallet_added: { icon: '💰', color: 'green', priority: 'low' },
  shipping_label_created: { icon: '📮', color: 'blue', priority: 'medium' },
  secret_code_verified: { icon: '🔓', color: 'green', priority: 'medium' },
  escrow_released: { icon: '🏦', color: 'green', priority: 'medium' },
  user_issue: { icon: '🚨', color: 'red', priority: 'high' },
  system_error: { icon: '⚡', color: 'red', priority: 'high' }
};

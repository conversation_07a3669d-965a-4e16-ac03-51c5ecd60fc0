"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.testAdminNotifications = exports.createAdminNotification = void 0;
const functions = __importStar(require("firebase-functions/v1"));
const admin = __importStar(require("firebase-admin"));
// Simple admin notification creation function
exports.createAdminNotification = functions.https.onCall(async (data, context) => {
    // Check if user is admin (you can implement your admin check here)
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }
    try {
        const notification = {
            type: data.type || 'system',
            title: data.title || 'Test Notification',
            message: data.message || 'This is a test notification',
            icon: data.icon || '📢',
            userId: data.userId || null,
            username: data.username || null,
            read: false,
            createdAt: admin.firestore.FieldValue.serverTimestamp()
        };
        const docRef = await admin.firestore().collection('admin_notifications').add(notification);
        return { success: true, id: docRef.id };
    }
    catch (error) {
        console.error('Error creating admin notification:', error);
        throw new functions.https.HttpsError('internal', 'Failed to create notification');
    }
});
// Simple function to test the system
exports.testAdminNotifications = functions.https.onCall(async (data, context) => {
    if (!context.auth) {
        throw new functions.https.HttpsError('unauthenticated', 'User must be authenticated');
    }
    try {
        // Create a few test notifications
        const notifications = [
            {
                type: 'user_signup',
                title: 'New User Signup',
                message: 'Test User has joined the platform',
                icon: '👋',
                username: 'Test User',
                read: false,
                createdAt: admin.firestore.FieldValue.serverTimestamp()
            },
            {
                type: 'payment_completed',
                title: 'Payment Completed',
                message: 'Test payment of $50 completed successfully',
                icon: '✅',
                username: 'Test Buyer',
                amount: 50,
                read: false,
                createdAt: admin.firestore.FieldValue.serverTimestamp()
            },
            {
                type: 'listing_created',
                title: 'New Listing Created',
                message: 'Test Seller created a new listing: Test Item',
                icon: '📦',
                username: 'Test Seller',
                amount: 25,
                read: false,
                createdAt: admin.firestore.FieldValue.serverTimestamp()
            }
        ];
        const batch = admin.firestore().batch();
        const collection = admin.firestore().collection('admin_notifications');
        notifications.forEach((notification) => {
            const docRef = collection.doc();
            batch.set(docRef, notification);
        });
        await batch.commit();
        return { success: true, created: notifications.length };
    }
    catch (error) {
        console.error('Error creating test notifications:', error);
        throw new functions.https.HttpsError('internal', 'Failed to create test notifications');
    }
});

import { useState, useEffect, useCallback } from 'react';
import {
  collection,
  query,
  orderBy,
  limit,
  where,
  onSnapshot,
  doc,
  updateDoc,
  deleteDoc,
  writeBatch,
  Timestamp,
  QueryConstraint,
  getDocs
} from 'firebase/firestore';
import { firestore } from '../firebase/config';
import { useAuth } from './useAuth';
import { UserNotification, NotificationFilter, NotificationStats } from '../types/notifications';

interface UseUserNotificationsState {
  notifications: UserNotification[];
  unreadCount: number;
  isLoading: boolean;
  error: string | null;
  stats: NotificationStats | null;
}

interface UseUserNotificationsActions {
  markAsRead: (notificationId: string) => Promise<void>;
  markAllAsRead: () => Promise<void>;
  deleteNotification: (notificationId: string) => Promise<void>;
  deleteAllRead: () => Promise<void>;
  refreshNotifications: () => void;
  getNotificationsByFilter: (filter: NotificationFilter) => Promise<UserNotification[]>;
}

export const useUserNotifications = (
  initialLimit: number = 50
): UseUserNotificationsState & UseUserNotificationsActions => {
  const { user } = useAuth();
  const [state, setState] = useState<UseUserNotificationsState>({
    notifications: [],
    unreadCount: 0,
    isLoading: true,
    error: null,
    stats: null
  });

  // Real-time listener for notifications
  useEffect(() => {
    if (!user) {
      setState({
        notifications: [],
        unreadCount: 0,
        isLoading: false,
        error: null,
        stats: null
      });
      return;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    const notificationsRef = collection(firestore, `users/${user.uid}/notifications`);
    const q = query(
      notificationsRef,
      orderBy('createdAt', 'desc'),
      limit(initialLimit)
    );

    const unsubscribe = onSnapshot(
      q,
      (snapshot) => {
        try {
          const notifications: UserNotification[] = [];
          let unreadCount = 0;

          snapshot.forEach((doc) => {
            const data = doc.data();
            const notification: UserNotification = {
              id: doc.id,
              type: data.type,
              title: data.title,
              message: data.message,
              icon: data.icon,
              createdAt: data.createdAt,
              read: data.read || false,
              link: data.link,
              priority: data.priority || 'normal',
              actionRequired: data.actionRequired || false,
              expiresAt: data.expiresAt,
              metadata: data.metadata || {},
              orderId: data.orderId,
              listingId: data.listingId,
              chatId: data.chatId,
              senderId: data.senderId,
              senderName: data.senderName,
              amount: data.amount,
              secretCode: data.secretCode
            };

            notifications.push(notification);
            if (!notification.read) {
              unreadCount++;
            }
          });

          // Calculate stats
          const stats: NotificationStats = {
            total: notifications.length,
            unread: unreadCount,
            byType: {},
            byChannel: { in_app: notifications.length, push: 0, email: 0 },
            lastRead: notifications.find(n => n.read)?.createdAt
          };

          // Count by type
          notifications.forEach(notification => {
            stats.byType[notification.type] = (stats.byType[notification.type] || 0) + 1;
          });

          setState({
            notifications,
            unreadCount,
            isLoading: false,
            error: null,
            stats
          });
        } catch (error) {
          console.error('Error processing notifications:', error);
          setState(prev => ({
            ...prev,
            isLoading: false,
            error: error instanceof Error ? error.message : 'Unknown error'
          }));
        }
      },
      (error) => {
        console.error('Error listening to notifications:', error);
        setState(prev => ({
          ...prev,
          isLoading: false,
          error: error.message
        }));
      }
    );

    return () => unsubscribe();
  }, [user, initialLimit]);

  // Mark a single notification as read
  const markAsRead = useCallback(async (notificationId: string): Promise<void> => {
    if (!user) return;

    try {
      const notificationRef = doc(firestore, `users/${user.uid}/notifications/${notificationId}`);
      await updateDoc(notificationRef, {
        read: true,
        readAt: Timestamp.now()
      });
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw error;
    }
  }, [user]);

  // Mark all notifications as read
  const markAllAsRead = useCallback(async (): Promise<void> => {
    if (!user) return;

    try {
      const batch = writeBatch(firestore);
      const unreadNotifications = state.notifications.filter(n => !n.read);

      unreadNotifications.forEach(notification => {
        const notificationRef = doc(firestore, `users/${user.uid}/notifications/${notification.id}`);
        batch.update(notificationRef, {
          read: true,
          readAt: Timestamp.now()
        });
      });

      await batch.commit();
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      throw error;
    }
  }, [user, state.notifications]);

  // Delete a single notification
  const deleteNotification = useCallback(async (notificationId: string): Promise<void> => {
    if (!user) return;

    try {
      const notificationRef = doc(firestore, `users/${user.uid}/notifications/${notificationId}`);
      await deleteDoc(notificationRef);
    } catch (error) {
      console.error('Error deleting notification:', error);
      throw error;
    }
  }, [user]);

  // Delete all read notifications
  const deleteAllRead = useCallback(async (): Promise<void> => {
    if (!user) return;

    try {
      const batch = writeBatch(firestore);
      const readNotifications = state.notifications.filter(n => n.read);

      readNotifications.forEach(notification => {
        const notificationRef = doc(firestore, `users/${user.uid}/notifications/${notification.id}`);
        batch.delete(notificationRef);
      });

      await batch.commit();
    } catch (error) {
      console.error('Error deleting read notifications:', error);
      throw error;
    }
  }, [user, state.notifications]);

  // Refresh notifications (force re-fetch)
  const refreshNotifications = useCallback(() => {
    setState(prev => ({ ...prev, isLoading: true }));
    // The useEffect will handle the refresh automatically
  }, []);

  // Get notifications by filter
  const getNotificationsByFilter = useCallback(async (filter: NotificationFilter): Promise<UserNotification[]> => {
    if (!user) return [];

    try {
      const notificationsRef = collection(firestore, `users/${user.uid}/notifications`);
      const constraints: QueryConstraint[] = [orderBy('createdAt', 'desc')];

      // Apply filters
      if (filter.types && filter.types.length > 0) {
        constraints.push(where('type', 'in', filter.types));
      }

      if (filter.read !== undefined) {
        constraints.push(where('read', '==', filter.read));
      }

      if (filter.priority && filter.priority.length > 0) {
        constraints.push(where('priority', 'in', filter.priority));
      }

      if (filter.limit) {
        constraints.push(limit(filter.limit));
      }

      const q = query(notificationsRef, ...constraints);
      const snapshot = await getDocs(q);

      const notifications: UserNotification[] = [];
      snapshot.forEach((doc) => {
        const data = doc.data();
        notifications.push({
          id: doc.id,
          type: data.type,
          title: data.title,
          message: data.message,
          icon: data.icon,
          createdAt: data.createdAt,
          read: data.read || false,
          link: data.link,
          priority: data.priority || 'normal',
          actionRequired: data.actionRequired || false,
          expiresAt: data.expiresAt,
          metadata: data.metadata || {},
          orderId: data.orderId,
          listingId: data.listingId,
          chatId: data.chatId,
          senderId: data.senderId,
          senderName: data.senderName,
          amount: data.amount,
          secretCode: data.secretCode
        });
      });

      return notifications;
    } catch (error) {
      console.error('Error getting notifications by filter:', error);
      throw error;
    }
  }, [user]);

  return {
    ...state,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    deleteAllRead,
    refreshNotifications,
    getNotificationsByFilter
  };
};

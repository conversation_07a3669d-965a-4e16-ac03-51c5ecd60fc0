import React, { useState, useEffect } from 'react';
import {
  TrendingUp,
  Users,
  DollarSign,
  Award,
  AlertTriangle,
  RefreshCw
} from 'lucide-react';
import { collection, query, orderBy, limit, getDocs } from 'firebase/firestore';
import { db } from '../../../firebase/config';
import { useAuth } from '../../../hooks/useAuth';

interface DailyWalletReport {
  date: string;
  totalCreditsGranted: number;
  referralCreditsUsed: number;
  topReferrers: {
    userId: string;
    name: string;
    referralCount: number;
    totalEarned: number;
  }[];
  generatedAt: any;
}

interface WalletAnalytics {
  userId: string;
  totalCreditsEarned: number;
  totalCreditsUsed: number;
  referralCount: number;
  suspiciousActivity: boolean;
  activityScore: number;
}

const AdminWalletReports: React.FC = () => {
  const { isAdmin } = useAuth();
  const [reports, setReports] = useState<DailyWalletReport[]>([]);
  const [suspiciousUsers, setSuspiciousUsers] = useState<WalletAnalytics[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [_selectedDate, _setSelectedDate] = useState<string>('');

  // Load recent reports
  const loadReports = async () => {
    if (!isAdmin) {
      setError('Admin access required to view wallet reports');
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const reportsQuery = query(
        collection(db, 'dailyWalletReports'),
        orderBy('date', 'desc'),
        limit(30)
      );

      const reportsSnapshot = await getDocs(reportsQuery);

      if (reportsSnapshot.empty) {
        // No reports exist yet, show empty state
        setReports([]);
        setError(null);
        return;
      }

      const reportsData = reportsSnapshot.docs.map(doc => ({
        ...doc.data(),
        id: doc.id
      })) as DailyWalletReport[];

      setReports(reportsData);
    } catch (error: any) {
      console.error('Error loading wallet reports:', error);

      if (error?.code === 'permission-denied') {
        setError('Permission denied. Please ensure you have admin privileges and Firestore rules are properly configured.');
      } else {
        // Don't show error for missing collections, just show empty state
        setReports([]);
        setError(null);
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Load suspicious users
  const loadSuspiciousUsers = async () => {
    if (!isAdmin) {
      return;
    }

    try {
      const analyticsQuery = query(
        collection(db, 'walletAnalytics'),
        limit(100)
      );

      const analyticsSnapshot = await getDocs(analyticsQuery);

      if (analyticsSnapshot.empty) {
        // No analytics exist yet, show empty state
        setSuspiciousUsers([]);
        return;
      }

      const analyticsData = analyticsSnapshot.docs.map(doc => doc.data()) as WalletAnalytics[];

      // Filter suspicious users
      const suspicious = analyticsData.filter(user =>
        user.suspiciousActivity || user.activityScore < 30
      );

      setSuspiciousUsers(suspicious);
    } catch (error: any) {
      console.error('Error loading suspicious users:', error);

      if (error?.code === 'permission-denied') {
        console.warn('Permission denied for wallet analytics - this is expected if the collection does not exist yet');
      }

      // Don't show error for missing collections, just show empty state
      setSuspiciousUsers([]);
    }
  };

  useEffect(() => {
    if (isAdmin) {
      loadReports();
      loadSuspiciousUsers();
    } else {
      setError('Admin access required to view wallet reports');
      setIsLoading(false);
    }
  }, [isAdmin]);

  const formatCurrency = (amount: number) => `$${amount.toFixed(2)}`;
  const formatDate = (dateString: string) => new Date(dateString).toLocaleDateString();

  const getTodaysReport = () => {
    const today = new Date().toISOString().split('T')[0];
    return reports.find(report => report.date === today);
  };

  const getYesterdaysReport = () => {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 1);
    const yesterdayStr = yesterday.toISOString().split('T')[0];
    return reports.find(report => report.date === yesterdayStr);
  };

  // Show error message if there's an error
  if (error) {
    return (
      <div className="space-y-6">
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex">
            <AlertTriangle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                Access Error
              </h3>
              <p className="mt-1 text-sm text-red-700 dark:text-red-300">
                {error}
              </p>
              {error.includes('permission') && (
                <div className="mt-3 text-sm text-red-600 dark:text-red-400">
                  <p>To fix this issue:</p>
                  <ol className="list-decimal list-inside mt-1 space-y-1">
                    <li>Ensure you have admin privileges</li>
                    <li>Verify Firestore rules have been deployed</li>
                    <li>Refresh the page</li>
                  </ol>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-3 text-gray-600 dark:text-gray-400">Loading wallet reports...</span>
      </div>
    );
  }

  const todaysReport = getTodaysReport();
  const _yesterdaysReport = getYesterdaysReport();

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
              <TrendingUp className="w-6 h-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                Wallet Reports & Analytics
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                Daily wallet activity, referral trends, and suspicious activity monitoring
              </p>
            </div>
          </div>
          <button
            onClick={() => { loadReports(); loadSuspiciousUsers(); }}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            <RefreshCw className="w-4 h-4" />
            <span>Refresh</span>
          </button>
        </div>
      </div>

      {/* Today's Summary */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-green-100 dark:bg-green-900/20 rounded-lg">
              <DollarSign className="w-5 h-5 text-green-600 dark:text-green-400" />
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Today's Credits Granted</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {formatCurrency(todaysReport?.totalCreditsGranted || 0)}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
              <Users className="w-5 h-5 text-purple-600 dark:text-purple-400" />
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Referral Credits Used</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {formatCurrency(todaysReport?.referralCreditsUsed || 0)}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-yellow-100 dark:bg-yellow-900/20 rounded-lg">
              <Award className="w-5 h-5 text-yellow-600 dark:text-yellow-400" />
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Top Referrers Today</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {todaysReport?.topReferrers?.length || 0}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-red-100 dark:bg-red-900/20 rounded-lg">
              <AlertTriangle className="w-5 h-5 text-red-600 dark:text-red-400" />
            </div>
            <div>
              <p className="text-sm text-gray-600 dark:text-gray-400">Suspicious Users</p>
              <p className="text-2xl font-bold text-gray-900 dark:text-white">
                {suspiciousUsers.length}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Top Referrers Today */}
      {todaysReport?.topReferrers && todaysReport.topReferrers.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            Today's Top Referrers
          </h2>
          <div className="space-y-3">
            {todaysReport.topReferrers.map((referrer, index) => (
              <div key={referrer.userId} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/20 rounded-full flex items-center justify-center">
                    <span className="text-sm font-bold text-blue-600 dark:text-blue-400">
                      #{index + 1}
                    </span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900 dark:text-white">
                      {referrer.name}
                    </p>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {referrer.referralCount} referrals
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-gray-900 dark:text-white">
                    {formatCurrency(referrer.totalEarned)}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">earned</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Suspicious Activity */}
      {suspiciousUsers.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 flex items-center space-x-2">
            <AlertTriangle className="w-5 h-5 text-red-500" />
            <span>Suspicious Wallet Activity</span>
          </h2>
          <div className="space-y-3">
            {suspiciousUsers.slice(0, 10).map((user) => (
              <div key={user.userId} className="flex items-center justify-between p-3 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800">
                <div>
                  <p className="font-medium text-gray-900 dark:text-white">
                    User ID: {user.userId.substring(0, 8)}...
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {user.referralCount} referrals • Score: {user.activityScore}
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-sm text-gray-900 dark:text-white">
                    Earned: {formatCurrency(user.totalCreditsEarned)}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Used: {formatCurrency(user.totalCreditsUsed)}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Recent Reports */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          Recent Daily Reports
        </h2>
        <div className="overflow-x-auto">
          <table className="w-full text-sm">
            <thead>
              <tr className="border-b border-gray-200 dark:border-gray-700">
                <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">Date</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">Credits Granted</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">Referral Credits</th>
                <th className="text-left py-3 px-4 font-medium text-gray-900 dark:text-white">Top Referrers</th>
              </tr>
            </thead>
            <tbody>
              {reports.slice(0, 10).map((report) => (
                <tr key={report.date} className="border-b border-gray-100 dark:border-gray-700">
                  <td className="py-3 px-4 text-gray-900 dark:text-white">
                    {formatDate(report.date)}
                  </td>
                  <td className="py-3 px-4 text-gray-900 dark:text-white">
                    {formatCurrency(report.totalCreditsGranted)}
                  </td>
                  <td className="py-3 px-4 text-gray-900 dark:text-white">
                    {formatCurrency(report.referralCreditsUsed)}
                  </td>
                  <td className="py-3 px-4 text-gray-900 dark:text-white">
                    {report.topReferrers?.length || 0}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default AdminWalletReports;

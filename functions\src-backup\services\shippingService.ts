import * as _functions from 'firebase-functions';
import * as admin from 'firebase-admin';
import { db } from '../config/firebase';

const SHIPPO_API_KEY = process.env.SHIPPO_API_KEY;
const SHIPPO_BASE_URL = 'https://api.goshippo.com';

interface ShippingAddress {
  name: string;
  street1: string;
  street2?: string;
  city: string;
  state: string;
  zip: string;
  country: string;
  phone?: string;
  email?: string;
}

interface PackageInfo {
  weight: string; // 'small', 'medium', 'large' or custom weight value
  weightUnit?: 'oz' | 'lb'; // Unit for custom weight
  length?: number;
  width?: number;
  height?: number;
  dimensionUnit?: 'in' | 'cm'; // Unit for dimensions
  presetUsed?: string; // Track which preset was used
}

interface ShippingRate {
  carrier: string;
  service: string;
  amount: string;
  currency: string;
  estimated_days: number;
  rate_id: string;
}

interface ShippingLabel {
  label_url: string;
  tracking_number: string;
  tracking_url: string;
  rate_id: string;
  carrier: string;
  service: string;
  cost: string;
}

// Weight mappings for package sizes
const PACKAGE_WEIGHTS = {
  small: { weight: 4, unit: 'oz' }, // 0-4 oz
  medium: { weight: 1, unit: 'lb' }, // ~1 lb
  large: { weight: 3, unit: 'lb' } // 3+ lb
};

// Package dimensions (in inches)
const PACKAGE_DIMENSIONS = {
  small: { length: 6, width: 4, height: 2 },
  medium: { length: 10, width: 8, height: 4 },
  large: { length: 12, width: 10, height: 6 }
};

// Package presets with combined info
export const PACKAGE_PRESETS = {
  'Small Box': {
    dimensions: { length: 9, width: 6, height: 2, unit: 'in' },
    weight: { value: 10, unit: 'oz' }
  },
  'Medium Box': {
    dimensions: { length: 12, width: 10, height: 4, unit: 'in' },
    weight: { value: 2, unit: 'lb' }
  },
  'Poly Mailer': {
    dimensions: { length: 10, width: 13, height: 1, unit: 'in' },
    weight: { value: 8, unit: 'oz' }
  }
};

/**
 * Get shipping rates from Shippo
 */
// Helper function to convert weight to ounces
const convertWeightToOunces = (value: number, unit: 'oz' | 'lb'): number => {
  return unit === 'lb' ? value * 16 : value;
};

// Helper function to convert dimensions to inches
const convertDimensionsToInches = (value: number, unit: 'in' | 'cm'): number => {
  return unit === 'cm' ? value / 2.54 : value;
};

export const getShippingRates = async (
  fromAddress: ShippingAddress,
  toAddress: ShippingAddress,
  packageInfo: PackageInfo
): Promise<ShippingRate[]> => {
  try {
    let weight: number;
    let weightUnit: string;
    let length: number;
    let width: number;
    let height: number;

    // Handle custom package dimensions and weight
    if (packageInfo.length && packageInfo.width && packageInfo.height) {
      // Custom dimensions provided
      const dimUnit = packageInfo.dimensionUnit || 'in';
      length = convertDimensionsToInches(packageInfo.length, dimUnit);
      width = convertDimensionsToInches(packageInfo.width, dimUnit);
      height = convertDimensionsToInches(packageInfo.height, dimUnit);
    } else {
      // Use preset dimensions
      const packageDimensions = PACKAGE_DIMENSIONS[packageInfo.weight as keyof typeof PACKAGE_DIMENSIONS];
      if (!packageDimensions) {
        throw new Error(`Invalid package size: ${packageInfo.weight}`);
      }
      length = packageDimensions.length;
      width = packageDimensions.width;
      height = packageDimensions.height;
    }

    // Handle custom weight
    if (packageInfo.weightUnit && !isNaN(Number(packageInfo.weight))) {
      // Custom weight provided
      const weightValue = Number(packageInfo.weight);
      weight = convertWeightToOunces(weightValue, packageInfo.weightUnit);
      weightUnit = 'oz';
    } else {
      // Use preset weight
      const packageWeight = PACKAGE_WEIGHTS[packageInfo.weight as keyof typeof PACKAGE_WEIGHTS];
      if (!packageWeight) {
        throw new Error(`Invalid package size: ${packageInfo.weight}`);
      }
      weight = packageWeight.unit === 'lb' ? packageWeight.weight * 16 : packageWeight.weight;
      weightUnit = 'oz';
    }

    const shipmentData = {
      address_from: fromAddress,
      address_to: toAddress,
      parcels: [{
        length: length,
        width: width,
        height: height,
        distance_unit: 'in',
        weight: weight,
        mass_unit: weightUnit
      }],
      async: false
    };

    const response = await fetch(`${SHIPPO_BASE_URL}/shipments/`, {
      method: 'POST',
      headers: {
        'Authorization': `ShippoToken ${SHIPPO_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(shipmentData)
    });

    if (!response.ok) {
      throw new Error(`Shippo API error: ${response.status}`);
    }

    const shipment = await response.json();
    
    if (shipment.status === 'ERROR') {
      throw new Error(`Shipment creation failed: ${shipment.messages?.[0]?.text || 'Unknown error'}`);
    }

    // Filter and format rates
    const rates: ShippingRate[] = shipment.rates
      .filter((rate: any) => rate.amount && parseFloat(rate.amount) > 0)
      .map((rate: any) => ({
        carrier: rate.provider,
        service: rate.servicelevel.name,
        amount: rate.amount,
        currency: rate.currency,
        estimated_days: rate.estimated_days || 3,
        rate_id: rate.object_id
      }))
      .sort((a: ShippingRate, b: ShippingRate) => parseFloat(a.amount) - parseFloat(b.amount));

    return rates;
  } catch (error) {
    console.error('Error getting shipping rates:', error);
    throw error;
  }
};

/**
 * Create shipping label
 */
export const createShippingLabel = async (
  rateId: string,
  orderId: string
): Promise<ShippingLabel> => {
  try {
    const transactionData = {
      rate: rateId,
      label_file_type: 'PDF',
      async: false
    };

    const response = await fetch(`${SHIPPO_BASE_URL}/transactions/`, {
      method: 'POST',
      headers: {
        'Authorization': `ShippoToken ${SHIPPO_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(transactionData)
    });

    if (!response.ok) {
      throw new Error(`Shippo transaction error: ${response.status}`);
    }

    const transaction = await response.json();

    if (transaction.status === 'ERROR') {
      throw new Error(`Label creation failed: ${transaction.messages?.[0]?.text || 'Unknown error'}`);
    }

    const label: ShippingLabel = {
      label_url: transaction.label_url,
      tracking_number: transaction.tracking_number,
      tracking_url: transaction.tracking_url_provider,
      rate_id: rateId,
      carrier: transaction.rate.provider,
      service: transaction.rate.servicelevel.name,
      cost: transaction.rate.amount
    };

    // Store label info in Firestore
    await db.collection('orders').doc(orderId).update({
      'shipping.labelUrl': label.label_url,
      'shipping.trackingNumber': label.tracking_number,
      'shipping.trackingUrl': label.tracking_url,
      'shipping.carrier': label.carrier,
      'shipping.service': label.service,
      'shipping.labelCreatedAt': admin.firestore.Timestamp.now(),
      'status': 'label_created'
    });

    return label;
  } catch (error) {
    console.error('Error creating shipping label:', error);
    throw error;
  }
};

/**
 * Get tracking information
 */
export const getTrackingInfo = async (trackingNumber: string, carrier: string) => {
  try {
    const response = await fetch(`${SHIPPO_BASE_URL}/tracks/${carrier}/${trackingNumber}/`, {
      headers: {
        'Authorization': `ShippoToken ${SHIPPO_API_KEY}`
      }
    });

    if (!response.ok) {
      throw new Error(`Tracking API error: ${response.status}`);
    }

    const tracking = await response.json();
    return tracking;
  } catch (error) {
    console.error('Error getting tracking info:', error);
    throw error;
  }
};

/**
 * Estimate shipping cost based on package size
 */
export const estimateShippingCost = (packageSize: string): { min: number; max: number } => {
  const estimates = {
    small: { min: 4.50, max: 6.00 },
    medium: { min: 6.50, max: 8.50 },
    large: { min: 8.00, max: 12.00 }
  };

  return estimates[packageSize as keyof typeof estimates] || estimates.medium;
};

/**
 * Validate shipping address
 */
export const validateAddress = async (address: ShippingAddress): Promise<boolean> => {
  try {
    const response = await fetch(`${SHIPPO_BASE_URL}/addresses/`, {
      method: 'POST',
      headers: {
        'Authorization': `ShippoToken ${SHIPPO_API_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        ...address,
        validate: true
      })
    });

    if (!response.ok) {
      return false;
    }

    const result = await response.json();
    return result.validation_results?.is_valid || false;
  } catch (error) {
    console.error('Error validating address:', error);
    return false;
  }
};
